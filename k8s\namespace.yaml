# Consolidated Kubernetes Namespace
# Single authoritative namespace definition

apiVersion: v1
kind: Namespace
metadata:
  name: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform
    app.kubernetes.io/version: "v1"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: quant-platform
    app.kubernetes.io/managed-by: kubectl
    environment: production
    project: quantitative-trading
    team: quant-ops
  annotations:
    description: "Quantitative Trading Platform - Production Environment"
    contact: "<EMAIL>"
---
# Resource Quota for the namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: quant-platform-quota
  namespace: quant-platform
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "20"
    pods: "50"
    services: "20"
    secrets: "10"
    configmaps: "10"
---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: quant-platform-netpol
  namespace: quant-platform
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: quant-platform
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: quant-platform
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS