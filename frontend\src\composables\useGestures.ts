/**
 * 手势支持组合式函数
 * 提供滑动、缩放、长按等手势识别功能
 */

import { ref, onMounted, onUnmounted, type Ref } from 'vue'

// 手势类型
export type GestureType = 'swipe' | 'pinch' | 'tap' | 'longpress' | 'pan'

// 手势方向
export type SwipeDirection = 'up' | 'down' | 'left' | 'right'

// 手势事件接口
export interface GestureEvent {
  type: GestureType
  direction?: SwipeDirection
  distance?: number
  scale?: number
  deltaX?: number
  deltaY?: number
  startX?: number
  startY?: number
  endX?: number
  endY?: number
  duration?: number
}

// 手势配置接口
export interface GestureOptions {
  // 滑动相关
  onSwipeLeft?: (event: GestureEvent) => void
  onSwipeRight?: (event: GestureEvent) => void
  onSwipeUp?: (event: GestureEvent) => void
  onSwipeDown?: (event: GestureEvent) => void
  swipeThreshold?: number // 滑动阈值，默认50px
  swipeVelocity?: number // 滑动速度阈值，默认0.3px/ms

  // 缩放相关
  onPinch?: (scale: number, event: GestureEvent) => void
  onPinchStart?: (event: GestureEvent) => void
  onPinchEnd?: (event: GestureEvent) => void
  pinchThreshold?: number // 缩放阈值，默认0.1

  // 点击相关
  onTap?: (event: GestureEvent) => void
  onDoubleTap?: (event: GestureEvent) => void
  onLongPress?: (event: GestureEvent) => void
  tapTimeout?: number // 点击超时，默认300ms
  longPressTimeout?: number // 长按超时，默认500ms

  // 拖拽相关
  onPanStart?: (event: GestureEvent) => void
  onPanMove?: (event: GestureEvent) => void
  onPanEnd?: (event: GestureEvent) => void
  panThreshold?: number // 拖拽阈值，默认10px

  // 通用配置
  preventDefault?: boolean // 是否阻止默认事件
  stopPropagation?: boolean // 是否阻止事件冒泡
}

// 触摸点信息
interface TouchPoint {
  id: number
  x: number
  y: number
  timestamp: number
}

export function useGestures(
  target: Ref<HTMLElement | undefined>,
  options: GestureOptions = {}
) {
  // 默认配置
  const config = {
    swipeThreshold: 50,
    swipeVelocity: 0.3,
    pinchThreshold: 0.1,
    tapTimeout: 300,
    longPressTimeout: 500,
    panThreshold: 10,
    preventDefault: true,
    stopPropagation: false,
    ...options
  }

  // 状态管理
  const isEnabled = ref(false)
  const touches = ref<TouchPoint[]>([])
  const startTouches = ref<TouchPoint[]>([])
  const lastTapTime = ref(0)
  const longPressTimer = ref<NodeJS.Timeout | null>(null)
  const isPanning = ref(false)
  const initialDistance = ref(0)
  const initialScale = ref(1)

  // 工具函数
  const getTouchPoint = (touch: Touch): TouchPoint => ({
    id: touch.identifier,
    x: touch.clientX,
    y: touch.clientY,
    timestamp: Date.now()
  })

  const getDistance = (touch1: TouchPoint, touch2: TouchPoint): number => {
    const dx = touch1.x - touch2.x
    const dy = touch1.y - touch2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  const getAngle = (touch1: TouchPoint, touch2: TouchPoint): number => {
    return Math.atan2(touch2.y - touch1.y, touch2.x - touch1.x) * 180 / Math.PI
  }

  const getSwipeDirection = (startTouch: TouchPoint, endTouch: TouchPoint): SwipeDirection | null => {
    const dx = endTouch.x - startTouch.x
    const dy = endTouch.y - startTouch.y
    const absDx = Math.abs(dx)
    const absDy = Math.abs(dy)

    if (Math.max(absDx, absDy) < config.swipeThreshold) {
      return null
    }

    if (absDx > absDy) {
      return dx > 0 ? 'right' : 'left'
    } else {
      return dy > 0 ? 'down' : 'up'
    }
  }

  const createGestureEvent = (
    type: GestureType,
    additionalData: Partial<GestureEvent> = {}
  ): GestureEvent => ({
    type,
    ...additionalData
  })

  // 事件处理函数
  const handleTouchStart = (event: TouchEvent) => {
    if (config.preventDefault) event.preventDefault()
    if (config.stopPropagation) event.stopPropagation()

    const newTouches = Array.from(event.touches).map(getTouchPoint)
    touches.value = newTouches
    startTouches.value = [...newTouches]

    // 清除长按定时器
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
      longPressTimer.value = null
    }

    if (newTouches.length === 1) {
      // 单指操作
      const touch = newTouches[0]
      
      // 检测双击
      const now = Date.now()
      if (now - lastTapTime.value < config.tapTimeout) {
        // 双击
        config.onDoubleTap?.(createGestureEvent('tap', {
          startX: touch.x,
          startY: touch.y
        }))
        lastTapTime.value = 0
      } else {
        lastTapTime.value = now
      }

      // 设置长按定时器
      longPressTimer.value = setTimeout(() => {
        config.onLongPress?.(createGestureEvent('longpress', {
          startX: touch.x,
          startY: touch.y
        }))
      }, config.longPressTimeout)

    } else if (newTouches.length === 2) {
      // 双指操作（缩放）
      initialDistance.value = getDistance(newTouches[0], newTouches[1])
      initialScale.value = 1
      config.onPinchStart?.(createGestureEvent('pinch'))
    }
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (config.preventDefault) event.preventDefault()
    if (config.stopPropagation) event.stopPropagation()

    const currentTouches = Array.from(event.touches).map(getTouchPoint)
    
    if (currentTouches.length === 1 && startTouches.value.length === 1) {
      // 单指移动
      const currentTouch = currentTouches[0]
      const startTouch = startTouches.value[0]
      const deltaX = currentTouch.x - startTouch.x
      const deltaY = currentTouch.y - startTouch.y
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

      // 清除长按定时器（移动超过阈值）
      if (distance > config.panThreshold && longPressTimer.value) {
        clearTimeout(longPressTimer.value)
        longPressTimer.value = null
      }

      // 拖拽事件
      if (distance > config.panThreshold) {
        if (!isPanning.value) {
          isPanning.value = true
          config.onPanStart?.(createGestureEvent('pan', {
            startX: startTouch.x,
            startY: startTouch.y,
            deltaX,
            deltaY
          }))
        } else {
          config.onPanMove?.(createGestureEvent('pan', {
            startX: startTouch.x,
            startY: startTouch.y,
            deltaX,
            deltaY,
            distance
          }))
        }
      }

    } else if (currentTouches.length === 2 && startTouches.value.length === 2) {
      // 双指缩放
      const currentDistance = getDistance(currentTouches[0], currentTouches[1])
      const scale = currentDistance / initialDistance.value
      
      if (Math.abs(scale - initialScale.value) > config.pinchThreshold) {
        config.onPinch?.(scale, createGestureEvent('pinch', { scale }))
        initialScale.value = scale
      }
    }

    touches.value = currentTouches
  }

  const handleTouchEnd = (event: TouchEvent) => {
    if (config.preventDefault) event.preventDefault()
    if (config.stopPropagation) event.stopPropagation()

    const endTouches = Array.from(event.changedTouches).map(getTouchPoint)
    
    // 清除长按定时器
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
      longPressTimer.value = null
    }

    if (startTouches.value.length === 1 && endTouches.length === 1) {
      const startTouch = startTouches.value[0]
      const endTouch = endTouches[0]
      const duration = endTouch.timestamp - startTouch.timestamp
      const distance = Math.sqrt(
        Math.pow(endTouch.x - startTouch.x, 2) + 
        Math.pow(endTouch.y - startTouch.y, 2)
      )

      if (isPanning.value) {
        // 结束拖拽
        isPanning.value = false
        config.onPanEnd?.(createGestureEvent('pan', {
          startX: startTouch.x,
          startY: startTouch.y,
          endX: endTouch.x,
          endY: endTouch.y,
          deltaX: endTouch.x - startTouch.x,
          deltaY: endTouch.y - startTouch.y,
          distance,
          duration
        }))
      } else if (distance < config.panThreshold && duration < config.tapTimeout) {
        // 单击
        config.onTap?.(createGestureEvent('tap', {
          startX: startTouch.x,
          startY: startTouch.y
        }))
      } else if (distance >= config.swipeThreshold) {
        // 滑动
        const velocity = distance / duration
        if (velocity >= config.swipeVelocity) {
          const direction = getSwipeDirection(startTouch, endTouch)
          if (direction) {
            const gestureEvent = createGestureEvent('swipe', {
              direction,
              distance,
              duration,
              startX: startTouch.x,
              startY: startTouch.y,
              endX: endTouch.x,
              endY: endTouch.y
            })

            // 调用对应方向的回调
            switch (direction) {
              case 'left':
                config.onSwipeLeft?.(gestureEvent)
                break
              case 'right':
                config.onSwipeRight?.(gestureEvent)
                break
              case 'up':
                config.onSwipeUp?.(gestureEvent)
                break
              case 'down':
                config.onSwipeDown?.(gestureEvent)
                break
            }
          }
        }
      }
    } else if (startTouches.value.length === 2) {
      // 结束缩放
      config.onPinchEnd?.(createGestureEvent('pinch'))
    }

    // 重置状态
    touches.value = []
    startTouches.value = []
    isPanning.value = false
  }

  // 启用手势
  const enableGestures = () => {
    if (!target.value || isEnabled.value) return

    const element = target.value
    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: false })
    element.addEventListener('touchcancel', handleTouchEnd, { passive: false })

    isEnabled.value = true
  }

  // 禁用手势
  const disableGestures = () => {
    if (!target.value || !isEnabled.value) return

    const element = target.value
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
    element.removeEventListener('touchcancel', handleTouchEnd)

    // 清理定时器
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
      longPressTimer.value = null
    }

    isEnabled.value = false
  }

  // 更新配置
  const updateOptions = (newOptions: Partial<GestureOptions>) => {
    Object.assign(config, newOptions)
  }

  return {
    isEnabled,
    enableGestures,
    disableGestures,
    updateOptions
  }
}
