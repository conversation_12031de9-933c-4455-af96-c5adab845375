# Consolidated AlertManager Configuration
# Multi-channel alerting with escalation and routing

global:
  # SMTP configuration for email alerts
  smtp_smarthost: '{{ env "SMTP_HOST" | default "localhost:587" }}'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '{{ env "SMTP_USERNAME" }}'
  smtp_auth_password: '{{ env "SMTP_PASSWORD" }}'
  smtp_auth_identity: '{{ env "SMTP_USERNAME" }}'
  
  # Global template paths
  templates:
    - '/etc/alertmanager/templates/*.tmpl'
  
  # Default resolve timeout
  resolve_timeout: 5m

# Route configuration - defines how alerts are distributed
route:
  group_by: ['cluster', 'alertname', 'service']
  group_wait: 30s          # Wait for alerts in same group
  group_interval: 5m       # Send new alerts in group
  repeat_interval: 4h      # Resend alerts if not resolved
  receiver: 'default-receiver'
  
  # Routing tree for different alert types
  routes:
    # Critical P0 alerts - immediate escalation
    - match:
        priority: p0
      receiver: 'critical-escalation'
      group_wait: 0s
      repeat_interval: 15m
      continue: true  # Also send to other receivers

    # Trading system alerts - specialized routing
    - match:
        service: trading
      receiver: 'trading-team'
      group_by: ['alertname', 'strategy_name']
      repeat_interval: 30m
      routes:
        - match:
            severity: critical
          receiver: 'trading-critical'
          repeat_interval: 10m

    # Risk management alerts - urgent handling
    - match:
        service: risk-management
      receiver: 'risk-team'
      group_wait: 0s
      repeat_interval: 5m

    # Infrastructure alerts
    - match_re:
        service: ^(backend|frontend|database|cache)$
      receiver: 'platform-team'
      routes:
        - match:
            severity: critical
          receiver: 'platform-critical'
          repeat_interval: 15m

    # Business hours vs after hours routing
    - match_re:
        severity: ^(warning|info)$
      receiver: 'business-hours-alerts'
      active_time_intervals:
        - business_hours

    - match_re:
        severity: ^(warning|info)$
      receiver: 'after-hours-alerts'
      active_time_intervals:
        - after_hours

    # Compliance and regulatory alerts
    - match:
        team: compliance
      receiver: 'compliance-team'
      repeat_interval: 1h

# Time intervals for business hours routing
time_intervals:
  - name: business_hours
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '17:00'
        weekdays: ['monday:friday']
        location: 'America/New_York'

  - name: after_hours
    time_intervals:
      - times:
          - start_time: '17:01'
            end_time: '08:59'
        weekdays: ['monday:friday']
        location: 'America/New_York'
      - weekdays: ['saturday', 'sunday']
        location: 'America/New_York'

# Inhibition rules - suppress redundant alerts
inhibit_rules:
  # Suppress warnings when critical alerts are firing
  - source_matchers:
      - severity = "critical"
    target_matchers:
      - severity = "warning"
    equal: ['cluster', 'service', 'instance']

  # Suppress individual service alerts when main service is down
  - source_matchers:
      - alertname = "BackendServiceDown"
    target_matchers:
      - service = "backend"
    equal: ['instance']

  # Suppress database connection alerts when database is down
  - source_matchers:
      - alertname = "DatabaseDown"
    target_matchers:
      - alertname =~ "Database.*"
    equal: ['instance']

# Receiver configurations - where alerts are sent
receivers:
  # Default fallback receiver
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '{{ .GroupLabels.cluster }} - {{ .Status | toUpper }} - {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.title | default .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          {{ end }}

  # Critical P0 escalation - multiple channels
  - name: 'critical-escalation'
    # PagerDuty for immediate response
    pagerduty_configs:
      - routing_key: '{{ env "PAGERDUTY_INTEGRATION_KEY" }}'
        description: '{{ .GroupLabels.cluster }} - {{ .GroupLabels.alertname }}'
        severity: 'critical'
        details:
          cluster: '{{ .GroupLabels.cluster }}'
          service: '{{ .GroupLabels.service }}'
          instance: '{{ .GroupLabels.instance }}'
    
    # Slack for team visibility
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_CRITICAL" }}'
        channel: '#critical-alerts'
        title: '🚨 CRITICAL ALERT - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.title | default .Annotations.summary }}*
          {{ .Annotations.description }}
          
          *Impact:* {{ .Annotations.impact | default "Service disruption" }}
          *Action:* {{ .Annotations.action | default "Investigate immediately" }}
          {{ if .Annotations.runbook }}*Runbook:* {{ .Annotations.runbook }}{{ end }}
          {{ end }}
        color: 'danger'
    
    # SMS for key personnel (using email-to-SMS gateway)
    email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
        body: '{{ .GroupLabels.cluster }} - {{ .Alerts | len }} critical alerts firing'

  # Trading team specialized alerts
  - name: 'trading-team'
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_TRADING" }}'
        channel: '#trading-alerts'
        title: '📈 Trading Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.title | default .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ if .Labels.strategy_name }}*Strategy:* {{ .Labels.strategy_name }}{{ end }}
          {{ end }}
        color: '{{ if eq .Status "firing" }}warning{{ else }}good{{ end }}'

  # Trading critical alerts with phone escalation
  - name: 'trading-critical'
    # Immediate Slack notification
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_TRADING" }}'
        channel: '#trading-critical'
        title: '🚨 CRITICAL TRADING ALERT'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.title }}*
          {{ .Annotations.description }}
          
          *Impact:* {{ .Annotations.impact }}
          *Action Required:* {{ .Annotations.action }}
          {{ end }}
        color: 'danger'
    
    # Phone call escalation
    pagerduty_configs:
      - routing_key: '{{ env "PAGERDUTY_TRADING_KEY" }}'
        description: 'CRITICAL TRADING: {{ .GroupLabels.alertname }}'
        severity: 'critical'

  # Risk management team - immediate escalation
  - name: 'risk-team'
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_RISK" }}'
        channel: '#risk-alerts'
        title: '⚠️ RISK ALERT - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.title }}*
          {{ .Annotations.description }}
          
          {{ if .Annotations.impact }}*Risk Impact:* {{ .Annotations.impact }}{{ end }}
          {{ if .Annotations.action }}*Required Action:* {{ .Annotations.action }}{{ end }}
          {{ end }}
        color: 'danger'
    
    # Always email risk team
    email_configs:
      - to: '<EMAIL>'
        subject: 'RISK ALERT: {{ .GroupLabels.alertname }}'

  # Platform team for infrastructure
  - name: 'platform-team'
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_PLATFORM" }}'
        channel: '#platform-alerts'
        title: '🔧 Platform Alert - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Service:* {{ .Labels.service }}
          *Instance:* {{ .Labels.instance }}
          *Issue:* {{ .Annotations.summary }}
          {{ if .Annotations.dashboard }}*Dashboard:* {{ .Annotations.dashboard }}{{ end }}
          {{ end }}

  # Platform critical with escalation
  - name: 'platform-critical'
    pagerduty_configs:
      - routing_key: '{{ env "PAGERDUTY_PLATFORM_KEY" }}'
        description: 'CRITICAL PLATFORM: {{ .GroupLabels.alertname }}'
        severity: 'critical'
    
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_PLATFORM" }}'
        channel: '#platform-critical'
        title: '🚨 CRITICAL PLATFORM ALERT'
        color: 'danger'

  # Business hours - email and Slack
  - name: 'business-hours-alerts'
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_GENERAL" }}'
        channel: '#alerts'
        color: 'warning'

  # After hours - email only for non-critical
  - name: 'after-hours-alerts'
    email_configs:
      - to: '<EMAIL>'

  # Compliance team
  - name: 'compliance-team'
    email_configs:
      - to: '<EMAIL>'
        subject: 'COMPLIANCE ALERT: {{ .GroupLabels.alertname }}'
    
    # High priority compliance alerts to Slack
    slack_configs:
      - api_url: '{{ env "SLACK_WEBHOOK_COMPLIANCE" }}'
        channel: '#compliance-alerts'
        title: '📋 Compliance Alert - {{ .GroupLabels.alertname }}'
        color: 'warning'

# Mute configuration (for maintenance windows)
mute_time_intervals:
  - name: maintenance_window
    time_intervals:
      - times:
          - start_time: '02:00'
            end_time: '04:00'
        weekdays: ['sunday']
        location: 'America/New_York'