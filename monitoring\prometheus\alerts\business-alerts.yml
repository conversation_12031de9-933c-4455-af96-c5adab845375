# Quantitative Trading Platform - Business Logic Alert Rules
# Trading-specific alerts for financial operations and business metrics

groups:
  - name: quant_trading_alerts
    interval: 1m
    rules:
      # === Trading System Health ===
      
      - alert: TradingSystemDown
        expr: trading_system_status == 0
        for: 30s
        labels:
          severity: critical
          service: trading
          team: quant
          priority: p0
        annotations:
          title: "🚨 Trading System is DOWN"
          summary: "Core trading system has stopped functioning"
          description: "Trading system status indicates system failure - all trading halted"
          impact: "NO TRADING POSSIBLE - Revenue impact"
          action: "Immediate investigation required - escalate to on-call team"
          runbook: "https://docs.company.com/runbooks/trading-down"

      - alert: MarketDataFeedDown
        expr: market_data_feed_status == 0
        for: 1m
        labels:
          severity: critical
          service: market-data
          team: quant
          priority: p1
        annotations:
          title: "🚨 Market Data Feed Down"
          summary: "Real-time market data feed is disconnected"
          description: "Market data provider {{ $labels.provider }} is not responding"
          impact: "Trading decisions based on stale data"

      - alert: OrderExecutionFailureHigh
        expr: |
          (
            rate(order_execution_failures_total[5m]) /
            rate(order_execution_attempts_total[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: warning
          service: trading
          team: quant
        annotations:
          title: "⚠️ High Order Execution Failure Rate"
          summary: "Elevated order execution failure rate detected"
          description: "{{ $value | humanizePercentage }} of orders failing execution"

      - alert: CriticalOrderExecutionFailures
        expr: |
          (
            rate(order_execution_failures_total[5m]) /
            rate(order_execution_attempts_total[5m])
          ) * 100 > 20
        for: 1m
        labels:
          severity: critical
          service: trading
          team: quant
        annotations:
          title: "🚨 Critical Order Execution Failures"
          summary: "Critically high order execution failure rate"
          description: "{{ $value | humanizePercentage }} of orders failing - trading system compromised"

      # === Risk Management ===
      
      - alert: PositionLimitExceeded
        expr: current_position_value > max_position_limit
        for: 0s  # Immediate alert
        labels:
          severity: critical
          service: risk-management
          team: quant
          priority: p0
        annotations:
          title: "🚨 POSITION LIMIT EXCEEDED"
          summary: "Position size exceeds configured risk limits"
          description: "Current position: ${{ $value | humanize }}, Limit: ${{ $labels.limit | humanize }}"
          impact: "REGULATORY RISK - Immediate position reduction required"
          action: "HALT TRADING - Reduce positions immediately"

      - alert: DailyLossLimitApproached
        expr: daily_pnl < (daily_loss_limit * 0.8)
        for: 1m
        labels:
          severity: warning
          service: risk-management
          team: quant
        annotations:
          title: "⚠️ Daily Loss Limit Approaching"
          summary: "Daily P&L approaching loss limit threshold"
          description: "Current P&L: ${{ $value | humanize }}, 80% of limit reached"

      - alert: DailyLossLimitExceeded
        expr: daily_pnl < daily_loss_limit
        for: 0s
        labels:
          severity: critical
          service: risk-management
          team: quant
          priority: p0
        annotations:
          title: "🚨 DAILY LOSS LIMIT EXCEEDED"
          summary: "Daily loss limit breached - trading halt required"
          description: "Current P&L: ${{ $value | humanize }}, Limit: ${{ $labels.limit | humanize }}"
          action: "HALT ALL TRADING IMMEDIATELY"

      - alert: VolatilitySpike
        expr: current_volatility > (average_volatility * 2)
        for: 5m
        labels:
          severity: warning
          service: risk-management
          team: quant
        annotations:
          title: "⚠️ Market Volatility Spike"
          summary: "Market volatility significantly elevated"
          description: "Current volatility: {{ $value | humanizePercentage }}, 2x average"
          action: "Review position sizing and risk parameters"

      # === Strategy Performance ===
      
      - alert: StrategyUnderperforming
        expr: |
          (
            strategy_return_7d < benchmark_return_7d * 0.5
          )
        for: 1h
        labels:
          severity: warning
          service: strategy
          team: quant
        annotations:
          title: "⚠️ Strategy Underperforming"
          summary: "Strategy {{ $labels.strategy_name }} significantly underperforming"
          description: "7-day return: {{ $value | humanizePercentage }} vs benchmark"

      - alert: StrategyDrawdownHigh
        expr: strategy_max_drawdown > 0.15
        for: 30m
        labels:
          severity: warning
          service: strategy
          team: quant
        annotations:
          title: "⚠️ High Strategy Drawdown"
          summary: "Strategy {{ $labels.strategy_name }} experiencing high drawdown"
          description: "Current drawdown: {{ $value | humanizePercentage }}"

      - alert: StrategyNotTrading
        expr: |
          increase(strategy_trades_count[1h]) == 0
          and
          market_hours == 1
        for: 2h
        labels:
          severity: warning
          service: strategy
          team: quant
        annotations:
          title: "⚠️ Strategy Not Trading"
          summary: "Strategy {{ $labels.strategy_name }} has not executed trades"
          description: "No trades executed in the past 2 hours during market hours"

      # === Market Data Quality ===
      
      - alert: StaleMarketData
        expr: |
          (time() - market_data_last_update_timestamp) > 300
        for: 1m
        labels:
          severity: warning
          service: market-data
          team: quant
        annotations:
          title: "⚠️ Stale Market Data"
          summary: "Market data is not updating"
          description: "Last update: {{ $value | humanizeDuration }} ago for {{ $labels.symbol }}"

      - alert: MarketDataGaps
        expr: market_data_missing_points > 10
        for: 5m
        labels:
          severity: warning
          service: market-data
          team: quant
        annotations:
          title: "⚠️ Market Data Gaps"
          summary: "Missing data points detected"
          description: "{{ $value }} missing data points for {{ $labels.symbol }}"

      # === Backtesting & Model Performance ===
      
      - alert: BacktestJobsFailing
        expr: |
          (
            rate(backtest_job_failures_total[10m]) /
            rate(backtest_job_total[10m])
          ) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: backtesting
          team: quant
        annotations:
          title: "⚠️ High Backtest Failure Rate"
          summary: "Backtest jobs are failing at elevated rate"
          description: "{{ $value | humanizePercentage }} of backtest jobs failing"

      - alert: ModelPerformanceDegraded
        expr: model_accuracy < 0.6
        for: 1h
        labels:
          severity: warning
          service: ml-models
          team: quant
        annotations:
          title: "⚠️ Model Performance Degraded"
          summary: "ML model {{ $labels.model_name }} accuracy below threshold"
          description: "Current accuracy: {{ $value | humanizePercentage }}"

      # === Portfolio Metrics ===
      
      - alert: PortfolioConcentrationHigh
        expr: max(position_weight_percentage) > 20
        for: 10m
        labels:
          severity: warning
          service: portfolio
          team: quant
        annotations:
          title: "⚠️ High Portfolio Concentration"
          summary: "Single position represents large portfolio percentage"
          description: "Position {{ $labels.symbol }}: {{ $value | humanizePercentage }} of portfolio"

      - alert: CashLevelLow
        expr: cash_balance_percentage < 5
        for: 30m
        labels:
          severity: warning
          service: portfolio
          team: quant
        annotations:
          title: "⚠️ Low Cash Reserves"
          summary: "Portfolio cash level below recommended threshold"
          description: "Cash: {{ $value | humanizePercentage }} of portfolio"

      # === Compliance & Regulatory ===
      
      - alert: RegulatoryReportingDelayed
        expr: |
          (time() - last_regulatory_report_timestamp) > 86400
        for: 1h
        labels:
          severity: critical
          service: compliance
          team: compliance
        annotations:
          title: "🚨 Regulatory Reporting Delayed"
          summary: "Required regulatory report is overdue"
          description: "Last report: {{ $value | humanizeDuration }} ago"
          impact: "REGULATORY COMPLIANCE RISK"