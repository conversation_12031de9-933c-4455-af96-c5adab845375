#!/usr/bin/env python3
"""
量化投资平台 - 仓库清理脚本
用于清理已经被提交但不应该在版本控制中的文件和目录
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Set
import argparse
import json
from datetime import datetime

class RepositoryCleanup:
    def __init__(self, repo_path: Path):
        self.repo_path = repo_path
        self.cleanup_stats = {
            'files_removed': 0,
            'dirs_removed': 0,
            'size_saved': 0,
            'removed_items': []
        }
        
        # 定义需要清理的目录和文件模式
        self.cleanup_patterns = {
            'directories': [
                # Python 相关
                'backend/venv',
                '__pycache__',
                '.pytest_cache',
                '.mypy_cache',
                '.tox',
                'htmlcov',
                
                # Node.js 相关
                'frontend/node_modules',
                'node_modules',
                'frontend/dist',
                '.next',
                '.nuxt',
                
                # 数据和缓存
                'data',
                'cache',
                'logs',
                'backups',
                
                # 测试和临时文件
                'screenshots',
                'test-screenshots',
                'test_screenshots',
                'final_test_screenshots',
                'smart_test_screenshots',
                'archive',
                'test-results',
                'test_results',
                'playwright-report',
                
                # 构建输出
                'dist',
                'build',
                'out',
                'target',
                
                # IDE
                '.vscode',
                '.idea',
                
                # 特定于项目的大目录
                'mcp/browser-tools-mcp',
                'mcp/mcp-use',
                'mcp/puppeteer',
                'mcp/servers',
                'reports/analysis',
                'reports/integration',
                'reports/performance',
                'reports/security',
                'reports/testing',
            ],
            
            'file_patterns': [
                # 数据库文件
                '*.db',
                '*.sqlite',
                '*.sqlite3',
                
                # 日志文件
                '*.log',
                
                # 临时文件
                '*.tmp',
                '*.temp',
                '*.bak',
                '*.swp',
                '*.swo',
                
                # 媒体文件
                '*.png',
                '*.jpg',
                '*.jpeg',
                '*.gif',
                '*.bmp',
                '*.webp',
                '*.ico',
                '*.mp4',
                '*.avi',
                '*.mov',
                
                # 压缩文件
                '*.zip',
                '*.tar',
                '*.tar.gz',
                '*.tgz',
                '*.rar',
                '*.7z',
                
                # Python 字节码
                '*.pyc',
                '*.pyo',
                '*.pyd',
                
                # 特定文件
                'nul',
            ],
            
            'specific_files': [
                # 根目录的测试和临时文件
                'backend_api_test.js',
                'backend_api_test_report.json',
                'comprehensive_frontend_test.js',
                'comprehensive_real_user_test.py',
                'comprehensive_test_report.md',
                'comprehensive_test_report_generator.js',
                'comprehensive_trading_center_test.js',
                'final_comprehensive_test_report.json',
                'final_deep_test.js',
                'final_deep_test_report.json',
                'final_system_test.py',
                'final_verification.py',
                'frontend_test_report.json',
                'improvement_verification_report.json',
                'index.html',
                'minimal_backend.py',
                'monitoring-dashboard.html',
                'package-lock.json',
                'package.json',
                'puppeteer_test.js',
                'puppeteer_test_report.json',
                'quick_server_check.js',
                'real_user_test.js',
                'real_user_test_report_*.json',
                'real_user_test_summary_*.md',
                'server_diagnostic*.js',
                'server_diagnostic*.json',
                'simple_cleanup.py',
                'simple_test.py',
                'simple_tushare_test.py',
                'smart_puppeteer_test.js',
                'smart_test_report.json',
                'smart_test_report.md',
                'test_backend_startup.py',
                'test_focused_tushare.py',
                'test_frontend_access.py',
                'test_server.py',
                'test_tushare_*.py',
                'trading-terminal.html',
                'trading_center_test.html',
                'user_experience_deep_test.js',
                'user_experience_test_report.json',
                'verify_improvements.py',
                'verify_tushare_service.py',
                'advanced_cleanup.py',
                'check_cleanup_size.py',
                'cleanup_redundant_files.py',
                
                # 启动脚本（保留新的统一脚本，移除旧的）
                'start_backend_simple.py',
                'start_fixed_platform.bat',
                'start_frontend.py',
                'start_frontend_simple.bat',
                'start_platform_en.bat',
                'start_platform_fixed.bat',
                'start_simple_frontend.py',
                'start_trading_platform.bat',
                'stop_platform.bat',
                
                # 中文文件名
                '完整深度测试总结报告.md',
                '实盘交易系统升级说明.md',
                
                # 大量测试报告文档（根目录级别）
                'AUTHORITATIVE_DOCS_INDEX.md',
                'COMPREHENSIVE_TRADING_CENTER_TESTING_REPORT.md',
                'DEPLOYMENT_CONSOLIDATION_SUMMARY.md',
                'DOCUMENTATION_CLEANUP_PLAN.md',
                'DOCUMENTATION_CLEANUP_SUMMARY.md',
                'FINAL_CLEANUP_REPORT.md',
                'FINAL_PROJECT_COMPLETION_REPORT.md',
                'FINAL_PUPPETEER_MCP_TEST_SUMMARY.md',
                'FINAL_TESTING_SUMMARY.md',
                'FINAL_TRADING_CENTER_ISSUES_RESOLUTION_REPORT.md',
                'FINAL_深度测试报告.md',
                'PRODUCTION_READY_SUMMARY.md',
                'PROJECT_COMPLETION_REPORT.md',
                'PUPPETEER_MCP_深度测试完整报告.md',
                'REDUNDANT_FILES_TO_CLEAN.md',
                'SCRIPT_CONSOLIDATION_SUMMARY.md',
                'SECURITY_FIXES_GUIDE.md',
                'TESTING_CONFIG_CONSOLIDATION.md',
            ]
        }
        
    def get_directory_size(self, path: Path) -> int:
        """计算目录大小"""
        total_size = 0
        try:
            if path.is_file():
                return path.stat().st_size
            elif path.is_dir():
                for item in path.rglob('*'):
                    if item.is_file():
                        try:
                            total_size += item.stat().st_size
                        except (PermissionError, FileNotFoundError):
                            continue
        except (PermissionError, FileNotFoundError):
            pass
        return total_size
    
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def scan_repository(self) -> Dict[str, List[Path]]:
        """扫描仓库中需要清理的文件"""
        found_items = {
            'directories': [],
            'files': []
        }
        
        print("🔍 扫描仓库中需要清理的文件...")
        
        # 扫描目录
        for dir_pattern in self.cleanup_patterns['directories']:
            for path in self.repo_path.rglob(dir_pattern):
                if path.is_dir() and path.exists():
                    found_items['directories'].append(path)
        
        # 扫描文件模式
        for file_pattern in self.cleanup_patterns['file_patterns']:
            for path in self.repo_path.rglob(file_pattern):
                if path.is_file() and path.exists():
                    # 跳过重要文件
                    if self.is_important_file(path):
                        continue
                    found_items['files'].append(path)
        
        # 扫描特定文件
        for file_name in self.cleanup_patterns['specific_files']:
            if '*' in file_name:
                for path in self.repo_path.rglob(file_name):
                    if path.is_file() and path.exists():
                        found_items['files'].append(path)
            else:
                file_path = self.repo_path / file_name
                if file_path.is_file() and file_path.exists():
                    found_items['files'].append(file_path)
        
        return found_items
    
    def is_important_file(self, path: Path) -> bool:
        """检查是否是重要文件，不应该删除"""
        important_files = {
            'README.md',
            'LICENSE',
            'package.json',
            'requirements.txt',
            'docker-compose.yml',
            'docker-compose.prod.yml',
            '.gitignore',
            '.env.example',
        }
        
        # 检查是否在重要目录中
        important_dirs = {
            'docs',
            '.github',
            'scripts',
            'config',
        }
        
        if path.name in important_files:
            return True
        
        for part in path.parts:
            if part in important_dirs:
                return True
                
        return False
    
    def preview_cleanup(self, found_items: Dict[str, List[Path]]) -> None:
        """预览将要清理的内容"""
        print("\n📋 清理预览:")
        print("=" * 60)
        
        total_size = 0
        
        # 预览目录
        if found_items['directories']:
            print(f"\n📁 将删除 {len(found_items['directories'])} 个目录:")
            for dir_path in found_items['directories'][:20]:  # 只显示前20个
                size = self.get_directory_size(dir_path)
                total_size += size
                relative_path = dir_path.relative_to(self.repo_path)
                print(f"  🗂️  {relative_path} ({self.format_size(size)})")
            
            if len(found_items['directories']) > 20:
                remaining = len(found_items['directories']) - 20
                print(f"  ... 还有 {remaining} 个目录")
        
        # 预览文件
        if found_items['files']:
            print(f"\n📄 将删除 {len(found_items['files'])} 个文件:")
            for file_path in found_items['files'][:30]:  # 只显示前30个
                size = self.get_directory_size(file_path)
                total_size += size
                relative_path = file_path.relative_to(self.repo_path)
                print(f"  📄 {relative_path} ({self.format_size(size)})")
            
            if len(found_items['files']) > 30:
                remaining = len(found_items['files']) - 30
                print(f"  ... 还有 {remaining} 个文件")
        
        print(f"\n💾 预计释放空间: {self.format_size(total_size)}")
        print("=" * 60)
    
    def remove_from_git(self, path: Path) -> bool:
        """从Git中移除文件或目录"""
        try:
            relative_path = path.relative_to(self.repo_path)
            result = subprocess.run(
                ['git', 'rm', '-r', '--cached', str(relative_path)],
                cwd=self.repo_path,
                capture_output=True,
                text=True
            )
            return result.returncode == 0
        except Exception as e:
            print(f"⚠️ 从Git移除失败 {path}: {e}")
            return False
    
    def cleanup_items(self, found_items: Dict[str, List[Path]], dry_run: bool = False) -> None:
        """执行清理操作"""
        if dry_run:
            print("\n🧪 预演模式 - 不会实际删除文件")
            return
        
        print("\n🧹 开始清理仓库...")
        
        # 清理目录
        for dir_path in found_items['directories']:
            try:
                if dir_path.exists():
                    size = self.get_directory_size(dir_path)
                    relative_path = dir_path.relative_to(self.repo_path)
                    
                    # 从Git中移除
                    self.remove_from_git(dir_path)
                    
                    # 删除目录
                    shutil.rmtree(dir_path)
                    
                    self.cleanup_stats['dirs_removed'] += 1
                    self.cleanup_stats['size_saved'] += size
                    self.cleanup_stats['removed_items'].append(str(relative_path))
                    
                    print(f"  ✅ 已删除目录: {relative_path} ({self.format_size(size)})")
            except Exception as e:
                print(f"  ❌ 删除目录失败 {dir_path}: {e}")
        
        # 清理文件
        for file_path in found_items['files']:
            try:
                if file_path.exists():
                    size = self.get_directory_size(file_path)
                    relative_path = file_path.relative_to(self.repo_path)
                    
                    # 从Git中移除
                    self.remove_from_git(file_path)
                    
                    # 删除文件
                    file_path.unlink()
                    
                    self.cleanup_stats['files_removed'] += 1
                    self.cleanup_stats['size_saved'] += size
                    self.cleanup_stats['removed_items'].append(str(relative_path))
                    
                    print(f"  ✅ 已删除文件: {relative_path} ({self.format_size(size)})")
            except Exception as e:
                print(f"  ❌ 删除文件失败 {file_path}: {e}")
    
    def create_cleanup_report(self) -> None:
        """创建清理报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'repository_path': str(self.repo_path),
            'cleanup_stats': self.cleanup_stats,
            'patterns_used': self.cleanup_patterns
        }
        
        report_file = self.repo_path / 'cleanup_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 清理报告已保存至: {report_file}")
    
    def print_summary(self) -> None:
        """打印清理汇总"""
        print("\n🎉 清理完成!")
        print("=" * 60)
        print(f"📁 删除的目录数量: {self.cleanup_stats['dirs_removed']}")
        print(f"📄 删除的文件数量: {self.cleanup_stats['files_removed']}")
        print(f"💾 释放的存储空间: {self.format_size(self.cleanup_stats['size_saved'])}")
        
        total_items = self.cleanup_stats['dirs_removed'] + self.cleanup_stats['files_removed']
        print(f"🗂️ 总清理项目数: {total_items}")
        print("=" * 60)
        
        # 建议后续操作
        print("\n📝 建议的后续操作:")
        print("1. 检查 .gitignore 文件是否正确配置")
        print("2. 运行: git add .gitignore")
        print("3. 运行: git commit -m 'feat: 清理仓库并完善.gitignore配置'")
        print("4. 检查是否还有其他不需要的文件")
        print("5. 考虑使用 git gc 来压缩仓库")
    
    def run(self, dry_run: bool = False, interactive: bool = True) -> None:
        """运行清理流程"""
        print("🧹 量化投资平台 - 仓库清理工具")
        print("=" * 60)
        print(f"📂 仓库路径: {self.repo_path}")
        
        # 确认是Git仓库
        if not (self.repo_path / '.git').exists():
            print("❌ 错误: 当前目录不是Git仓库")
            return
        
        # 扫描需要清理的项目
        found_items = self.scan_repository()
        
        if not found_items['directories'] and not found_items['files']:
            print("✅ 仓库已经很干净，没有找到需要清理的文件！")
            return
        
        # 预览清理内容
        self.preview_cleanup(found_items)
        
        # 交互式确认
        if interactive:
            response = input("\n❓ 确定要执行清理操作吗? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("🚫 清理操作已取消")
                return
        
        # 执行清理
        self.cleanup_items(found_items, dry_run)
        
        # 生成报告
        if not dry_run:
            self.create_cleanup_report()
            self.print_summary()

def main():
    parser = argparse.ArgumentParser(description='量化投资平台仓库清理工具')
    parser.add_argument('--path', default='.', help='仓库路径 (默认: 当前目录)')
    parser.add_argument('--dry-run', action='store_true', help='预演模式，不实际删除文件')
    parser.add_argument('--yes', action='store_true', help='跳过交互确认')
    parser.add_argument('--verbose', action='store_true', help='显示详细输出')
    
    args = parser.parse_args()
    
    repo_path = Path(args.path).resolve()
    
    if not repo_path.exists():
        print(f"❌ 错误: 路径不存在: {repo_path}")
        sys.exit(1)
    
    try:
        cleanup = RepositoryCleanup(repo_path)
        cleanup.run(dry_run=args.dry_run, interactive=not args.yes)
    except KeyboardInterrupt:
        print("\n🚫 操作已被用户中断")
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()