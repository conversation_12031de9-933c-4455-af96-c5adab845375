# API 文档

## 📋 目录

- [基础信息](#基础信息)
- [认证授权](#认证授权)
- [用户管理](#用户管理)
- [市场数据](#市场数据)
- [交易接口](#交易接口)
- [策略管理](#策略管理)
- [错误处理](#错误处理)

## 🔧 基础信息

### 服务地址
- **开发环境**: `http://localhost:8000`
- **测试环境**: `https://test-api.yourdomain.com`
- **生产环境**: `https://api.yourdomain.com`

### API版本
- **当前版本**: v1
- **基础路径**: `/api/v1`

### 请求格式
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **时间格式**: ISO 8601 (`2025-08-07T16:30:00Z`)

### 响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-08-07T16:30:00Z",
  "request_id": "uuid-string"
}
```

## 🔐 认证授权

### JWT Token认证

#### 登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123",
  "captcha_token": "captcha-token"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": "user-id",
      "username": "<EMAIL>",
      "name": "用户名",
      "role": "trader"
    }
  }
}
```

#### 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

#### 登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

### 请求头认证
```http
Authorization: Bearer <access_token>
X-API-Key: <api_key>  # 可选，用于API密钥认证
```

## 👤 用户管理

### 获取用户信息
```http
GET /api/v1/users/me
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "user-id",
    "username": "<EMAIL>",
    "name": "用户名",
    "avatar": "https://example.com/avatar.jpg",
    "role": "trader",
    "permissions": ["trade", "view_market"],
    "created_at": "2025-01-01T00:00:00Z",
    "last_login": "2025-08-07T16:30:00Z"
  }
}
```

### 更新用户信息
```http
PUT /api/v1/users/me
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "新用户名",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

## 📊 市场数据

### 获取股票列表
```http
GET /api/v1/market/stocks?page=1&size=20&search=平安
Authorization: Bearer <access_token>
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20, 最大: 100)
- `search`: 搜索关键词
- `market`: 市场代码 (sh, sz, bj)
- `industry`: 行业代码

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "symbol": "000001.SZ",
        "name": "平安银行",
        "market": "sz",
        "industry": "银行",
        "current_price": 12.50,
        "change": 0.15,
        "change_percent": 1.22,
        "volume": 1234567,
        "turnover": 15432100.50,
        "market_cap": 241500000000,
        "pe_ratio": 5.8,
        "pb_ratio": 0.65
      }
    ],
    "total": 4500,
    "page": 1,
    "size": 20,
    "pages": 225
  }
}
```

### 获取实时行情
```http
GET /api/v1/market/quote/{symbol}
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "symbol": "000001.SZ",
    "name": "平安银行",
    "current_price": 12.50,
    "open_price": 12.35,
    "high_price": 12.68,
    "low_price": 12.30,
    "close_price": 12.35,
    "volume": 1234567,
    "turnover": 15432100.50,
    "change": 0.15,
    "change_percent": 1.22,
    "timestamp": "2025-08-07T16:30:00Z",
    "status": "trading"
  }
}
```

### 获取K线数据
```http
GET /api/v1/market/kline/{symbol}?period=1d&start=2025-01-01&end=2025-08-07
Authorization: Bearer <access_token>
```

**查询参数**:
- `period`: 周期 (1m, 5m, 15m, 30m, 1h, 1d, 1w, 1M)
- `start`: 开始日期
- `end`: 结束日期
- `limit`: 数据条数限制

## 💰 交易接口

### 下单
```http
POST /api/v1/trading/orders
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "symbol": "000001.SZ",
  "side": "buy",
  "type": "limit",
  "quantity": 100,
  "price": 12.50,
  "time_in_force": "GTC"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "order_id": "order-uuid",
    "client_order_id": "client-order-id",
    "symbol": "000001.SZ",
    "side": "buy",
    "type": "limit",
    "quantity": 100,
    "price": 12.50,
    "status": "pending",
    "time_in_force": "GTC",
    "created_at": "2025-08-07T16:30:00Z"
  }
}
```

### 撤单
```http
DELETE /api/v1/trading/orders/{order_id}
Authorization: Bearer <access_token>
```

### 获取订单列表
```http
GET /api/v1/trading/orders?status=pending&page=1&size=20
Authorization: Bearer <access_token>
```

### 获取持仓信息
```http
GET /api/v1/trading/positions
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "symbol": "000001.SZ",
      "name": "平安银行",
      "quantity": 1000,
      "available_quantity": 1000,
      "avg_cost": 12.30,
      "current_price": 12.50,
      "market_value": 12500.00,
      "unrealized_pnl": 200.00,
      "unrealized_pnl_percent": 1.63,
      "updated_at": "2025-08-07T16:30:00Z"
    }
  ]
}
```

## 🎯 策略管理

### 创建策略
```http
POST /api/v1/strategies
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "均线策略",
  "description": "基于双均线的交易策略",
  "type": "trend_following",
  "parameters": {
    "fast_period": 5,
    "slow_period": 20,
    "symbols": ["000001.SZ", "000002.SZ"]
  },
  "risk_settings": {
    "max_position_size": 10000,
    "stop_loss_percent": 5.0,
    "take_profit_percent": 10.0
  }
}
```

### 启动/停止策略
```http
POST /api/v1/strategies/{strategy_id}/start
POST /api/v1/strategies/{strategy_id}/stop
Authorization: Bearer <access_token>
```

### 获取策略列表
```http
GET /api/v1/strategies?status=running&page=1&size=20
Authorization: Bearer <access_token>
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数无效",
    "details": "价格必须大于0",
    "field": "price"
  },
  "timestamp": "2025-08-07T16:30:00Z",
  "request_id": "uuid-string"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `INVALID_PARAMETER` | 400 | 参数无效 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

### 业务错误码

| 错误码 | 描述 |
|--------|------|
| `INSUFFICIENT_BALANCE` | 余额不足 |
| `INVALID_SYMBOL` | 无效的股票代码 |
| `MARKET_CLOSED` | 市场已关闭 |
| `ORDER_NOT_FOUND` | 订单不存在 |
| `STRATEGY_RUNNING` | 策略正在运行 |

---

更多详细信息请参考 [Swagger文档](http://localhost:8000/docs) 或 [ReDoc文档](http://localhost:8000/redoc)。
