# Consolidated Backend Deployment
# Production-ready with autoscaling, security, and monitoring

apiVersion: apps/v1
kind: Deployment
metadata:
  name: quant-platform-backend
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-backend
    app.kubernetes.io/instance: quant-platform
    app.kubernetes.io/version: "v1"
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: quant-platform
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: quant-platform-backend
      app.kubernetes.io/instance: quant-platform
  template:
    metadata:
      labels:
        app.kubernetes.io/name: quant-platform-backend
        app.kubernetes.io/instance: quant-platform
        app.kubernetes.io/version: "v1"
        app.kubernetes.io/component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
        config.kubernetes.io/depends-on: "quant-platform-postgres,quant-platform-redis"
    spec:
      serviceAccountName: quant-platform-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: backend
        image: quant-platform/backend:latest
        imagePullPolicy: Always
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        - containerPort: 8001
          name: metrics
          protocol: TCP
        env:
        # Configuration from ConfigMap
        - name: API_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: API_PORT
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: LOG_LEVEL
        - name: DEBUG
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: DEBUG
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: DATABASE_PORT
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: DATABASE_NAME
        - name: DATABASE_USER
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: DATABASE_USER
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: REDIS_PORT
        - name: REDIS_DB
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: REDIS_DB
        - name: JWT_ALGORITHM
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: JWT_ALGORITHM
        - name: ACCESS_TOKEN_EXPIRE_MINUTES
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: ACCESS_TOKEN_EXPIRE_MINUTES
        - name: CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: CORS_ORIGINS
        - name: GUNICORN_WORKERS
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: GUNICORN_WORKERS
        # Secrets
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: DATABASE_PASSWORD
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: DATABASE_URL
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: REDIS_PASSWORD
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: JWT_SECRET_KEY
        - name: MARKET_DATA_API_KEY
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: MARKET_DATA_API_KEY
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
            ephemeral-storage: "1Gi"
          limits:
            memory: "4Gi"
            cpu: "2"
            ephemeral-storage: "2Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
        - name: data
          mountPath: /app/data
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        persistentVolumeClaim:
          claimName: quant-platform-logs-pvc
      - name: data
        persistentVolumeClaim:
          claimName: quant-platform-data-pvc
      - name: cache
        emptyDir:
          sizeLimit: 1Gi
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "quant-workload"
        operator: "Equal"
        value: "backend"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - quant-platform-backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: quant-platform-backend
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-backend
    app.kubernetes.io/instance: quant-platform
    app.kubernetes.io/component: backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8001"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  - port: 8001
    targetPort: 8001
    protocol: TCP
    name: metrics
  selector:
    app.kubernetes.io/name: quant-platform-backend
    app.kubernetes.io/instance: quant-platform
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: quant-platform-backend-hpa
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-backend
    app.kubernetes.io/component: autoscaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: quant-platform-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: quant-platform-backend
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-backend
    app.kubernetes.io/component: serviceaccount
automountServiceAccountToken: false