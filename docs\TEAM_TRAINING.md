# 团队培训指南

## 📋 培训大纲

### 🎯 培训目标
- 掌握项目架构和技术栈
- 熟悉开发流程和代码规范
- 了解最佳实践和常见问题解决方案
- 提升团队协作效率

### 👥 培训对象
- 新加入的前端开发工程师
- 新加入的后端开发工程师
- 项目维护人员
- 测试工程师

## 🏗️ 第一部分：项目架构概览

### 1.1 技术栈介绍

#### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架
- **TypeScript**: 类型安全的JavaScript超集
- **Vite**: 快速的前端构建工具
- **Element Plus**: Vue 3组件库
- **Pinia**: Vue状态管理库
- **Vue Router**: 官方路由管理器

#### 后端技术栈
- **FastAPI**: 现代、快速的Python Web框架
- **SQLAlchemy**: Python SQL工具包和ORM
- **Alembic**: 数据库迁移工具
- **Pydantic**: 数据验证和设置管理
- **Uvicorn**: ASGI服务器

### 1.2 项目结构解析

```
quant014/
├── frontend/          # 前端应用
│   ├── src/
│   │   ├── api/       # API接口层
│   │   ├── components/ # 可复用组件
│   │   ├── views/     # 页面组件
│   │   ├── stores/    # 状态管理
│   │   └── types/     # TypeScript类型
│   └── public/        # 静态资源
├── backend/           # 后端服务
│   ├── app/
│   │   ├── api/       # API路由
│   │   ├── models/    # 数据模型
│   │   ├── services/  # 业务逻辑
│   │   └── core/      # 核心配置
│   └── alembic/       # 数据库迁移
├── docs/              # 项目文档
└── scripts/           # 构建脚本
```

## 💻 第二部分：开发环境搭建

### 2.1 环境要求
- Node.js >= 18.0.0
- Python >= 3.9
- Git 最新版本
- VS Code (推荐)

### 2.2 快速开始

#### 步骤1: 克隆项目
```bash
git clone <repository-url>
cd quant014
```

#### 步骤2: 前端环境
```bash
cd frontend
npm install
cp .env.example .env.local
npm run dev
```

#### 步骤3: 后端环境
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
python app/main.py
```

### 2.3 开发工具配置

#### VS Code扩展推荐
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- Python
- Pylance
- GitLens
- Prettier
- ESLint

#### 配置文件示例
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "python.defaultInterpreterPath": "./backend/venv/bin/python"
}
```

## 📝 第三部分：代码规范培训

### 3.1 前端代码规范

#### 组件命名规范
```typescript
// ✅ 正确：使用PascalCase
export default defineComponent({
  name: 'UserProfile'
})

// ❌ 错误：使用camelCase
export default defineComponent({
  name: 'userProfile'
})
```

#### 文件组织规范
```
components/
├── common/           # 通用组件
│   ├── AppButton/
│   │   ├── index.vue
│   │   └── types.ts
│   └── AppModal/
├── trading/          # 业务组件
└── charts/           # 图表组件
```

#### TypeScript最佳实践
```typescript
// ✅ 正确：明确的接口定义
interface UserInfo {
  id: string
  name: string
  email: string
  avatar?: string
}

// ✅ 正确：使用泛型
function createApiResponse<T>(data: T): ApiResponse<T> {
  return { success: true, data }
}

// ❌ 错误：使用any类型
function processData(data: any): any {
  return data
}
```

### 3.2 后端代码规范

#### API设计规范
```python
# ✅ 正确：清晰的路由和响应模型
@router.post("/users", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    """创建新用户"""
    return await user_service.create_user(db, user_data)

# ❌ 错误：缺少类型注解和文档
@router.post("/users")
async def create_user(user_data, db):
    return user_service.create_user(db, user_data)
```

#### 错误处理规范
```python
# ✅ 正确：统一的错误处理
try:
    result = await some_operation()
    return result
except ValidationError as e:
    raise HTTPException(
        status_code=400,
        detail=f"数据验证失败: {e}"
    )
except Exception as e:
    logger.error(f"操作失败: {e}")
    raise HTTPException(
        status_code=500,
        detail="服务器内部错误"
    )
```

## 🔄 第四部分：开发流程培训

### 4.1 Git工作流

#### 分支策略
```bash
# 主分支
main              # 生产环境代码
develop           # 开发环境代码

# 功能分支
feature/user-auth # 功能开发
hotfix/fix-login  # 紧急修复
release/v1.2.0    # 发布准备
```

#### 提交规范
```bash
# 提交格式
<type>(<scope>): <description>

# 示例
feat(auth): add user login functionality
fix(trading): resolve order submission bug
docs(api): update authentication documentation
style(ui): improve button hover effects
refactor(utils): optimize date formatting function
test(auth): add unit tests for login service
chore(deps): update dependencies to latest versions
```

### 4.2 代码审查流程

#### Pull Request模板
```markdown
## 变更描述
简要描述本次变更的内容和目的

## 变更类型
- [ ] 新功能
- [ ] Bug修复
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化

## 测试情况
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码符合规范
- [ ] 添加了必要的注释
- [ ] 更新了相关文档
- [ ] 没有引入新的警告
```

### 4.3 测试策略

#### 前端测试
```typescript
// 组件测试示例
import { mount } from '@vue/test-utils'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('should display user name', () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: { name: '张三', email: '<EMAIL>' }
      }
    })
    
    expect(wrapper.text()).toContain('张三')
  })
})
```

#### 后端测试
```python
# API测试示例
import pytest
from fastapi.testclient import TestClient

def test_create_user(client: TestClient):
    response = client.post(
        "/api/v1/users",
        json={
            "name": "张三",
            "email": "<EMAIL>",
            "password": "password123"
        }
    )
    
    assert response.status_code == 201
    assert response.json()["name"] == "张三"
```

## 🚨 第五部分：常见问题和解决方案

### 5.1 前端常见问题

#### 问题1: 组件无法正确导入
```typescript
// ❌ 错误：相对路径导入
import UserProfile from '../../../components/UserProfile.vue'

// ✅ 正确：使用别名导入
import UserProfile from '@/components/UserProfile.vue'
```

#### 问题2: 状态管理混乱
```typescript
// ❌ 错误：直接修改状态
store.user.name = 'new name'

// ✅ 正确：使用action修改状态
store.updateUserName('new name')
```

### 5.2 后端常见问题

#### 问题1: 数据库连接问题
```python
# 检查数据库配置
DATABASE_URL = "postgresql://user:password@localhost/dbname"

# 确保数据库服务运行
# 检查连接池配置
```

#### 问题2: API响应格式不一致
```python
# ✅ 正确：使用统一的响应格式
from app.schemas.base import BaseResponse

@router.get("/users", response_model=BaseResponse[List[UserResponse]])
async def get_users():
    users = await user_service.get_all_users()
    return BaseResponse(data=users)
```

## 📚 第六部分：学习资源

### 6.1 官方文档
- [Vue 3 官方文档](https://vuejs.org/)
- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Element Plus 文档](https://element-plus.org/)

### 6.2 推荐教程
- Vue 3 Composition API 深入指南
- FastAPI 实战教程
- TypeScript 进阶指南
- Python 异步编程最佳实践

### 6.3 工具和插件
- Vue DevTools
- Postman/Insomnia (API测试)
- DBeaver (数据库管理)
- Git GUI工具

## ✅ 第七部分：培训检查清单

### 新员工入职检查
- [ ] 开发环境搭建完成
- [ ] 项目代码成功运行
- [ ] 熟悉项目结构和架构
- [ ] 了解代码规范和最佳实践
- [ ] 掌握Git工作流程
- [ ] 完成第一个功能开发
- [ ] 通过代码审查
- [ ] 了解测试流程

### 持续学习计划
- [ ] 每周技术分享
- [ ] 代码审查参与
- [ ] 文档维护贡献
- [ ] 新技术调研
- [ ] 性能优化实践

---

**培训联系人**: 技术负责人
**培训时间**: 根据团队安排
**培训方式**: 线上/线下结合
