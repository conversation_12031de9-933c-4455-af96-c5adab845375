# 量化投资平台 - 项目结构规范

## 📁 项目总体结构

```
quant014/
├── 📁 frontend/           # 前端应用 (Vue 3 + TypeScript)
├── 📁 backend/            # 后端服务 (FastAPI + Python)
├── 📁 docs/               # 项目文档
├── 📁 scripts/            # 构建和部署脚本
├── 📁 k8s/                # Kubernetes 配置
├── 📁 docker/             # Docker 配置
├── 📁 tests/              # 集成测试
├── 📁 data/               # 数据文件
├── 📁 logs/               # 日志文件
├── 📄 README.md           # 项目说明
├── 📄 .gitignore          # Git 忽略规则
├── 📄 .env.example        # 环境变量模板
└── 📄 docker-compose.yml  # 容器编排
```

## 🎨 前端结构 (frontend/)

### 核心目录结构

```
frontend/
├── 📁 public/             # 静态资源
│   ├── favicon.ico
│   ├── manifest.json
│   └── icons/
├── 📁 src/                # 源代码
│   ├── 📁 api/            # API 接口层
│   ├── 📁 assets/         # 静态资源
│   ├── 📁 components/     # 可复用组件
│   ├── 📁 composables/    # 组合式函数
│   ├── 📁 config/         # 配置文件
│   ├── 📁 directives/     # 自定义指令
│   ├── 📁 layouts/        # 布局组件
│   ├── 📁 locales/        # 国际化
│   ├── 📁 plugins/        # 插件配置
│   ├── 📁 router/         # 路由配置
│   ├── 📁 services/       # 业务服务
│   ├── 📁 stores/         # 状态管理
│   ├── 📁 types/          # TypeScript 类型
│   ├── 📁 utils/          # 工具函数
│   ├── 📁 views/          # 页面组件
│   ├── 📁 workers/        # Web Workers
│   ├── 📄 App.vue         # 根组件
│   └── 📄 main.ts         # 入口文件
├── 📁 scripts/            # 构建脚本
├── 📄 package.json        # 依赖配置
├── 📄 vite.config.ts      # Vite 配置
├── 📄 tsconfig.json       # TypeScript 配置
└── 📄 .env.example        # 环境变量模板
```

### 组件组织规范

```
src/components/
├── 📁 common/             # 通用组件
│   ├── 📁 AppButton/      # 按钮组件
│   ├── 📁 AppCard/        # 卡片组件
│   └── 📁 AppModal/       # 模态框组件
├── 📁 charts/             # 图表组件
│   ├── 📁 KLineChart/     # K线图
│   ├── 📁 DepthChart/     # 深度图
│   └── 📁 TrendChart/     # 趋势图
├── 📁 trading/            # 交易相关
│   ├── 📁 OrderPanel/     # 下单面板
│   ├── 📁 PositionList/   # 持仓列表
│   └── 📁 TradeHistory/   # 交易历史
├── 📁 market/             # 行情相关
│   ├── 📁 StockList/      # 股票列表
│   ├── 📁 MarketDepth/    # 市场深度
│   └── 📁 NewsPanel/      # 新闻面板
├── 📁 strategy/           # 策略相关
│   ├── 📁 StrategyEditor/ # 策略编辑器
│   ├── 📁 BacktestPanel/  # 回测面板
│   └── 📁 PerformanceChart/ # 绩效图表
└── 📁 widgets/            # 小部件
    ├── 📁 Clock/          # 时钟
    ├── 📁 Weather/        # 天气
    └── 📁 Calculator/     # 计算器
```

### 页面组织规范

```
src/views/
├── 📁 Dashboard/          # 仪表板
│   ├── index.vue          # 主页面
│   ├── Overview.vue       # 概览
│   └── Analytics.vue      # 分析
├── 📁 Market/             # 行情中心
│   ├── index.vue          # 行情首页
│   ├── StockDetail.vue    # 股票详情
│   └── MarketAnalysis.vue # 市场分析
├── 📁 Trading/            # 交易中心
│   ├── index.vue          # 交易首页
│   ├── OrderManagement.vue # 订单管理
│   └── PositionManagement.vue # 持仓管理
├── 📁 Strategy/           # 策略中心
│   ├── index.vue          # 策略首页
│   ├── StrategyList.vue   # 策略列表
│   └── StrategyDetail.vue # 策略详情
├── 📁 Backtest/           # 回测中心
│   ├── index.vue          # 回测首页
│   ├── BacktestList.vue   # 回测列表
│   └── BacktestResult.vue # 回测结果
├── 📁 Portfolio/          # 投资组合
│   ├── index.vue          # 组合首页
│   ├── PortfolioList.vue  # 组合列表
│   └── PortfolioDetail.vue # 组合详情
├── 📁 Risk/               # 风险管理
│   ├── index.vue          # 风险首页
│   ├── RiskMonitor.vue    # 风险监控
│   └── RiskReport.vue     # 风险报告
├── 📁 Settings/           # 系统设置
│   ├── index.vue          # 设置首页
│   ├── UserProfile.vue    # 用户资料
│   └── SystemConfig.vue   # 系统配置
├── 📁 Auth/               # 认证相关
│   ├── Login.vue          # 登录
│   ├── Register.vue       # 注册
│   └── ForgotPassword.vue # 忘记密码
└── 📁 Error/              # 错误页面
    ├── 404.vue            # 404 错误
    ├── 500.vue            # 500 错误
    └── NetworkError.vue   # 网络错误
```

## 🔧 后端结构 (backend/)

### 核心目录结构

```
backend/
├── 📁 app/                # 应用代码
│   ├── 📁 api/            # API 路由
│   ├── 📁 core/           # 核心配置
│   ├── 📁 db/             # 数据库
│   ├── 📁 models/         # 数据模型
│   ├── 📁 schemas/        # Pydantic 模式
│   ├── 📁 services/       # 业务服务
│   ├── 📁 utils/          # 工具函数
│   ├── 📁 middleware/     # 中间件
│   ├── 📁 tasks/          # 异步任务
│   └── 📄 main.py         # 应用入口
├── 📁 alembic/            # 数据库迁移
├── 📁 tests/              # 测试代码
├── 📁 scripts/            # 脚本文件
├── 📁 data/               # 数据文件
├── 📁 logs/               # 日志文件
├── 📄 requirements.txt    # Python 依赖
├── 📄 Dockerfile          # Docker 配置
└── 📄 .env.example        # 环境变量模板
```

## 📋 文件命名规范

### 前端文件命名

- **组件文件**: PascalCase (如: `UserProfile.vue`)
- **页面文件**: PascalCase (如: `Dashboard.vue`)
- **工具文件**: camelCase (如: `formatUtils.ts`)
- **类型文件**: camelCase (如: `userTypes.ts`)
- **配置文件**: camelCase (如: `apiConfig.ts`)

### 后端文件命名

- **模块文件**: snake_case (如: `user_service.py`)
- **类名**: PascalCase (如: `UserService`)
- **函数名**: snake_case (如: `get_user_profile`)
- **常量**: UPPER_SNAKE_CASE (如: `API_VERSION`)

## 🎯 代码组织原则

### 1. 单一职责原则
- 每个文件/模块只负责一个功能
- 组件保持简洁，职责明确

### 2. 分层架构
- **表现层**: Views/Components
- **业务层**: Services/Composables
- **数据层**: API/Stores

### 3. 依赖注入
- 使用依赖注入减少耦合
- 便于测试和维护

### 4. 配置外部化
- 环境相关配置使用环境变量
- 业务配置集中管理

## 📝 文档规范

### 1. README 文件
- 每个主要目录都应有 README.md
- 说明目录用途和使用方法

### 2. 代码注释
- 复杂逻辑必须添加注释
- 公共 API 必须有 JSDoc/docstring

### 3. 类型定义
- TypeScript 类型定义要完整
- 接口要有清晰的注释

## 🔄 版本控制规范

### 分支策略
- `main`: 生产环境代码
- `develop`: 开发环境代码
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

### 提交规范
```
type(scope): description

feat: 新功能
fix: 修复
docs: 文档
style: 格式
refactor: 重构
test: 测试
chore: 构建
```

## 🚀 部署结构

### 环境分离
- `development`: 开发环境
- `staging`: 测试环境
- `production`: 生产环境

### 配置管理
- 使用环境变量管理配置
- 敏感信息使用密钥管理

---

**注意**: 此规范是项目的基础架构指南，所有团队成员都应遵循此规范进行开发。
