"""
策略执行器
负责执行用户策略，管理策略生命周期
"""

import asyncio
import sys
import traceback
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from uuid import UUID
import threading
import time
import importlib.util

from loguru import logger
from app.services.real_data_sources import TushareDataSource, AKShareDataSource
from app.monitoring.alerting import trigger_custom_alert, AlertSeverity


class StrategyContext:
    """策略执行上下文"""
    
    def __init__(self, strategy_id: UUID, parameters: Dict[str, Any]):
        self.strategy_id = strategy_id
        self.parameters = parameters
        self.portfolio = {}
        self.current_data = {}
        self.history = []
        self.signals = []
        self.is_running = False
        self.start_time = None
        self.last_update = None
        self.error_count = 0
        self.max_errors = 10
        
    def log(self, message: str, level: str = "INFO"):
        """记录策略日志"""
        logger.info(f"[Strategy {self.strategy_id}] {message}")
        
    def order(self, symbol: str, amount: int, order_type: str = "market"):
        """下单接口"""
        order = {
            'symbol': symbol,
            'amount': amount,
            'order_type': order_type,
            'timestamp': datetime.now().isoformat(),
            'strategy_id': str(self.strategy_id)
        }
        self.signals.append(order)
        logger.info(f"策略下单: {symbol} {amount} {order_type}")
        
    def get_price(self, symbol: str) -> Optional[float]:
        """获取股票价格"""
        if symbol in self.current_data:
            return self.current_data[symbol].get('price', 0)
        return None
        
    def get_data(self, symbol: str, days: int = 30) -> list:
        """获取历史数据"""
        # 简化实现，返回模拟数据
        return self.history[-days:] if self.history else []


class StrategyExecutor:
    """策略执行器"""
    
    def __init__(self):
        self.active_strategies: Dict[UUID, StrategyContext] = {}
        self.strategy_threads: Dict[UUID, threading.Thread] = {}
        self.data_sources = []
        
        # 初始化数据源
        self._init_data_sources()
        
        # 启动市场数据更新任务
        asyncio.create_task(self._market_data_update_loop())
        
    def _init_data_sources(self):
        """初始化数据源"""
        tushare_source = TushareDataSource()
        akshare_source = AKShareDataSource()
        
        if tushare_source.is_available():
            self.data_sources.append(tushare_source)
            logger.info("Tushare数据源已启用")
            
        if akshare_source.is_available():
            self.data_sources.append(akshare_source)
            logger.info("AKShare数据源已启用")
            
        if not self.data_sources:
            logger.warning("没有可用的数据源")
            
    async def _market_data_update_loop(self):
        """市场数据更新循环"""
        while True:
            try:
                # 为所有活跃策略更新市场数据
                if self.active_strategies:
                    await self._update_market_data()
                await asyncio.sleep(30)  # 每30秒更新一次
            except Exception as e:
                logger.error(f"市场数据更新出错: {e}")
                await asyncio.sleep(60)
                
    async def _update_market_data(self):
        """更新市场数据"""
        if not self.data_sources:
            return
            
        # 获取所有策略关注的股票列表
        symbols = set()
        for context in self.active_strategies.values():
            # 从策略参数中提取股票代码
            if 'symbols' in context.parameters:
                symbols.update(context.parameters['symbols'])
                
        # 默认更新一些主要股票
        default_symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        symbols.update(default_symbols)
        
        # 获取实时行情
        for symbol in list(symbols)[:10]:  # 限制数量避免过多API调用
            for data_source in self.data_sources:
                try:
                    quote = await data_source.get_realtime_quote(symbol)
                    if quote:
                        # 更新所有策略的市场数据
                        for context in self.active_strategies.values():
                            context.current_data[symbol] = quote
                            context.last_update = datetime.now()
                        break
                except Exception as e:
                    logger.error(f"获取 {symbol} 行情失败: {e}")
                    
    async def start_strategy(self, strategy_id: UUID, code: str, parameters: Dict[str, Any]) -> bool:
        """启动策略"""
        try:
            if strategy_id in self.active_strategies:
                logger.warning(f"策略 {strategy_id} 已在运行")
                return True
                
            # 创建策略上下文
            context = StrategyContext(strategy_id, parameters)
            context.is_running = True
            context.start_time = datetime.now()
            
            # 编译策略代码
            strategy_module = self._compile_strategy_code(code, strategy_id)
            if not strategy_module:
                return False
                
            self.active_strategies[strategy_id] = context
            
            # 在单独线程中运行策略
            strategy_thread = threading.Thread(
                target=self._run_strategy,
                args=(strategy_id, strategy_module, context),
                daemon=True
            )
            strategy_thread.start()
            self.strategy_threads[strategy_id] = strategy_thread
            
            logger.info(f"策略 {strategy_id} 启动成功")
            return True
            
        except Exception as e:
            logger.error(f"策略 {strategy_id} 启动失败: {e}")
            return False
            
    async def stop_strategy(self, strategy_id: UUID) -> bool:
        """停止策略"""
        try:
            if strategy_id not in self.active_strategies:
                logger.warning(f"策略 {strategy_id} 不在运行中")
                return True
                
            # 标记停止
            context = self.active_strategies[strategy_id]
            context.is_running = False
            
            # 等待线程结束
            if strategy_id in self.strategy_threads:
                thread = self.strategy_threads[strategy_id]
                thread.join(timeout=5)  # 最多等待5秒
                del self.strategy_threads[strategy_id]
                
            # 清理资源
            del self.active_strategies[strategy_id]
            
            logger.info(f"策略 {strategy_id} 已停止")
            return True
            
        except Exception as e:
            logger.error(f"策略 {strategy_id} 停止失败: {e}")
            return False
            
    def _compile_strategy_code(self, code: str, strategy_id: UUID):
        """编译策略代码"""
        try:
            # 创建模块
            spec = importlib.util.spec_from_loader(
                f"strategy_{strategy_id}", 
                loader=None
            )
            strategy_module = importlib.util.module_from_spec(spec)
            
            # 执行代码
            exec(code, strategy_module.__dict__)
            
            # 验证必要函数存在
            if not hasattr(strategy_module, 'initialize'):
                logger.error(f"策略 {strategy_id} 缺少 initialize 函数")
                return None
                
            if not hasattr(strategy_module, 'handle_data'):
                logger.error(f"策略 {strategy_id} 缺少 handle_data 函数")
                return None
                
            return strategy_module
            
        except Exception as e:
            logger.error(f"策略 {strategy_id} 编译失败: {e}")
            return None
            
    def _run_strategy(self, strategy_id: UUID, strategy_module, context: StrategyContext):
        """运行策略主循环"""
        try:
            # 初始化策略
            strategy_module.initialize(context)
            context.log("策略初始化完成")
            
            # 主循环
            while context.is_running:
                try:
                    # 调用策略的数据处理函数
                    strategy_module.handle_data(context)
                    
                    # 检查错误次数
                    if context.error_count >= context.max_errors:
                        context.log(f"错误次数达到上限 {context.max_errors}，停止策略", "ERROR")
                        trigger_custom_alert(
                            f"strategy_error_{strategy_id}",
                            f"策略 {strategy_id} 错误次数过多，已自动停止",
                            AlertSeverity.HIGH,
                            {"strategy_id": str(strategy_id), "error_count": context.error_count}
                        )
                        break
                        
                    # 策略执行间隔
                    time.sleep(60)  # 每分钟执行一次
                    
                except Exception as e:
                    context.error_count += 1
                    context.log(f"策略执行出错 ({context.error_count}/{context.max_errors}): {e}", "ERROR")
                    logger.error(f"策略 {strategy_id} 执行错误: {e}\n{traceback.format_exc()}")
                    
                    # 如果错误严重，触发告警
                    if context.error_count >= 3:
                        trigger_custom_alert(
                            f"strategy_error_{strategy_id}",
                            f"策略 {strategy_id} 频繁出错",
                            AlertSeverity.MEDIUM,
                            {"strategy_id": str(strategy_id), "error": str(e)}
                        )
                    
                    time.sleep(30)  # 出错后等待30秒再继续
                    
        except Exception as e:
            context.log(f"策略运行失败: {e}", "ERROR")
            logger.error(f"策略 {strategy_id} 运行失败: {e}")
            trigger_custom_alert(
                f"strategy_fatal_{strategy_id}",
                f"策略 {strategy_id} 发生致命错误",
                AlertSeverity.CRITICAL,
                {"strategy_id": str(strategy_id), "error": str(e)}
            )
        finally:
            context.is_running = False
            context.log("策略已停止")
            
    def get_strategy_status(self, strategy_id: UUID) -> Optional[Dict[str, Any]]:
        """获取策略状态"""
        if strategy_id not in self.active_strategies:
            return None
            
        context = self.active_strategies[strategy_id]
        return {
            "strategy_id": str(strategy_id),
            "is_running": context.is_running,
            "start_time": context.start_time.isoformat() if context.start_time else None,
            "last_update": context.last_update.isoformat() if context.last_update else None,
            "error_count": context.error_count,
            "signals_count": len(context.signals),
            "portfolio": context.portfolio
        }
        
    def get_all_strategies_status(self) -> Dict[str, Any]:
        """获取所有策略状态"""
        return {
            "total_active": len(self.active_strategies),
            "strategies": [
                self.get_strategy_status(strategy_id) 
                for strategy_id in self.active_strategies.keys()
            ]
        }


# 全局策略执行器实例
strategy_executor = StrategyExecutor()