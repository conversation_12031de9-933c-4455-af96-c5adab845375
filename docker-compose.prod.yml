version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: quant_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./frontend/dist:/usr/share/nginx/html:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - quant_network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: quant_frontend
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=https://api.yourdomain.com/api/v1
      - VITE_WS_BASE_URL=wss://api.yourdomain.com/api/v1/ws
    volumes:
      - frontend_dist:/app/dist
    restart: unless-stopped
    networks:
      - quant_network

  # 后端应用
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: quant_backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://quant_user:${DB_PASSWORD}@postgres:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
      - LOG_LEVEL=INFO
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ./logs/backend:/app/logs
      - ./data/uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - quant_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: quant_postgres
    environment:
      - POSTGRES_DB=quant_db
      - POSTGRES_USER=quant_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
      - ./logs/postgres:/var/log/postgresql
    ports:
      - "127.0.0.1:5432:5432"
    restart: unless-stopped
    networks:
      - quant_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quant_user -d quant_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: quant_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    ports:
      - "127.0.0.1:6379:6379"
    restart: unless-stopped
    networks:
      - quant_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: quant_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    restart: unless-stopped
    networks:
      - quant_network

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: quant_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - quant_network

  # 日志收集
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: quant_filebeat
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/app:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=${ELASTICSEARCH_HOSTS}
      - KIBANA_HOST=${KIBANA_HOST}
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - quant_network

  # 备份服务
  backup:
    build:
      context: ./scripts
      dockerfile: Dockerfile.backup
    container_name: quant_backup
    environment:
      - DB_HOST=postgres
      - DB_NAME=quant_db
      - DB_USER=quant_user
      - DB_PASSWORD=${DB_PASSWORD}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
      - S3_BUCKET=${S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    volumes:
      - ./backups:/backups
      - postgres_data:/var/lib/postgresql/data:ro
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - quant_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  frontend_dist:
    driver: local

networks:
  quant_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
