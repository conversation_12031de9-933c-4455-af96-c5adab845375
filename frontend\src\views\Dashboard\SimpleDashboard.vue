<template>
  <div class="simple-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h1>🚀 量化投资平台</h1>
      <p>专业级量化交易系统 - 实时数据驱动投资决策</p>
    </div>

    <!-- 核心指标 -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-icon">💰</div>
        <div class="metric-info">
          <h3>总资产</h3>
          <div class="metric-value">¥1,234,567.89</div>
          <div class="metric-change positive">+12.34%</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">📈</div>
        <div class="metric-info">
          <h3>今日收益</h3>
          <div class="metric-value">¥8,765.43</div>
          <div class="metric-change positive">+2.15%</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">📊</div>
        <div class="metric-info">
          <h3>持仓数量</h3>
          <div class="metric-value">15</div>
          <div class="metric-change neutral">持股</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">⚡</div>
        <div class="metric-info">
          <h3>策略收益</h3>
          <div class="metric-value">¥45,678.90</div>
          <div class="metric-change positive">+18.76%</div>
        </div>
      </div>
    </div>

    <!-- 功能模块 -->
    <div class="feature-modules">
      <h2>核心功能</h2>
      <div class="modules-grid">
        <router-link to="/market" class="module-card">
          <div class="module-icon">📈</div>
          <h3>市场行情</h3>
          <p>实时股票价格、K线图表、技术指标分析</p>
        </router-link>

        <router-link to="/trading" class="module-card">
          <div class="module-icon">💰</div>
          <h3>智能交易</h3>
          <p>算法交易、订单管理、风险控制</p>
        </router-link>

        <router-link to="/strategy" class="module-card">
          <div class="module-icon">🧠</div>
          <h3>策略研发</h3>
          <p>量化策略开发、回测验证、参数优化</p>
        </router-link>

        <router-link to="/portfolio" class="module-card">
          <div class="module-icon">📋</div>
          <h3>投资组合</h3>
          <p>资产配置、风险分析、收益追踪</p>
        </router-link>

        <router-link to="/backtest" class="module-card">
          <div class="module-icon">🔄</div>
          <h3>策略回测</h3>
          <p>历史数据验证、性能评估、风险测试</p>
        </router-link>

        <router-link to="/risk" class="module-card">
          <div class="module-icon">🛡️</div>
          <h3>风险管理</h3>
          <p>风险监控、止损设置、资金管理</p>
        </router-link>
      </div>
    </div>

    <!-- 市场概览 -->
    <div class="market-overview">
      <h2>市场概览</h2>
      <div class="market-indices">
        <div class="index-item">
          <span class="index-name">上证指数</span>
          <span class="index-value">3,234.56</span>
          <span class="index-change positive">+1.23%</span>
        </div>
        <div class="index-item">
          <span class="index-name">深证成指</span>
          <span class="index-value">12,345.67</span>
          <span class="index-change negative">-0.45%</span>
        </div>
        <div class="index-item">
          <span class="index-name">创业板指</span>
          <span class="index-value">2,567.89</span>
          <span class="index-change positive">+2.10%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const totalAssets = ref(1234567.89)
const todayProfit = ref(8765.43)
const positions = ref(15)
const strategyProfit = ref(45678.90)

onMounted(() => {
  console.log('📊 仪表盘已加载')
})
</script>

<style scoped>
.simple-dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-section h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-section p {
  font-size: 1.2rem;
  color: #6b7280;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  color: white;
}

.metric-info h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 0.9rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 0.25rem;
}

.metric-change {
  font-size: 0.9rem;
  font-weight: 500;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

.metric-change.neutral {
  color: #6b7280;
}

.feature-modules {
  margin-bottom: 3rem;
}

.feature-modules h2 {
  margin-bottom: 1.5rem;
  color: #374151;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.module-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.module-card h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.module-card p {
  color: #6b7280;
  margin: 0;
  font-size: 0.9rem;
}

.market-overview h2 {
  margin-bottom: 1.5rem;
  color: #374151;
}

.market-indices {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.index-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.index-name {
  font-weight: 500;
  color: #374151;
}

.index-value {
  font-weight: bold;
  color: #111827;
}

.index-change {
  font-size: 0.9rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .simple-dashboard {
    padding: 1rem;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
}
</style>
