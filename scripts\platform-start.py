#!/usr/bin/env python3
"""
量化投资平台 - 统一跨平台启动脚本
支持 Windows, Linux, MacOS 和 Docker 容器环境
"""

import os
import sys
import json
import time
import subprocess
import platform
import argparse
from pathlib import Path
from typing import Dict, List, Optional

# 配置文件路径
CONFIG_FILE = Path(__file__).parent / "platform-config.json"

class PlatformManager:
    def __init__(self):
        self.platform = platform.system().lower()
        self.config = self.load_config()
        self.project_root = Path(__file__).parent.parent
        
    def load_config(self) -> Dict:
        """加载平台配置"""
        default_config = {
            "environments": {
                "development": {
                    "backend_port": 8000,
                    "frontend_port": 5173,
                    "database_url": "sqlite:///./data/quant.db",
                    "redis_url": "redis://localhost:6379/0"
                },
                "production": {
                    "backend_port": 8000,
                    "frontend_port": 80,
                    "database_url": "******************************/quant",
                    "redis_url": "redis://redis:6379/0"
                }
            },
            "services": {
                "backend": {
                    "path": "backend",
                    "start_cmd": {
                        "windows": "python app/main.py",
                        "linux": "python3 app/main.py",
                        "docker": "python app/main.py"
                    },
                    "health_check": "/health"
                },
                "frontend": {
                    "path": "frontend",
                    "start_cmd": {
                        "windows": "npm run dev",
                        "linux": "npm run dev",
                        "docker": "npm run serve"
                    },
                    "health_check": "/"
                }
            }
        }
        
        if CONFIG_FILE.exists():
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Warning: Failed to load config file: {e}")
        
        return default_config
    
    def detect_environment(self) -> str:
        """自动检测运行环境"""
        if os.getenv('DOCKER_ENV'):
            return 'docker'
        elif os.getenv('KUBERNETES_SERVICE_HOST'):
            return 'kubernetes'
        elif os.getenv('CI'):
            return 'ci'
        else:
            return 'local'
    
    def check_dependencies(self, service: str) -> bool:
        """检查服务依赖"""
        service_path = self.project_root / self.config['services'][service]['path']
        
        if service == 'backend':
            # 检查Python和依赖
            try:
                subprocess.run([sys.executable, '--version'], check=True, capture_output=True)
                if (service_path / 'requirements.txt').exists():
                    print(f"✓ Found Python requirements for {service}")
                return True
            except subprocess.CalledProcessError:
                print(f"✗ Python not found for {service}")
                return False
                
        elif service == 'frontend':
            # 检查Node.js和依赖
            try:
                subprocess.run(['node', '--version'], check=True, capture_output=True)
                subprocess.run(['npm', '--version'], check=True, capture_output=True)
                if (service_path / 'package.json').exists():
                    print(f"✓ Found Node.js requirements for {service}")
                return True
            except subprocess.CalledProcessError:
                print(f"✗ Node.js/npm not found for {service}")
                return False
        
        return False
    
    def install_dependencies(self, service: str):
        """安装服务依赖"""
        service_path = self.project_root / self.config['services'][service]['path']
        os.chdir(service_path)
        
        if service == 'backend':
            print(f"Installing Python dependencies for {service}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        elif service == 'frontend':
            print(f"Installing Node.js dependencies for {service}...")
            subprocess.run(['npm', 'install'], check=True)
    
    def start_service(self, service: str, env: str = 'development') -> subprocess.Popen:
        """启动服务"""
        service_config = self.config['services'][service]
        service_path = self.project_root / service_config['path']
        
        # 选择适当的启动命令
        if self.detect_environment() == 'docker':
            cmd = service_config['start_cmd']['docker']
        elif self.platform == 'windows':
            cmd = service_config['start_cmd']['windows']
        else:
            cmd = service_config['start_cmd']['linux']
        
        # 设置环境变量
        env_vars = os.environ.copy()
        env_config = self.config['environments'][env]
        
        if service == 'backend':
            env_vars.update({
                'DATABASE_URL': env_config['database_url'],
                'REDIS_URL': env_config['redis_url'],
                'PORT': str(env_config['backend_port'])
            })
        elif service == 'frontend':
            env_vars.update({
                'VITE_API_URL': f"http://localhost:{env_config['backend_port']}",
                'PORT': str(env_config['frontend_port'])
            })
        
        print(f"Starting {service} with command: {cmd}")
        os.chdir(service_path)
        
        if self.platform == 'windows':
            return subprocess.Popen(cmd.split(), env=env_vars, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            return subprocess.Popen(cmd.split(), env=env_vars)
    
    def check_health(self, service: str, port: int) -> bool:
        """检查服务健康状态"""
        import urllib.request
        import urllib.error
        
        health_endpoint = self.config['services'][service]['health_check']
        url = f"http://localhost:{port}{health_endpoint}"
        
        try:
            with urllib.request.urlopen(url, timeout=5) as response:
                return response.status == 200
        except urllib.error.URLError:
            return False
    
    def start_platform(self, env: str = 'development', services: Optional[List[str]] = None):
        """启动整个平台"""
        if services is None:
            services = ['backend', 'frontend']
        
        processes = []
        env_config = self.config['environments'][env]
        
        print(f"🚀 Starting Quant Platform in {env} mode on {self.platform}...")
        print("=" * 60)
        
        # 检查并安装依赖
        for service in services:
            print(f"\n📋 Checking dependencies for {service}...")
            if not self.check_dependencies(service):
                try:
                    self.install_dependencies(service)
                except subprocess.CalledProcessError as e:
                    print(f"❌ Failed to install dependencies for {service}: {e}")
                    return
        
        # 启动服务
        for service in services:
            try:
                print(f"\n🔄 Starting {service}...")
                process = self.start_service(service, env)
                processes.append((service, process))
                time.sleep(3)  # 等待服务启动
            except Exception as e:
                print(f"❌ Failed to start {service}: {e}")
                self.cleanup_processes(processes)
                return
        
        # 健康检查
        print(f"\n🔍 Performing health checks...")
        for service in services:
            if service == 'backend':
                port = env_config['backend_port']
            elif service == 'frontend':
                port = env_config['frontend_port']
            
            print(f"Checking {service} at port {port}...")
            for attempt in range(12):  # 60秒超时
                if self.check_health(service, port):
                    print(f"✅ {service} is healthy")
                    break
                time.sleep(5)
                print(f"⏳ Waiting for {service} to be ready... ({attempt + 1}/12)")
            else:
                print(f"⚠️ {service} health check failed, but continuing...")
        
        # 显示服务信息
        print(f"\n🎉 Platform started successfully!")
        print("=" * 60)
        print(f"Frontend: http://localhost:{env_config['frontend_port']}")
        print(f"Backend API: http://localhost:{env_config['backend_port']}")
        print(f"Environment: {env}")
        print(f"Platform: {self.platform}")
        
        if env == 'development':
            print(f"\n📱 Opening browser...")
            try:
                if self.platform == 'windows':
                    subprocess.run(['start', f"http://localhost:{env_config['frontend_port']}"], shell=True)
                elif self.platform == 'darwin':  # macOS
                    subprocess.run(['open', f"http://localhost:{env_config['frontend_port']}"])
                else:  # Linux
                    subprocess.run(['xdg-open', f"http://localhost:{env_config['frontend_port']}"])
            except:
                pass
        
        print(f"\nPress Ctrl+C to stop all services")
        
        try:
            # 等待用户中断
            while True:
                time.sleep(1)
                # 检查进程是否还在运行
                for service, process in processes:
                    if process.poll() is not None:
                        print(f"⚠️ {service} process ended unexpectedly")
        except KeyboardInterrupt:
            print(f"\n🛑 Shutting down platform...")
            self.cleanup_processes(processes)
    
    def cleanup_processes(self, processes: List):
        """清理进程"""
        for service, process in processes:
            try:
                print(f"Stopping {service}...")
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print(f"Force killing {service}...")
                process.kill()
            except Exception as e:
                print(f"Error stopping {service}: {e}")
    
    def docker_start(self, env: str = 'development'):
        """使用Docker启动"""
        compose_file = self.project_root / 'docker-compose.yml'
        if env == 'production':
            compose_file = self.project_root / 'docker-compose.prod.yml'
        
        if not compose_file.exists():
            print(f"❌ Docker compose file not found: {compose_file}")
            return
        
        print(f"🐳 Starting platform with Docker ({env})...")
        try:
            subprocess.run([
                'docker-compose', 
                '-f', str(compose_file), 
                'up', '--build', '-d'
            ], check=True)
            print("✅ Platform started with Docker")
            print("Frontend: http://localhost:5173")
            print("Backend: http://localhost:8000")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start with Docker: {e}")

def main():
    parser = argparse.ArgumentParser(description='量化投资平台统一启动脚本')
    parser.add_argument('--env', choices=['development', 'production'], default='development',
                       help='运行环境 (默认: development)')
    parser.add_argument('--services', nargs='+', choices=['backend', 'frontend'], 
                       help='要启动的服务 (默认: 全部)')
    parser.add_argument('--docker', action='store_true', help='使用Docker启动')
    parser.add_argument('--check', action='store_true', help='只检查依赖，不启动服务')
    
    args = parser.parse_args()
    
    manager = PlatformManager()
    
    if args.check:
        print("🔍 Checking platform dependencies...")
        services = args.services or ['backend', 'frontend']
        all_ok = True
        for service in services:
            if not manager.check_dependencies(service):
                all_ok = False
        if all_ok:
            print("✅ All dependencies are satisfied")
        else:
            print("❌ Some dependencies are missing")
            sys.exit(1)
    elif args.docker:
        manager.docker_start(args.env)
    else:
        manager.start_platform(args.env, args.services)

if __name__ == '__main__':
    main()