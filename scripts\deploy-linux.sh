#!/bin/bash
# 量化投资平台 - Linux 生产环境部署脚本

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly CONFIG_FILE="${PROJECT_ROOT}/.env"
readonly BACKUP_DIR="${PROJECT_ROOT}/backups"

# 默认值
ENVIRONMENT="production"
COMPOSE_FILE="docker/docker-compose.linux.yml"
BACKUP_ENABLED=true
MIGRATION_ENABLED=true
HEALTH_CHECK_TIMEOUT=300
DRY_RUN=false

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 显示帮助
show_help() {
    cat << EOF
量化投资平台 Linux 生产环境部署脚本

用法: $0 [选项]

选项:
    -e, --env ENV           部署环境 (production|staging) [默认: production]
    -f, --file FILE         Docker Compose 文件 [默认: docker/docker-compose.linux.yml]
    -b, --no-backup         跳过备份
    -m, --no-migration      跳过数据库迁移
    -t, --timeout SECONDS  健康检查超时时间 [默认: 300]
    -d, --dry-run          预演模式，不实际执行
    -h, --help             显示此帮助信息

环境变量:
    DATA_PATH              数据存储路径
    SECRET_KEY             应用密钥
    JWT_SECRET_KEY         JWT 密钥
    POSTGRES_PASSWORD      数据库密码
    GRAFANA_PASSWORD       Grafana 管理员密码

示例:
    $0                              # 标准生产部署
    $0 -e staging                   # 部署到预生产环境
    $0 -f docker-compose.prod.yml   # 使用自定义配置文件
    $0 -d                          # 预演模式
    
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -f|--file)
                COMPOSE_FILE="$2"
                shift 2
                ;;
            -b|--no-backup)
                BACKUP_ENABLED=false
                shift
                ;;
            -m|--no-migration)
                MIGRATION_ENABLED=false
                shift
                ;;
            -t|--timeout)
                HEALTH_CHECK_TIMEOUT="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查系统要求
check_system_requirements() {
    log "检查系统要求..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查磁盘空间
    local available_space=$(df "${PROJECT_ROOT}" | awk 'NR==2 {print $4}')
    local required_space=$((5 * 1024 * 1024))  # 5GB in KB
    
    if [[ $available_space -lt $required_space ]]; then
        log_error "磁盘空间不足。需要至少 5GB，当前可用: $(($available_space / 1024 / 1024))GB"
        exit 1
    fi
    
    # 检查内存
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    local required_memory=2048
    
    if [[ $available_memory -lt $required_memory ]]; then
        log_warning "可用内存较少: ${available_memory}MB，建议至少 ${required_memory}MB"
    fi
    
    log_success "系统要求检查通过"
}

# 加载环境变量
load_environment() {
    log "加载环境配置..."
    
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        log_success "已加载环境配置文件: $CONFIG_FILE"
    else
        log_warning "环境配置文件不存在: $CONFIG_FILE"
    fi
    
    # 检查必需的环境变量
    local required_vars=("SECRET_KEY" "JWT_SECRET_KEY" "POSTGRES_PASSWORD")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必需的环境变量: ${missing_vars[*]}"
        log "请在 $CONFIG_FILE 中设置这些变量"
        exit 1
    fi
    
    # 设置默认值
    export DATA_PATH="${DATA_PATH:-${PROJECT_ROOT}/data}"
    export UID="${UID:-$(id -u)}"
    export GID="${GID:-$(id -g)}"
    
    log_success "环境配置加载完成"
}

# 创建必要的目录
create_directories() {
    log "创建必要的目录..."
    
    local dirs=(
        "$DATA_PATH"
        "$DATA_PATH/postgres"
        "$DATA_PATH/redis"
        "$DATA_PATH/prometheus"
        "$DATA_PATH/grafana"
        "${PROJECT_ROOT}/logs"
        "${PROJECT_ROOT}/logs/nginx"
        "${PROJECT_ROOT}/logs/nginx-lb"
        "$BACKUP_DIR"
    )
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log "创建目录: $dir"
        fi
    done
    
    # 设置权限
    chown -R "${UID}:${GID}" "$DATA_PATH" "${PROJECT_ROOT}/logs" "$BACKUP_DIR" 2>/dev/null || true
    
    log_success "目录创建完成"
}

# 备份当前部署
backup_current_deployment() {
    if [[ "$BACKUP_ENABLED" == "false" ]]; then
        log "跳过备份"
        return
    fi
    
    log "创建部署备份..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="deployment_backup_${backup_timestamp}"
    local backup_path="${BACKUP_DIR}/${backup_name}"
    
    mkdir -p "$backup_path"
    
    # 备份 Docker Compose 配置
    if docker-compose -f "$COMPOSE_FILE" ps -q &>/dev/null; then
        docker-compose -f "$COMPOSE_FILE" config > "${backup_path}/docker-compose.yml"
    fi
    
    # 备份数据库
    if docker ps -q -f name=quant-postgres &>/dev/null; then
        log "备份 PostgreSQL 数据库..."
        docker exec quant-postgres pg_dump -U quant_user -d quant_db | gzip > "${backup_path}/database.sql.gz"
    fi
    
    # 备份配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        cp "$CONFIG_FILE" "${backup_path}/"
    fi
    
    # 创建备份清单
    cat > "${backup_path}/backup_info.txt" << EOF
备份时间: $(date)
环境: $ENVIRONMENT
Git 提交: $(git rev-parse HEAD 2>/dev/null || echo "N/A")
Docker 镜像:
$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}" | grep quant-platform || echo "无镜像")
EOF
    
    log_success "备份已创建: $backup_path"
    
    # 清理旧备份（保留最近 10 个）
    cd "$BACKUP_DIR"
    ls -t | grep "deployment_backup_" | tail -n +11 | xargs rm -rf 2>/dev/null || true
}

# 拉取最新镜像
pull_images() {
    log "拉取最新 Docker 镜像..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "预演模式：将拉取镜像"
        return
    fi
    
    docker-compose -f "$COMPOSE_FILE" pull
    
    log_success "镜像拉取完成"
}

# 执行数据库迁移
run_migrations() {
    if [[ "$MIGRATION_ENABLED" == "false" ]]; then
        log "跳过数据库迁移"
        return
    fi
    
    log "执行数据库迁移..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "预演模式：将执行数据库迁移"
        return
    fi
    
    # 确保 PostgreSQL 服务在运行
    if ! docker ps -q -f name=quant-postgres &>/dev/null; then
        log "启动 PostgreSQL 服务..."
        docker-compose -f "$COMPOSE_FILE" up -d postgres
        
        # 等待数据库就绪
        log "等待数据库服务就绪..."
        for i in {1..30}; do
            if docker exec quant-postgres pg_isready -U quant_user -d quant_db &>/dev/null; then
                break
            fi
            sleep 2
            if [[ $i -eq 30 ]]; then
                log_error "数据库服务启动超时"
                exit 1
            fi
        done
    fi
    
    # 运行迁移
    docker-compose -f "$COMPOSE_FILE" run --rm backend python -m alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 部署服务
deploy_services() {
    log "部署服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "预演模式：将使用以下配置部署:"
        docker-compose -f "$COMPOSE_FILE" config
        return
    fi
    
    # 启动服务（滚动更新）
    docker-compose -f "$COMPOSE_FILE" up -d --remove-orphans
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "预演模式：将执行健康检查"
        return
    fi
    
    local start_time=$(date +%s)
    local timeout=$HEALTH_CHECK_TIMEOUT
    
    # 检查后端服务
    log "检查后端服务..."
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            log_error "后端服务健康检查超时"
            return 1
        fi
        
        if curl -f -s http://localhost:8000/health &>/dev/null; then
            log_success "后端服务健康"
            break
        fi
        
        log "等待后端服务就绪... (${elapsed}s/${timeout}s)"
        sleep 10
    done
    
    # 检查前端服务
    log "检查前端服务..."
    start_time=$(date +%s)
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            log_error "前端服务健康检查超时"
            return 1
        fi
        
        if curl -f -s http://localhost:80/health &>/dev/null; then
            log_success "前端服务健康"
            break
        fi
        
        log "等待前端服务就绪... (${elapsed}s/${timeout}s)"
        sleep 10
    done
    
    # 检查数据库服务
    if docker exec quant-postgres pg_isready -U quant_user -d quant_db &>/dev/null; then
        log_success "数据库服务健康"
    else
        log_error "数据库服务不健康"
        return 1
    fi
    
    # 检查 Redis 服务
    if docker exec quant-redis redis-cli ping | grep -q PONG; then
        log_success "Redis 服务健康"
    else
        log_error "Redis 服务不健康"
        return 1
    fi
    
    log_success "所有服务健康检查通过"
}

# 显示部署状态
show_deployment_status() {
    log "部署状态:"
    echo "=============================================="
    
    # 显示服务状态
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    echo "服务地址:"
    echo "  前端: http://localhost:80"
    echo "  后端API: http://localhost:8000"
    echo "  监控面板: http://localhost:3001 (如启用)"
    echo "  Prometheus: http://localhost:9090 (如启用)"
    
    echo ""
    echo "日志查看:"
    echo "  所有服务: docker-compose -f $COMPOSE_FILE logs -f"
    echo "  后端: docker-compose -f $COMPOSE_FILE logs -f backend"
    echo "  前端: docker-compose -f $COMPOSE_FILE logs -f frontend"
    
    echo ""
    echo "数据路径: $DATA_PATH"
    echo "日志路径: ${PROJECT_ROOT}/logs"
    echo "备份路径: $BACKUP_DIR"
    echo "=============================================="
}

# 回滚部署
rollback_deployment() {
    log_error "部署失败，开始回滚..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "预演模式：将回滚部署"
        return
    fi
    
    # 查找最新的备份
    local latest_backup=$(ls -t "$BACKUP_DIR" | grep "deployment_backup_" | head -n1)
    
    if [[ -z "$latest_backup" ]]; then
        log_error "未找到备份，无法回滚"
        return 1
    fi
    
    log "使用备份回滚: $latest_backup"
    
    local backup_path="${BACKUP_DIR}/${latest_backup}"
    
    # 恢复配置
    if [[ -f "${backup_path}/docker-compose.yml" ]]; then
        cp "${backup_path}/docker-compose.yml" "${PROJECT_ROOT}/docker-compose.rollback.yml"
        docker-compose -f "${PROJECT_ROOT}/docker-compose.rollback.yml" up -d
    fi
    
    # 恢复数据库
    if [[ -f "${backup_path}/database.sql.gz" ]]; then
        log "恢复数据库..."
        zcat "${backup_path}/database.sql.gz" | docker exec -i quant-postgres psql -U quant_user -d quant_db
    fi
    
    log_success "回滚完成"
}

# 清理资源
cleanup() {
    log "清理资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷（谨慎）
    # docker volume prune -f
    
    log_success "清理完成"
}

# 主函数
main() {
    log "开始量化投资平台 Linux 部署"
    log "环境: $ENVIRONMENT"
    log "配置文件: $COMPOSE_FILE"
    
    cd "$PROJECT_ROOT"
    
    # 检查系统要求
    check_system_requirements
    
    # 加载环境配置
    load_environment
    
    # 创建目录
    create_directories
    
    # 备份当前部署
    backup_current_deployment
    
    # 拉取镜像
    pull_images
    
    # 执行数据库迁移
    run_migrations
    
    # 部署服务
    deploy_services
    
    # 健康检查
    if ! health_check; then
        rollback_deployment
        exit 1
    fi
    
    # 清理资源
    cleanup
    
    # 显示状态
    show_deployment_status
    
    log_success "🎉 部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误"; rollback_deployment; exit 1' ERR

# 解析参数并运行主函数
parse_args "$@"
main