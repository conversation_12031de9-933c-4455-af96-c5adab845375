const puppeteer = require('puppeteer');

async function quickTest5173() {
    console.log('🔍 快速测试 localhost:5173...');
    
    let browser;
    try {
        browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1280, height: 720 }
        });
        
        const page = await browser.newPage();
        
        const errors = [];
        const networkErrors = [];
        
        page.on('console', msg => {
            if (!msg.text().includes('[vite]')) {
                console.log(`📝 ${msg.text()}`);
            }
        });
        
        page.on('pageerror', error => {
            errors.push(error.message);
            console.error(`❌ 页面错误: ${error.message}`);
        });
        
        page.on('requestfailed', request => {
            networkErrors.push(`${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
        });
        
        console.log('\n=== 访问 localhost:5173 ===');
        
        await page.goto('http://localhost:5173', { 
            waitUntil: 'networkidle0',
            timeout: 30000
        });
        
        const title = await page.title();
        const content = await page.$eval('body', el => el.textContent || '').catch(() => '');
        
        console.log(`📋 页面标题: ${title}`);
        console.log(`📄 页面内容: ${content.substring(0, 200)}...`);
        
        // 查找按钮和链接
        const buttons = await page.$$('button');
        const links = await page.$$('a');
        
        console.log(`\n=== 页面元素 ===`);
        console.log(`🔘 按钮数量: ${buttons.length}`);
        console.log(`🔗 链接数量: ${links.length}`);
        
        // 测试"启动完整应用"按钮
        if (buttons.length > 0) {
            console.log('\n=== 测试按钮点击 ===');
            try {
                const buttonText = await buttons[0].evaluate(el => el.textContent);
                console.log(`🖱️ 点击按钮: "${buttonText}"`);
                
                await buttons[0].click();
                
                // 等待页面变化
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                const newContent = await page.$eval('body', el => el.textContent || '').catch(() => '');
                console.log(`📄 点击后内容: ${newContent.substring(0, 200)}...`);
                
                // 再次检查页面元素
                const newButtons = await page.$$('button');
                const newLinks = await page.$$('a');
                
                console.log(`🔘 点击后按钮数量: ${newButtons.length}`);
                console.log(`🔗 点击后链接数量: ${newLinks.length}`);
                
                // 如果有导航链接，测试几个
                if (newLinks.length > 0) {
                    console.log('\n=== 测试导航链接 ===');
                    for (let i = 0; i < Math.min(3, newLinks.length); i++) {
                        try {
                            const linkText = await newLinks[i].evaluate(el => el.textContent?.trim());
                            const linkHref = await newLinks[i].evaluate(el => el.href);
                            
                            if (linkHref && linkHref.includes('localhost:5173')) {
                                console.log(`🧭 测试链接: "${linkText}" -> ${linkHref}`);
                                
                                await newLinks[i].click();
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                
                                const linkContent = await page.$eval('body', el => el.textContent || '').catch(() => '');
                                console.log(`   ✅ 导航成功，内容长度: ${linkContent.length}`);
                                
                                // 截图每个导航页面
                                await page.screenshot({ path: `test_5173_page_${i}.png` });
                                
                                // 返回首页
                                await page.goto('http://localhost:5173');
                                await buttons[0].click(); // 重新点击启动按钮
                                await new Promise(resolve => setTimeout(resolve, 2000));
                            }
                        } catch (navError) {
                            console.log(`   ❌ 导航失败: ${navError.message}`);
                        }
                    }
                }
                
            } catch (clickError) {
                console.log(`❌ 按钮点击失败: ${clickError.message}`);
            }
        }
        
        // 最终截图
        await page.screenshot({ path: 'test_5173_final.png', fullPage: true });
        
        console.log('\n=== 测试总结 ===');
        console.log(`📊 发现的问题:`);
        console.log(`   - 页面错误: ${errors.length}`);
        console.log(`   - 网络错误: ${networkErrors.length}`);
        
        errors.forEach(error => console.log(`   ❌ ${error}`));
        networkErrors.forEach(error => console.log(`   🌐 ${error}`));
        
        if (content.includes('量化投资平台')) {
            console.log('✅ 平台基本可访问');
        } else {
            console.log('❌ 平台内容异常');
        }
        
        if (buttons.length > 0) {
            console.log('✅ 发现交互元素');
        } else {
            console.log('❌ 缺少交互元素');
        }
        
        console.log('\n🎯 主要发现:');
        console.log('1. 页面需要点击"启动完整应用"按钮才能使用');
        console.log('2. 这是一个启动页面，不是完整的应用界面');
        console.log('3. 需要进一步测试点击后的完整功能');
        
        return {
            title,
            hasContent: content.length > 0,
            buttonCount: buttons.length,
            linkCount: links.length,
            errorCount: errors.length,
            networkErrorCount: networkErrors.length
        };
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return null;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

quickTest5173().then(result => {
    console.log('\n✅ 测试完成');
    if (result) {
        console.log('📋 结果:', JSON.stringify(result, null, 2));
    }
}).catch(console.error);