<template>
  <div class="advanced-trading-panel">
    <!-- 交易模式选择 -->
    <div class="trading-mode-selector">
      <el-segmented v-model="tradingMode" :options="tradingModes" />
    </div>

    <!-- 单笔交易 -->
    <div v-if="tradingMode === 'single'" class="single-trading">
      <SingleOrderForm @order-submitted="handleOrderSubmitted" />
    </div>

    <!-- 批量交易 -->
    <div v-if="tradingMode === 'batch'" class="batch-trading">
      <BatchOrderPanel @orders-submitted="handleBatchOrdersSubmitted" />
    </div>

    <!-- 条件交易 -->
    <div v-if="tradingMode === 'conditional'" class="conditional-trading">
      <ConditionalOrderPanel @condition-order-created="handleConditionalOrderCreated" />
    </div>

    <!-- 策略交易 -->
    <div v-if="tradingMode === 'strategy'" class="strategy-trading">
      <StrategyTradingPanel @strategy-activated="handleStrategyActivated" />
    </div>

    <!-- 算法交易 -->
    <div v-if="tradingMode === 'algorithm'" class="algorithm-trading">
      <AlgorithmTradingPanel @algorithm-started="handleAlgorithmStarted" />
    </div>

    <!-- 交易历史和状态 -->
    <div class="trading-status">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="活跃订单" name="active">
          <ActiveOrdersList :orders="activeOrders" @order-action="handleOrderAction" />
        </el-tab-pane>
        
        <el-tab-pane label="条件单" name="conditional">
          <ConditionalOrdersList :orders="conditionalOrders" @condition-action="handleConditionAction" />
        </el-tab-pane>
        
        <el-tab-pane label="策略状态" name="strategies">
          <StrategyStatusList :strategies="activeStrategies" @strategy-action="handleStrategyAction" />
        </el-tab-pane>
        
        <el-tab-pane label="交易历史" name="history">
          <TradingHistory :history="tradingHistory" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import SingleOrderForm from './SingleOrderForm.vue'
import BatchOrderPanel from './BatchOrderPanel.vue'
import ConditionalOrderPanel from './ConditionalOrderPanel.vue'
import StrategyTradingPanel from './StrategyTradingPanel.vue'
import AlgorithmTradingPanel from './AlgorithmTradingPanel.vue'
import ActiveOrdersList from './ActiveOrdersList.vue'
import ConditionalOrdersList from './ConditionalOrdersList.vue'
import StrategyStatusList from './StrategyStatusList.vue'
import TradingHistory from './TradingHistory.vue'
import apiClient from '@/api/optimized-client'

// 交易模式
const tradingModes = [
  { label: '单笔交易', value: 'single' },
  { label: '批量交易', value: 'batch' },
  { label: '条件交易', value: 'conditional' },
  { label: '策略交易', value: 'strategy' },
  { label: '算法交易', value: 'algorithm' }
]

// 响应式数据
const tradingMode = ref('single')
const activeTab = ref('active')
const activeOrders = ref([])
const conditionalOrders = ref([])
const activeStrategies = ref([])
const tradingHistory = ref([])

// 事件处理
const handleOrderSubmitted = async (orderData: any) => {
  try {
    const response = await apiClient.post('/trading/orders', orderData)
    if (response.success) {
      ElMessage.success('订单提交成功')
      await refreshActiveOrders()
    } else {
      ElMessage.error(response.message || '订单提交失败')
    }
  } catch (error) {
    ElMessage.error('订单提交失败，请重试')
  }
}

const handleBatchOrdersSubmitted = async (ordersData: any[]) => {
  try {
    const response = await apiClient.post('/trading/batch-orders', { orders: ordersData })
    if (response.success) {
      ElNotification({
        title: '批量订单提交成功',
        message: `成功提交 ${ordersData.length} 个订单`,
        type: 'success'
      })
      await refreshActiveOrders()
    } else {
      ElMessage.error(response.message || '批量订单提交失败')
    }
  } catch (error) {
    ElMessage.error('批量订单提交失败，请重试')
  }
}

const handleConditionalOrderCreated = async (conditionData: any) => {
  try {
    const response = await apiClient.post('/trading/conditional-orders', conditionData)
    if (response.success) {
      ElMessage.success('条件单创建成功')
      await refreshConditionalOrders()
    } else {
      ElMessage.error(response.message || '条件单创建失败')
    }
  } catch (error) {
    ElMessage.error('条件单创建失败，请重试')
  }
}

const handleStrategyActivated = async (strategyData: any) => {
  try {
    const response = await apiClient.post('/trading/strategies/activate', strategyData)
    if (response.success) {
      ElNotification({
        title: '策略激活成功',
        message: `策略 "${strategyData.name}" 已开始运行`,
        type: 'success'
      })
      await refreshActiveStrategies()
    } else {
      ElMessage.error(response.message || '策略激活失败')
    }
  } catch (error) {
    ElMessage.error('策略激活失败，请重试')
  }
}

const handleAlgorithmStarted = async (algorithmData: any) => {
  try {
    const response = await apiClient.post('/trading/algorithms/start', algorithmData)
    if (response.success) {
      ElNotification({
        title: '算法交易启动成功',
        message: `算法 "${algorithmData.name}" 已开始执行`,
        type: 'success'
      })
      await refreshActiveStrategies()
    } else {
      ElMessage.error(response.message || '算法交易启动失败')
    }
  } catch (error) {
    ElMessage.error('算法交易启动失败，请重试')
  }
}

const handleOrderAction = async (action: string, orderId: string) => {
  try {
    let response
    switch (action) {
      case 'cancel':
        response = await apiClient.delete(`/trading/orders/${orderId}`)
        break
      case 'modify':
        // 打开修改对话框
        break
      default:
        return
    }
    
    if (response?.success) {
      ElMessage.success('操作成功')
      await refreshActiveOrders()
    } else {
      ElMessage.error(response?.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleConditionAction = async (action: string, conditionId: string) => {
  try {
    let response
    switch (action) {
      case 'cancel':
        response = await apiClient.delete(`/trading/conditional-orders/${conditionId}`)
        break
      case 'modify':
        // 打开修改对话框
        break
      default:
        return
    }
    
    if (response?.success) {
      ElMessage.success('操作成功')
      await refreshConditionalOrders()
    } else {
      ElMessage.error(response?.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleStrategyAction = async (action: string, strategyId: string) => {
  try {
    let response
    switch (action) {
      case 'stop':
        response = await apiClient.post(`/trading/strategies/${strategyId}/stop`)
        break
      case 'pause':
        response = await apiClient.post(`/trading/strategies/${strategyId}/pause`)
        break
      case 'resume':
        response = await apiClient.post(`/trading/strategies/${strategyId}/resume`)
        break
      default:
        return
    }
    
    if (response?.success) {
      ElMessage.success('操作成功')
      await refreshActiveStrategies()
    } else {
      ElMessage.error(response?.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

// 数据刷新方法
const refreshActiveOrders = async () => {
  try {
    const response = await apiClient.get('/trading/orders/active')
    if (response.success) {
      activeOrders.value = response.data
    }
  } catch (error) {
    console.error('刷新活跃订单失败:', error)
  }
}

const refreshConditionalOrders = async () => {
  try {
    const response = await apiClient.get('/trading/conditional-orders')
    if (response.success) {
      conditionalOrders.value = response.data
    }
  } catch (error) {
    console.error('刷新条件单失败:', error)
  }
}

const refreshActiveStrategies = async () => {
  try {
    const response = await apiClient.get('/trading/strategies/active')
    if (response.success) {
      activeStrategies.value = response.data
    }
  } catch (error) {
    console.error('刷新活跃策略失败:', error)
  }
}

const refreshTradingHistory = async () => {
  try {
    const response = await apiClient.get('/trading/history', {
      cache: true,
      cacheTTL: 60000 // 1分钟缓存
    })
    if (response.success) {
      tradingHistory.value = response.data
    }
  } catch (error) {
    console.error('刷新交易历史失败:', error)
  }
}

const refreshAllData = async () => {
  await Promise.all([
    refreshActiveOrders(),
    refreshConditionalOrders(),
    refreshActiveStrategies(),
    refreshTradingHistory()
  ])
}

// 生命周期
onMounted(() => {
  refreshAllData()
  
  // 设置定时刷新
  const interval = setInterval(refreshAllData, 30000) // 30秒刷新一次
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.advanced-trading-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.trading-mode-selector {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.single-trading,
.batch-trading,
.conditional-trading,
.strategy-trading,
.algorithm-trading {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.trading-status {
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.trading-status :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 16px;
}

.trading-status :deep(.el-tabs__content) {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .advanced-trading-panel {
    height: auto;
  }
  
  .trading-status :deep(.el-tabs__content) {
    max-height: 300px;
  }
}
</style>
