# Prometheus 告警规则配置
groups:
  # 系统级别告警
  - name: system.rules
    rules:
      # 高 CPU 使用率
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高 CPU 使用率"
          description: "实例 {{ $labels.instance }} CPU 使用率超过 80%，当前值: {{ $value }}%"

      # 高内存使用率
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高内存使用率"
          description: "实例 {{ $labels.instance }} 内存使用率超过 85%，当前值: {{ $value }}%"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 90
        for: 5m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过 90%，当前值: {{ $value }}%"

      # 高磁盘 I/O
      - alert: HighDiskIO
        expr: irate(node_disk_io_time_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高磁盘 I/O"
          description: "实例 {{ $labels.instance }} 磁盘 I/O 使用率超过 80%，当前值: {{ $value }}%"

  # 应用级别告警
  - name: application.rules
    rules:
      # 应用服务不可用
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: application
        annotations:
          summary: "服务不可用"
          description: "服务 {{ $labels.job }} 实例 {{ $labels.instance }} 已停止响应"

      # 高错误率
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "高错误率"
          description: "服务 {{ $labels.job }} 5xx 错误率超过 5%，当前值: {{ $value }}%"

      # 高响应时间
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "高响应时间"
          description: "服务 {{ $labels.job }} 95% 响应时间超过 2 秒，当前值: {{ $value }}s"

      # 高并发连接数
      - alert: HighConcurrentConnections
        expr: nginx_connections_active > 1000
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "高并发连接数"
          description: "Nginx 活跃连接数超过 1000，当前值: {{ $value }}"

  # 数据库告警
  - name: database.rules
    rules:
      # 数据库连接数过高
      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "数据库连接数过高"
          description: "PostgreSQL 连接数使用率超过 80%，当前值: {{ $value }}%"

      # 数据库查询时间过长
      - alert: SlowDatabaseQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 2m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "数据库查询时间过长"
          description: "PostgreSQL 存在运行超过 5 分钟的查询，最长时间: {{ $value }}s"

      # 数据库锁等待
      - alert: DatabaseLockWait
        expr: pg_locks_count{mode="ExclusiveLock"} > 10
        for: 2m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "数据库锁等待"
          description: "PostgreSQL 存在过多排他锁，当前数量: {{ $value }}"

      # Redis 内存使用率过高
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "Redis 内存使用率过高"
          description: "Redis 内存使用率超过 90%，当前值: {{ $value }}%"

  # 业务指标告警
  - name: business.rules
    rules:
      # 交易失败率过高
      - alert: HighTradingFailureRate
        expr: rate(trading_orders_total{status="failed"}[5m]) / rate(trading_orders_total[5m]) * 100 > 10
        for: 5m
        labels:
          severity: critical
          category: business
        annotations:
          summary: "交易失败率过高"
          description: "交易失败率超过 10%，当前值: {{ $value }}%"

      # 市场数据延迟
      - alert: MarketDataDelay
        expr: time() - market_data_last_update_timestamp > 300
        for: 2m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "市场数据延迟"
          description: "市场数据更新延迟超过 5 分钟，延迟时间: {{ $value }}s"

      # 用户登录失败率过高
      - alert: HighLoginFailureRate
        expr: rate(auth_login_total{status="failed"}[5m]) / rate(auth_login_total[5m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "用户登录失败率过高"
          description: "用户登录失败率超过 20%，当前值: {{ $value }}%"

      # 策略执行异常
      - alert: StrategyExecutionError
        expr: increase(strategy_execution_errors_total[5m]) > 5
        for: 1m
        labels:
          severity: critical
          category: business
        annotations:
          summary: "策略执行异常"
          description: "5 分钟内策略执行错误超过 5 次，当前值: {{ $value }}"

  # 安全告警
  - name: security.rules
    rules:
      # 异常登录尝试
      - alert: SuspiciousLoginAttempts
        expr: rate(auth_login_total{status="failed"}[1m]) > 10
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "异常登录尝试"
          description: "1 分钟内登录失败次数超过 10 次，可能存在暴力破解攻击"

      # 异常 API 调用
      - alert: SuspiciousAPIUsage
        expr: rate(http_requests_total[1m]) > 1000
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "异常 API 调用"
          description: "API 调用频率异常，1 分钟内超过 1000 次请求"

      # SSL 证书即将过期
      - alert: SSLCertificateExpiringSoon
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 1h
        labels:
          severity: warning
          category: security
        annotations:
          summary: "SSL 证书即将过期"
          description: "SSL 证书将在 7 天内过期，剩余时间: {{ $value }}s"

  # 网络告警
  - name: network.rules
    rules:
      # 网络延迟过高
      - alert: HighNetworkLatency
        expr: probe_duration_seconds > 5
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "网络延迟过高"
          description: "网络探测延迟超过 5 秒，当前值: {{ $value }}s"

      # 网络连接失败
      - alert: NetworkConnectionFailure
        expr: probe_success == 0
        for: 2m
        labels:
          severity: critical
          category: network
        annotations:
          summary: "网络连接失败"
          description: "网络探测失败，目标: {{ $labels.instance }}"
