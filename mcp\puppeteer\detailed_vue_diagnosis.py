#!/usr/bin/env python3
"""
详细的Vue应用诊断
"""

import requests
import json
import time
from datetime import datetime

class DetailedVueDiagnosis:
    def __init__(self):
        self.frontend_url = "http://localhost:5174"
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'diagnosis': {}
        }
        
    def check_html_content(self):
        """检查HTML内容"""
        print("🔍 检查HTML内容...")
        try:
            response = requests.get(self.frontend_url, timeout=10)
            content = response.text
            
            print(f"状态码: {response.status_code}")
            print(f"内容长度: {len(content)} 字符")
            
            # 检查关键HTML元素
            checks = {
                'DOCTYPE': '<!DOCTYPE html>' in content,
                'HTML标签': '<html' in content,
                'HEAD标签': '<head>' in content,
                'BODY标签': '<body>' in content,
                'APP容器': 'id="app"' in content,
                'Vite客户端': '@vite/client' in content,
                'Main.ts脚本': 'main.ts' in content or 'main.js' in content,
                'Vue脚本': 'vue' in content.lower(),
                'Element Plus': 'element-plus' in content.lower(),
                'Router View': 'router-view' in content,
                'Vue应用挂载': 'mount' in content,
                '中文标题': '量化投资平台' in content,
                '导航元素': any(nav in content for nav in ['仪表盘', '市场', '交易', '策略']),
                'JavaScript错误': 'error' in content.lower() or 'exception' in content.lower()
            }
            
            print("\nHTML元素检查:")
            for check, result in checks.items():
                status = "✅" if result else "❌"
                print(f"  {status} {check}: {result}")
                
            self.results['diagnosis']['html_checks'] = checks
            
            # 检查具体的脚本标签
            print("\n📜 脚本标签分析:")
            script_tags = []
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if '<script' in line:
                    script_tags.append(f"第{i}行: {line.strip()}")
                    
            for script in script_tags:
                print(f"  {script}")
                
            self.results['diagnosis']['script_tags'] = script_tags
            
            # 检查页面的实际渲染内容
            print("\n🎨 页面渲染内容分析:")
            body_start = content.find('<body')
            body_end = content.find('</body>')
            
            if body_start != -1 and body_end != -1:
                body_content = content[body_start:body_end + 7]
                print(f"Body内容长度: {len(body_content)} 字符")
                
                # 检查body中是否有实际的Vue组件内容
                vue_indicators = [
                    'router-view',
                    'el-',  # Element Plus组件
                    'v-',   # Vue指令
                    'class="',
                    'style="',
                    '量化投资平台',
                    '仪表盘',
                    '市场行情'
                ]
                
                found_indicators = []
                for indicator in vue_indicators:
                    if indicator in body_content:
                        found_indicators.append(indicator)
                        
                print(f"找到的Vue指示器: {found_indicators}")
                self.results['diagnosis']['vue_indicators'] = found_indicators
                
                if len(found_indicators) < 3:
                    print("⚠️ Vue应用可能没有正确渲染到DOM中")
                    
            return content
            
        except Exception as e:
            print(f"❌ HTML内容检查失败: {e}")
            return None
            
    def check_javascript_execution(self):
        """检查JavaScript执行情况"""
        print("\n🔧 检查JavaScript执行...")
        
        # 尝试获取可能的JavaScript错误信息
        try:
            # 检查是否有JavaScript控制台输出的API
            console_url = f"{self.frontend_url}/__vite_ping"
            response = requests.get(console_url, timeout=5)
            print(f"Vite ping状态: {response.status_code}")
        except:
            print("Vite ping失败")
            
        # 检查开发工具相关的端点
        try:
            devtools_url = f"{self.frontend_url}/__inspect/"
            response = requests.get(devtools_url, timeout=5)
            print(f"开发工具状态: {response.status_code}")
        except:
            print("开发工具不可用")
            
    def check_vue_router_paths(self):
        """检查Vue Router路径"""
        print("\n🛣️ 检查Vue Router路径...")
        
        paths_to_test = [
            '/',
            '/dashboard',
            '/market',
            '/trading',
            '/strategy'
        ]
        
        path_results = {}
        
        for path in paths_to_test:
            try:
                url = f"{self.frontend_url}{path}"
                response = requests.get(url, timeout=5)
                content = response.text
                
                # 检查是否返回相同的HTML（说明是SPA）
                has_vue_content = any(indicator in content for indicator in [
                    'router-view', '量化投资平台', 'Vue', 'vite'
                ])
                
                path_results[path] = {
                    'status_code': response.status_code,
                    'content_length': len(content),
                    'has_vue_content': has_vue_content
                }
                
                status = "✅" if response.status_code == 200 else "❌"
                vue_status = "✅" if has_vue_content else "❌"
                print(f"  {status} {path}: {response.status_code} ({len(content)} 字符) Vue内容: {vue_status}")
                
            except Exception as e:
                path_results[path] = {'error': str(e)}
                print(f"  ❌ {path}: 错误 - {e}")
                
        self.results['diagnosis']['router_paths'] = path_results
        
    def check_static_assets(self):
        """检查静态资源"""
        print("\n📦 检查静态资源...")
        
        assets_to_check = [
            '/favicon.ico',
            '/manifest.json',
            '/@vite/client',
            '/src/main.ts'
        ]
        
        asset_results = {}
        
        for asset in assets_to_check:
            try:
                url = f"{self.frontend_url}{asset}"
                response = requests.get(url, timeout=5)
                
                asset_results[asset] = {
                    'status_code': response.status_code,
                    'content_type': response.headers.get('content-type', ''),
                    'content_length': len(response.content)
                }
                
                status = "✅" if response.status_code == 200 else "❌"
                print(f"  {status} {asset}: {response.status_code} ({response.headers.get('content-type', 'unknown')})")
                
            except Exception as e:
                asset_results[asset] = {'error': str(e)}
                print(f"  ❌ {asset}: 错误 - {e}")
                
        self.results['diagnosis']['static_assets'] = asset_results
        
    def analyze_potential_issues(self):
        """分析潜在问题"""
        print("\n🔍 分析潜在问题...")
        
        issues = []
        
        # 基于检查结果分析问题
        html_checks = self.results['diagnosis'].get('html_checks', {})
        
        if not html_checks.get('APP容器'):
            issues.append("缺少#app容器元素")
            
        if not html_checks.get('Main.ts脚本'):
            issues.append("main.ts脚本未加载")
            
        if not html_checks.get('Vue脚本'):
            issues.append("Vue脚本未加载")
            
        if not html_checks.get('Router View'):
            issues.append("router-view元素缺失")
            
        if html_checks.get('JavaScript错误'):
            issues.append("页面包含JavaScript错误")
            
        vue_indicators = self.results['diagnosis'].get('vue_indicators', [])
        if len(vue_indicators) < 3:
            issues.append("Vue应用可能未正确渲染")
            
        router_paths = self.results['diagnosis'].get('router_paths', {})
        if router_paths:
            all_same_length = len(set(path.get('content_length', 0) for path in router_paths.values() if 'content_length' in path)) <= 1
            if all_same_length:
                issues.append("所有路由返回相同内容，路由可能未工作")
                
        self.results['diagnosis']['potential_issues'] = issues
        
        if issues:
            print("发现的潜在问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("✅ 未发现明显问题")
            
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        timestamp = int(time.time())
        report_file = f"detailed_vue_diagnosis_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
            
        print(f"\n📊 详细诊断报告已生成: {report_file}")
        return self.results
        
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始Vue应用详细诊断")
        print("="*60)
        
        self.check_html_content()
        self.check_javascript_execution()
        self.check_vue_router_paths()
        self.check_static_assets()
        self.analyze_potential_issues()
        
        report = self.generate_diagnosis_report()
        
        print("\n✅ 详细诊断完成!")
        return report

if __name__ == "__main__":
    diagnosis = DetailedVueDiagnosis()
    diagnosis.run_diagnosis()
