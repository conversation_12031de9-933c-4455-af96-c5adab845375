"""
存储系统API - 简化版
提供基本的存储监控功能
"""
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
import psutil

router = APIRouter()


@router.get("/stats")
async def get_storage_stats():
    """
    获取存储系统统计信息 - 简化版
    """
    try:
        # 获取磁盘使用情况
        disk_usage = psutil.disk_usage('/')
        
        # 简化的统计信息
        stats = {
            "disk_usage": {
                "total_gb": round(disk_usage.total / (1024**3), 2),
                "used_gb": round(disk_usage.used / (1024**3), 2),
                "free_gb": round(disk_usage.free / (1024**3), 2),
                "usage_percent": round((disk_usage.used / disk_usage.total) * 100, 2)
            },
            "status": "healthy" if disk_usage.free > 1024**3 else "warning",
            "timestamp": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": stats,
            "message": "存储统计信息获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储统计失败: {str(e)}")


@router.get("/health")
async def storage_health():
    """存储健康检查"""
    try:
        disk_usage = psutil.disk_usage('/')
        usage_percent = (disk_usage.used / disk_usage.total) * 100
        
        if usage_percent < 70:
            status = "healthy"
            message = "存储空间充足"
        elif usage_percent < 85:
            status = "warning"
            message = "存储空间使用较高"
        else:
            status = "critical"
            message = "存储空间不足"
        
        return {
            "status": status,
            "message": message,
            "usage_percent": round(usage_percent, 2),
            "free_space_gb": round(disk_usage.free / (1024**3), 2)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"存储健康检查失败: {str(e)}")
