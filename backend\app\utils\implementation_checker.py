"""
实现检查器
检测和修复代码中的空实现、占位符等问题
"""

import ast
import inspect
import os
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import re


class EmptyImplementationDetector:
    """空实现检测器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        
    def scan_directory(self, directory: Path = None) -> List[Dict[str, Any]]:
        """扫描目录中的空实现"""
        if directory is None:
            directory = self.project_root
            
        issues = []
        
        for py_file in directory.rglob("*.py"):
            if self._should_skip_file(py_file):
                continue
                
            file_issues = self._analyze_file(py_file)
            issues.extend(file_issues)
            
        return issues
    
    def _should_skip_file(self, file_path: Path) -> bool:
        """判断是否应该跳过文件"""
        skip_patterns = [
            'venv', '__pycache__', '.git', 'migrations',
            'test_', '_test.py', 'conftest.py'
        ]
        
        str_path = str(file_path)
        return any(pattern in str_path for pattern in skip_patterns)
    
    def _analyze_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """分析单个文件"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            tree = ast.parse(content)
            analyzer = ASTAnalyzer(file_path)
            analyzer.visit(tree)
            
            issues.extend(analyzer.issues)
            
            # 检查文本模式
            text_issues = self._check_text_patterns(file_path, content)
            issues.extend(text_issues)
            
        except Exception as e:
            issues.append({
                'file': str(file_path),
                'type': 'parse_error',
                'message': f'Failed to parse file: {e}',
                'line': 0,
                'severity': 'low'
            })
            
        return issues
    
    def _check_text_patterns(self, file_path: Path, content: str) -> List[Dict[str, Any]]:
        """检查文本模式"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # 检查TODO/FIXME等
            if any(marker in line.upper() for marker in ['TODO', 'FIXME', 'XXX', 'HACK']):
                issues.append({
                    'file': str(file_path),
                    'type': 'todo_marker',
                    'message': f'TODO marker found: {line}',
                    'line': i,
                    'severity': 'medium'
                })
            
            # 检查明显的占位符
            placeholders = ['NOT_IMPLEMENTED', 'PLACEHOLDER', 'DUMMY', 'MOCK_DATA']
            if any(placeholder in line.upper() for placeholder in placeholders):
                issues.append({
                    'file': str(file_path),
                    'type': 'placeholder',
                    'message': f'Placeholder found: {line}',
                    'line': i,
                    'severity': 'high'
                })
                
        return issues


class ASTAnalyzer(ast.NodeVisitor):
    """AST分析器"""
    
    def __init__(self, file_path: Path):
        self.file_path = file_path
        self.issues = []
        self.class_stack = []
        self.function_stack = []
    
    def visit_ClassDef(self, node):
        """访问类定义"""
        self.class_stack.append(node.name)
        self.generic_visit(node)
        self.class_stack.pop()
    
    def visit_FunctionDef(self, node):
        """访问函数定义"""
        self.function_stack.append(node.name)
        
        # 分析函数体
        self._analyze_function_body(node)
        
        self.generic_visit(node)
        self.function_stack.pop()
    
    def visit_AsyncFunctionDef(self, node):
        """访问异步函数定义"""
        self.function_stack.append(node.name)
        self._analyze_function_body(node)
        self.generic_visit(node)
        self.function_stack.pop()
    
    def _analyze_function_body(self, node):
        """分析函数体"""
        if not node.body:
            return
            
        # 检查是否只有pass语句
        if len(node.body) == 1 and isinstance(node.body[0], ast.Pass):
            # 判断是否是合理的pass
            if not self._is_reasonable_pass(node):
                severity = self._determine_severity(node)
                self.issues.append({
                    'file': str(self.file_path),
                    'type': 'empty_implementation',
                    'message': f'Function {node.name} has only pass statement',
                    'line': node.lineno,
                    'function': node.name,
                    'class': self.class_stack[-1] if self.class_stack else None,
                    'severity': severity
                })
        
        # 检查是否有NotImplementedError
        for stmt in node.body:
            if isinstance(stmt, ast.Raise):
                if isinstance(stmt.exc, ast.Name) and stmt.exc.id == 'NotImplementedError':
                    self.issues.append({
                        'file': str(self.file_path),
                        'type': 'not_implemented',
                        'message': f'Function {node.name} raises NotImplementedError',
                        'line': node.lineno,
                        'function': node.name,
                        'class': self.class_stack[-1] if self.class_stack else None,
                        'severity': 'high'
                    })
                elif isinstance(stmt.exc, ast.Call) and isinstance(stmt.exc.func, ast.Name) and stmt.exc.func.id == 'NotImplementedError':
                    self.issues.append({
                        'file': str(self.file_path),
                        'type': 'not_implemented',
                        'message': f'Function {node.name} raises NotImplementedError',
                        'line': node.lineno,
                        'function': node.name,
                        'class': self.class_stack[-1] if self.class_stack else None,
                        'severity': 'high'
                    })
        
        # 检查是否只返回空值/占位符
        if len(node.body) == 1 and isinstance(node.body[0], ast.Return):
            return_value = node.body[0].value
            if self._is_placeholder_return(return_value):
                self.issues.append({
                    'file': str(self.file_path),
                    'type': 'placeholder_return',
                    'message': f'Function {node.name} returns placeholder value',
                    'line': node.lineno,
                    'function': node.name,
                    'class': self.class_stack[-1] if self.class_stack else None,
                    'severity': 'medium'
                })
    
    def _is_reasonable_pass(self, node) -> bool:
        """判断pass是否合理"""
        # 检查装饰器
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                if decorator.id in ['abstractmethod', 'property']:
                    return True
            elif isinstance(decorator, ast.Attribute):
                if decorator.attr in ['abstractmethod', 'property']:
                    return True
        
        # 检查是否在抽象基类中
        if self.class_stack:
            # 简单检查，可以更复杂
            pass
        
        # 检查函数名模式
        function_name = node.name
        if function_name.startswith('_') and function_name.endswith('_'):
            return True  # 魔法方法
        
        # 检查是否是事件处理器或钩子函数
        hook_patterns = ['on_', 'handle_', 'callback_', 'hook_']
        if any(function_name.startswith(pattern) for pattern in hook_patterns):
            return True
            
        return False
    
    def _determine_severity(self, node) -> str:
        """确定严重程度"""
        # 公共方法更严重
        if not node.name.startswith('_'):
            return 'high'
        
        # API端点更严重
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Attribute):
                if decorator.attr in ['get', 'post', 'put', 'delete']:
                    return 'critical'
            elif isinstance(decorator, ast.Name):
                if decorator.id in ['route']:
                    return 'critical'
        
        return 'medium'
    
    def _is_placeholder_return(self, return_value) -> bool:
        """判断是否是占位符返回值"""
        if return_value is None:
            return True
            
        if isinstance(return_value, ast.Constant):
            value = return_value.value
            if value in [None, "", 0, [], {}]:
                return True
            if isinstance(value, str) and value.upper() in ['TODO', 'NOT_IMPLEMENTED', 'PLACEHOLDER']:
                return True
        
        if isinstance(return_value, (ast.Dict, ast.List)) and len(return_value.elts if hasattr(return_value, 'elts') else return_value.keys if hasattr(return_value, 'keys') else []) == 0:
            return True
            
        return False


class ImplementationFixer:
    """实现修复器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = []
    
    def generate_fix_suggestions(self, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成修复建议"""
        suggestions = []
        
        for issue in issues:
            if issue['type'] == 'empty_implementation':
                suggestion = self._suggest_implementation_fix(issue)
                suggestions.append(suggestion)
            elif issue['type'] == 'not_implemented':
                suggestion = self._suggest_not_implemented_fix(issue)
                suggestions.append(suggestion)
            elif issue['type'] == 'placeholder_return':
                suggestion = self._suggest_placeholder_fix(issue)
                suggestions.append(suggestion)
                
        return suggestions
    
    def _suggest_implementation_fix(self, issue: Dict[str, Any]) -> Dict[str, Any]:
        """建议实现修复"""
        function_name = issue['function']
        class_name = issue.get('class')
        
        # 根据函数名推断实现
        if 'get' in function_name.lower():
            if 'list' in function_name.lower():
                implementation = '''
    # TODO: 实现获取列表逻辑
    items = []  # 从数据库或其他数据源获取
    return items'''
            else:
                implementation = '''
    # TODO: 实现获取单个对象逻辑
    item = None  # 从数据库或其他数据源获取
    return item'''
        
        elif 'create' in function_name.lower():
            implementation = '''
    # TODO: 实现创建逻辑
    # 1. 验证输入数据
    # 2. 保存到数据库
    # 3. 返回创建的对象
    raise NotImplementedError("Create method needs implementation")'''
        
        elif 'update' in function_name.lower():
            implementation = '''
    # TODO: 实现更新逻辑
    # 1. 查找现有对象
    # 2. 验证更新数据
    # 3. 保存更改
    # 4. 返回更新后的对象
    raise NotImplementedError("Update method needs implementation")'''
        
        elif 'delete' in function_name.lower():
            implementation = '''
    # TODO: 实现删除逻辑
    # 1. 查找要删除的对象
    # 2. 检查删除权限
    # 3. 执行删除操作
    # 4. 返回删除结果
    raise NotImplementedError("Delete method needs implementation")'''
        
        else:
            implementation = '''
    # TODO: 实现具体业务逻辑
    raise NotImplementedError("Method needs implementation")'''
        
        return {
            'issue': issue,
            'type': 'replace_pass',
            'suggested_code': implementation,
            'description': f'Replace empty pass in {function_name} with proper implementation structure'
        }
    
    def _suggest_not_implemented_fix(self, issue: Dict[str, Any]) -> Dict[str, Any]:
        """建议NotImplementedError修复"""
        return {
            'issue': issue,
            'type': 'implement_method',
            'description': f'Method {issue["function"]} needs actual implementation',
            'priority': 'high'
        }
    
    def _suggest_placeholder_fix(self, issue: Dict[str, Any]) -> Dict[str, Any]:
        """建议占位符修复"""
        return {
            'issue': issue,
            'type': 'replace_placeholder',
            'description': f'Replace placeholder return in {issue["function"]} with meaningful data',
            'priority': 'medium'
        }


def generate_implementation_report(project_root: str) -> Dict[str, Any]:
    """生成实现报告"""
    detector = EmptyImplementationDetector(project_root)
    issues = detector.scan_directory()
    
    fixer = ImplementationFixer(project_root)
    suggestions = fixer.generate_fix_suggestions(issues)
    
    # 统计
    stats = {
        'total_issues': len(issues),
        'by_type': {},
        'by_severity': {},
        'files_affected': len(set(issue['file'] for issue in issues))
    }
    
    for issue in issues:
        issue_type = issue['type']
        severity = issue['severity']
        
        stats['by_type'][issue_type] = stats['by_type'].get(issue_type, 0) + 1
        stats['by_severity'][severity] = stats['by_severity'].get(severity, 0) + 1
    
    return {
        'statistics': stats,
        'issues': issues,
        'suggestions': suggestions,
        'summary': _generate_summary(stats, issues)
    }


def _generate_summary(stats: Dict, issues: List[Dict]) -> str:
    """生成摘要"""
    summary = f"""
Implementation Analysis Summary:
=============================

Total Issues Found: {stats['total_issues']}
Files Affected: {stats['files_affected']}

Issue Types:
{chr(10).join(f"  - {issue_type}: {count}" for issue_type, count in stats['by_type'].items())}

Severity Distribution:
{chr(10).join(f"  - {severity}: {count}" for severity, count in stats['by_severity'].items())}

Critical Issues (require immediate attention):
{chr(10).join(f"  - {issue['file']}:{issue['line']} - {issue['message']}" for issue in issues if issue['severity'] == 'critical')}

Recommendations:
1. Address critical issues first (API endpoints with empty implementations)
2. Implement high-severity empty methods
3. Replace placeholder returns with meaningful data
4. Add proper error handling to all methods
5. Add comprehensive unit tests to catch future regressions
"""
    return summary


# 命令行工具
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python implementation_checker.py <project_root>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    report = generate_implementation_report(project_root)
    
    print(report['summary'])
    print(f"\nDetailed report with {len(report['issues'])} issues and {len(report['suggestions'])} suggestions generated.")
    
    # 可选：保存详细报告到文件
    import json
    with open('implementation_report.json', 'w') as f:
        json.dump(report, f, indent=2, default=str)