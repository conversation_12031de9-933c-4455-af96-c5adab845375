# 开发指南

## 📋 目录

- [环境准备](#环境准备)
- [项目结构](#项目结构)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [调试指南](#调试指南)
- [常见问题](#常见问题)

## 🛠️ 环境准备

### 系统要求

- **Node.js**: >= 18.0.0
- **Python**: >= 3.9
- **npm**: >= 8.0.0
- **Git**: 最新版本

### 开发工具推荐

- **IDE**: VS Code / WebStorm / PyCharm
- **浏览器**: Chrome (推荐) / Firefox / Edge
- **API测试**: Postman / Insomnia
- **数据库工具**: DBeaver / Navicat

### 环境配置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd quant014
```

#### 2. 前端环境
```bash
cd frontend
npm install
cp .env.example .env.local
# 编辑 .env.local 配置文件
npm run dev
```

#### 3. 后端环境
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# 编辑 .env 配置文件
python app/main.py
```

## 📁 项目结构

### 前端结构 (frontend/)
```
frontend/
├── src/
│   ├── api/              # API接口层
│   ├── assets/           # 静态资源
│   ├── components/       # 可复用组件
│   │   ├── common/       # 通用组件
│   │   ├── charts/       # 图表组件
│   │   ├── trading/      # 交易相关组件
│   │   └── market/       # 行情相关组件
│   ├── composables/      # 组合式函数
│   ├── layouts/          # 布局组件
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   ├── types/            # TypeScript类型
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   └── workers/          # Web Workers
├── public/               # 公共资源
└── scripts/              # 构建脚本
```

### 后端结构 (backend/)
```
backend/
├── app/
│   ├── api/              # API路由
│   │   └── v1/           # API版本1
│   ├── core/             # 核心配置
│   ├── models/           # 数据模型
│   ├── schemas/          # Pydantic模式
│   ├── services/         # 业务服务
│   ├── utils/            # 工具函数
│   └── middleware/       # 中间件
├── alembic/              # 数据库迁移
├── tests/                # 测试代码
└── scripts/              # 脚本文件
```

## 🔄 开发流程

### 1. 功能开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发前准备**
   - 查看相关文档和API设计
   - 确认技术方案和实现路径
   - 创建或更新类型定义

3. **编写代码**
   - 遵循代码规范
   - 编写单元测试
   - 添加必要的注释

4. **测试验证**
   ```bash
   # 前端测试
   cd frontend
   npm run test
   npm run lint
   npm run type-check
   
   # 后端测试
   cd backend
   python -m pytest
   python -m flake8
   python -m mypy app/
   ```

5. **提交代码**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   git push origin feature/your-feature-name
   ```

### 2. 代码审查流程

1. 创建Pull Request
2. 代码审查和讨论
3. 修改和完善
4. 合并到主分支

## 📝 代码规范

### 前端规范

#### 1. 文件命名
- **组件文件**: PascalCase (如: `UserProfile.vue`)
- **页面文件**: PascalCase (如: `Dashboard.vue`)
- **工具文件**: camelCase (如: `formatUtils.ts`)
- **类型文件**: camelCase (如: `userTypes.ts`)

#### 2. 组件规范
```vue
<template>
  <!-- 使用语义化的HTML标签 -->
  <div class="user-profile">
    <h1 class="profile-title">{{ title }}</h1>
    <!-- 使用v-if而不是v-show来条件渲染 -->
    <div v-if="isLoading" class="loading">加载中...</div>
  </div>
</template>

<script setup lang="ts">
// 导入顺序：Vue相关 -> 第三方库 -> 本地模块
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 接口定义
interface Props {
  userId: string
  showActions?: boolean
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

// 响应式数据
const isLoading = ref(false)
const userInfo = ref<UserInfo | null>(null)

// 计算属性
const displayName = computed(() => {
  return userInfo.value?.name || '未知用户'
})

// 方法
const loadUserInfo = async () => {
  try {
    isLoading.value = true
    // API调用
  } catch (error) {
    ElMessage.error('加载用户信息失败')
  } finally {
    isLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped lang="scss">
.user-profile {
  padding: 20px;
  
  .profile-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  .loading {
    text-align: center;
    color: #666;
  }
}
</style>
```

#### 3. TypeScript规范
```typescript
// 接口定义
interface UserInfo {
  id: string
  name: string
  email: string
  avatar?: string
  createdAt: Date
}

// 类型别名
type UserStatus = 'active' | 'inactive' | 'pending'

// 枚举
enum OrderType {
  MARKET = 'market',
  LIMIT = 'limit',
  STOP = 'stop'
}

// 泛型函数
function createApiResponse<T>(data: T, message = 'success'): ApiResponse<T> {
  return {
    success: true,
    data,
    message
  }
}
```

### 后端规范

#### 1. 文件命名
- **模块文件**: snake_case (如: `user_service.py`)
- **类名**: PascalCase (如: `UserService`)
- **函数名**: snake_case (如: `get_user_profile`)
- **常量**: UPPER_SNAKE_CASE (如: `API_VERSION`)

#### 2. API设计规范
```python
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/api/v1/users", tags=["users"])

class UserCreate(BaseModel):
    """用户创建模型"""
    name: str
    email: str
    password: str

class UserResponse(BaseModel):
    """用户响应模型"""
    id: str
    name: str
    email: str
    created_at: datetime

@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    """
    创建新用户
    
    Args:
        user_data: 用户创建数据
        db: 数据库会话
        
    Returns:
        创建的用户信息
        
    Raises:
        HTTPException: 当用户已存在时
    """
    try:
        # 业务逻辑
        user = await user_service.create_user(db, user_data)
        return UserResponse.from_orm(user)
    except UserExistsError:
        raise HTTPException(
            status_code=400,
            detail="用户已存在"
        )
```

## 🐛 调试指南

### 前端调试

#### 1. 浏览器开发者工具
- **Console**: 查看日志和错误信息
- **Network**: 监控API请求和响应
- **Vue DevTools**: 调试Vue组件状态

#### 2. VS Code调试配置
```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Frontend",
  "program": "${workspaceFolder}/frontend/node_modules/.bin/vite",
  "args": ["--mode", "development"],
  "console": "integratedTerminal"
}
```

### 后端调试

#### 1. Python调试
```python
import pdb; pdb.set_trace()  # 设置断点
```

#### 2. 日志调试
```python
import logging

logger = logging.getLogger(__name__)
logger.info("调试信息")
logger.error("错误信息")
```

## ❓ 常见问题

### Q1: 前端构建失败
**A**: 检查Node.js版本，清理缓存后重新安装依赖
```bash
rm -rf node_modules package-lock.json
npm install
```

### Q2: 后端启动失败
**A**: 检查Python版本和依赖，确认环境变量配置
```bash
pip install -r requirements.txt
python -c "import app.main"
```

### Q3: API请求跨域问题
**A**: 检查CORS配置，确认前后端端口配置一致

### Q4: TypeScript类型错误
**A**: 更新类型定义，运行类型检查
```bash
npm run type-check
```

### Q5: 数据库连接失败
**A**: 检查数据库配置和连接字符串

---

更多问题请查看 [FAQ文档](./FAQ.md) 或提交Issue。
