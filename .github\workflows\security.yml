name: Security Scan

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'

jobs:
  # 代码安全扫描
  code-security:
    name: Code Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run GitLeaks
      uses: gitleaks/gitleaks-action@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/python
          p/javascript
          p/typescript
          p/docker
    
    - name: Run Bandit (Python)
      run: |
        pip install bandit
        bandit -r backend/ -f json -o bandit-report.json || true
        
        # 解析报告
        if [ -f bandit-report.json ]; then
          python -c "
          import json
          with open('bandit-report.json') as f:
              report = json.load(f)
              if report.get('results'):
                  print('发现安全问题:')
                  for issue in report['results']:
                      print(f\"- {issue['issue_text']} ({issue['filename']}:{issue['line_number']})\")
          "
        fi
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          semgrep.sarif

  # 依赖漏洞扫描
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Snyk (Backend)
      uses: snyk/actions/python@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high --file=backend/requirements.txt
    
    - name: Run Snyk (Frontend)
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high --file=frontend/package.json
    
    - name: Run OWASP Dependency Check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'quant-platform'
        path: '.'
        format: 'HTML'
        args: >
          --enableRetired
          --enableExperimental
    
    - name: Upload dependency check results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-check-report
        path: reports/

  # 容器镜像扫描
  container-scan:
    name: Container Image Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build test images
      run: |
        docker build -f Dockerfile.backend -t quant-backend:test .
        docker build -f Dockerfile.frontend -t quant-frontend:test .
    
    - name: Run Trivy scan on backend
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'quant-backend:test'
        format: 'sarif'
        output: 'trivy-backend.sarif'
        severity: 'CRITICAL,HIGH'
    
    - name: Run Trivy scan on frontend
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'quant-frontend:test'
        format: 'sarif'
        output: 'trivy-frontend.sarif'
        severity: 'CRITICAL,HIGH'
    
    - name: Upload Trivy results to GitHub Security
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-backend.sarif'
    
    - name: Run Grype scan
      run: |
        curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
        
        grype quant-backend:test -o json > grype-backend.json
        grype quant-frontend:test -o json > grype-frontend.json
        
        # 检查高危漏洞
        HIGH_VULNS=$(jq '[.matches[] | select(.vulnerability.severity == "High" or .vulnerability.severity == "Critical")] | length' grype-backend.json)
        if [ "$HIGH_VULNS" -gt 0 ]; then
          echo "发现 $HIGH_VULNS 个高危漏洞"
          jq '.matches[] | select(.vulnerability.severity == "High" or .vulnerability.severity == "Critical") | {cve: .vulnerability.id, package: .artifact.name, severity: .vulnerability.severity}' grype-backend.json
        fi

  # SAST扫描
  sast-scan:
    name: Static Application Security Testing
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: python, javascript
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
    
    - name: Run SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # API安全测试
  api-security:
    name: API Security Testing
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Start services
      run: |
        docker-compose up -d
        sleep 60  # 等待服务启动
    
    - name: Run OWASP ZAP API Scan
      run: |
        docker run --rm --network host \
          -v $(pwd):/zap/wrk/:rw \
          -t owasp/zap2docker-stable zap-api-scan.py \
          -t http://localhost:8000/openapi.json \
          -f openapi \
          -r zap-api-report.html \
          -w zap-api-report.md
    
    - name: Run API Fuzzing
      run: |
        # 安装RESTler
        docker run --rm --network host \
          -v $(pwd):/app \
          mcr.microsoft.com/restlerfuzzer/restler:latest \
          compile --api_spec /app/backend/openapi.json
        
        # 运行模糊测试
        docker run --rm --network host \
          -v $(pwd):/app \
          mcr.microsoft.com/restlerfuzzer/restler:latest \
          test --grammar_file /app/Compile/grammar.py \
          --dictionary_file /app/Compile/dict.json \
          --settings /app/Compile/engine_settings.json
    
    - name: Upload API security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: api-security-reports
        path: |
          zap-api-report.*
          Test/

  # 基础设施安全扫描
  infrastructure-security:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        quiet: true
        framework: all
        output_format: sarif
        output_file_path: reports/checkov.sarif
    
    - name: Run Terrascan
      run: |
        docker run --rm -v $(pwd):/app accurics/terrascan:latest \
          scan -i k8s -d /app/k8s \
          -o json > terrascan-report.json
    
    - name: Run Kubesec
      run: |
        # 扫描Kubernetes配置
        for file in k8s/*.yaml; do
          echo "扫描 $file"
          docker run --rm -v $(pwd):/app kubesec/kubesec:latest \
            scan /app/$file > kubesec-$(basename $file).json
        done
    
    - name: Upload infrastructure reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: infrastructure-security-reports
        path: |
          reports/
          terrascan-report.json
          kubesec-*.json

  # 安全报告汇总
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [code-security, dependency-scan, container-scan, sast-scan]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate security summary
      run: |
        echo "# 安全扫描摘要" > security-summary.md
        echo "" >> security-summary.md
        echo "扫描时间: $(date)" >> security-summary.md
        echo "" >> security-summary.md
        
        # 汇总各项扫描结果
        echo "## 扫描结果" >> security-summary.md
        echo "" >> security-summary.md
        
        # 检查是否有高危发现
        HIGH_RISK=false
        
        if [ -f "security-reports/bandit-report.json" ]; then
          BANDIT_ISSUES=$(jq '.results | length' security-reports/bandit-report.json)
          echo "- Bandit (Python安全): $BANDIT_ISSUES 个问题" >> security-summary.md
          [ "$BANDIT_ISSUES" -gt 0 ] && HIGH_RISK=true
        fi
        
        if [ -f "dependency-check-report/dependency-check-report.html" ]; then
          echo "- OWASP依赖检查: 已完成" >> security-summary.md
        fi
        
        echo "" >> security-summary.md
        echo "## 建议措施" >> security-summary.md
        
        if [ "$HIGH_RISK" = true ]; then
          echo "⚠️ **发现高风险安全问题，请立即处理**" >> security-summary.md
        else
          echo "✅ 未发现高风险安全问题" >> security-summary.md
        fi
    
    - name: Comment PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync('security-summary.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: summary
          });
    
    - name: Create security issue if needed
      if: env.HIGH_RISK == 'true'
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: '🔒 安全扫描发现高风险问题',
            body: '安全扫描在最新的提交中发现了高风险问题。请查看[工作流运行结果](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})获取详细信息。',
            labels: ['security', 'high-priority']
          });