# Production Environment Configuration
# Includes security hardening, resource limits, and monitoring

version: '3.8'

services:
  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quant_nginx_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - quant_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Backend API (Production)
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
      args:
        BUILD_ENV: production
        PYTHON_VERSION: "3.11"
    container_name: quant_backend_prod
    restart: unless-stopped
    environment:
      - API_PORT=${API_PORT:-8000}
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:${DB_PORT:-5432}/quant_db
      - REDIS_URL=redis://redis:${REDIS_PORT:-6379}/0
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - LOG_LEVEL=INFO
      - PROMETHEUS_METRICS_PORT=8001
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant_network
    volumes:
      - backend_logs:/app/logs
      - backend_data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${API_PORT:-8000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Frontend (Production)
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
      args:
        BUILD_ENV: production
        NODE_VERSION: "18"
    container_name: quant_frontend_prod
    restart: unless-stopped
    environment:
      - API_PORT=${API_PORT:-8000}
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - quant_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Celery Worker (Async Tasks)
  celery-worker:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
      args:
        BUILD_ENV: production
        PYTHON_VERSION: "3.11"
    container_name: quant_celery_worker
    restart: unless-stopped
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4
    environment:
      - API_PORT=${API_PORT:-8000}
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:${DB_PORT:-5432}/quant_db
      - REDIS_URL=redis://redis:${REDIS_PORT:-6379}/0
      - CELERY_BROKER_URL=redis://redis:${REDIS_PORT:-6379}/1
      - CELERY_RESULT_BACKEND=redis://redis:${REDIS_PORT:-6379}/2
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant_network
    volumes:
      - celery_logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M

  # Celery Beat (Scheduled Tasks)
  celery-beat:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
      args:
        BUILD_ENV: production
        PYTHON_VERSION: "3.11"
    container_name: quant_celery_beat
    restart: unless-stopped
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - API_PORT=${API_PORT:-8000}
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:${DB_PORT:-5432}/quant_db
      - REDIS_URL=redis://redis:${REDIS_PORT:-6379}/0
      - CELERY_BROKER_URL=redis://redis:${REDIS_PORT:-6379}/1
      - CELERY_RESULT_BACKEND=redis://redis:${REDIS_PORT:-6379}/2
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant_network
    volumes:
      - celery_logs:/app/logs

volumes:
  backend_logs:
    name: quant_backend_logs_prod
    driver: local
  backend_data:
    name: quant_backend_data_prod
    driver: local
  celery_logs:
    name: quant_celery_logs_prod
    driver: local
  nginx_logs:
    name: quant_nginx_logs_prod
    driver: local

networks:
  quant_network:
    external: true