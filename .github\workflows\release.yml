name: Release Pipeline

on:
  push:
    tags:
      - 'v*'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 创建发布
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Generate changelog
      id: changelog
      run: |
        # 获取上一个标签
        PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
        
        # 生成变更日志
        echo "## 变更日志" > CHANGELOG.md
        echo "" >> CHANGELOG.md
        
        if [ -n "$PREVIOUS_TAG" ]; then
          echo "### 自 $PREVIOUS_TAG 以来的变更:" >> CHANGELOG.md
          git log --pretty=format:"- %s" "$PREVIOUS_TAG"..HEAD >> CHANGELOG.md
        else
          echo "### 所有变更:" >> CHANGELOG.md
          git log --pretty=format:"- %s" >> CHANGELOG.md
        fi
        
        # 统计信息
        echo "" >> CHANGELOG.md
        echo "### 统计" >> CHANGELOG.md
        echo "- 提交数: $(git rev-list --count ${PREVIOUS_TAG:+$PREVIOUS_TAG..}HEAD)" >> CHANGELOG.md
        echo "- 贡献者: $(git shortlog -sn ${PREVIOUS_TAG:+$PREVIOUS_TAG..}HEAD | wc -l)" >> CHANGELOG.md
        
        # 设置输出
        echo "::set-output name=changelog::$(cat CHANGELOG.md)"
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body: ${{ steps.changelog.outputs.changelog }}
        draft: false
        prerelease: ${{ contains(github.ref, '-rc') || contains(github.ref, '-beta') || contains(github.ref, '-alpha') }}
    
    - name: Build release artifacts
      run: |
        # 创建发布目录
        mkdir -p release
        
        # 打包源代码
        tar -czf release/quant-platform-source-${{ github.ref_name }}.tar.gz \
          --exclude='.git' \
          --exclude='node_modules' \
          --exclude='__pycache__' \
          --exclude='.env*' \
          .
        
        # 生成配置模板
        cp docker-compose.yml release/docker-compose-${{ github.ref_name }}.yml
        sed -i "s|:latest|:${{ github.ref_name }}|g" release/docker-compose-${{ github.ref_name }}.yml
        
        # 创建部署脚本
        cat > release/deploy.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "🚀 部署量化平台 ${{ github.ref_name }}"
        
        # 检查Docker和Docker Compose
        if ! command -v docker &> /dev/null; then
            echo "❌ Docker未安装"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo "❌ Docker Compose未安装"
            exit 1
        fi
        
        # 拉取镜像
        echo "📦 拉取镜像..."
        docker pull ghcr.io/${{ github.repository }}-backend:${{ github.ref_name }}
        docker pull ghcr.io/${{ github.repository }}-frontend:${{ github.ref_name }}
        
        # 启动服务
        echo "🔧 启动服务..."
        docker-compose -f docker-compose-${{ github.ref_name }}.yml up -d
        
        # 检查健康状态
        echo "🏥 检查健康状态..."
        sleep 30
        
        if curl -f http://localhost:8000/health; then
            echo "✅ 后端服务正常"
        else
            echo "❌ 后端服务异常"
            exit 1
        fi
        
        if curl -f http://localhost; then
            echo "✅ 前端服务正常"
        else
            echo "❌ 前端服务异常"
            exit 1
        fi
        
        echo "🎉 部署成功!"
        EOF
        
        chmod +x release/deploy.sh
        
        # 生成文档
        echo "# 量化投资平台 ${{ github.ref_name }}" > release/README.md
        echo "" >> release/README.md
        echo "## 部署说明" >> release/README.md
        echo "1. 确保已安装Docker和Docker Compose" >> release/README.md
        echo "2. 运行部署脚本: ./deploy.sh" >> release/README.md
        echo "3. 访问前端: http://localhost" >> release/README.md
        echo "4. 访问API文档: http://localhost:8000/docs" >> release/README.md
    
    - name: Upload Release Assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./release/quant-platform-source-${{ github.ref_name }}.tar.gz
        asset_name: quant-platform-source-${{ github.ref_name }}.tar.gz
        asset_content_type: application/gzip

  # 构建和发布Docker镜像
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=tag
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
    
    - name: Build and push backend
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.backend
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.ref_name }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push frontend
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.frontend
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.ref_name }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 发布Helm Chart
  publish-helm-chart:
    name: Publish Helm Chart
    runs-on: ubuntu-latest
    needs: [create-release, build-and-push]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Helm
      uses: azure/setup-helm@v3
      with:
        version: 'v3.12.0'
    
    - name: Package Helm chart
      run: |
        # 创建Helm chart目录
        mkdir -p helm-charts
        
        # 更新Chart版本
        sed -i "s/version: .*/version: ${{ github.ref_name }}/g" helm/quant-platform/Chart.yaml
        sed -i "s/appVersion: .*/appVersion: ${{ github.ref_name }}/g" helm/quant-platform/Chart.yaml
        
        # 打包Chart
        helm package helm/quant-platform -d helm-charts/
    
    - name: Push to Chart Museum
      run: |
        # 推送到Chart仓库
        curl --data-binary "@helm-charts/quant-platform-${{ github.ref_name }}.tgz" \
          -H "Content-Type: application/gzip" \
          -u ${{ secrets.CHARTMUSEUM_USER }}:${{ secrets.CHARTMUSEUM_PASS }} \
          https://charts.example.com/api/charts

  # 通知
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [create-release, build-and-push, publish-helm-chart]
    if: always()
    
    steps:
    - name: Notify via Slack
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            text: "🎉 新版本发布: ${{ github.ref_name }}",
            attachments: [{
              color: '${{ job.status == 'success' && 'good' || 'danger' }}',
              title: '量化投资平台发布',
              fields: [
                {
                  title: '版本',
                  value: '${{ github.ref_name }}',
                  short: true
                },
                {
                  title: '状态',
                  value: '${{ job.status }}',
                  short: true
                },
                {
                  title: '发布者',
                  value: '${{ github.actor }}',
                  short: true
                },
                {
                  title: '发布页面',
                  value: '<https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}|查看发布>',
                  short: true
                }
              ]
            }]
          }
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
    
    - name: Send email notification
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: smtp.gmail.com
        server_port: 465
        username: ${{ secrets.MAIL_USERNAME }}
        password: ${{ secrets.MAIL_PASSWORD }}
        subject: 量化平台新版本发布 - ${{ github.ref_name }}
        to: ${{ secrets.RELEASE_EMAIL_LIST }}
        from: GitHub Actions
        body: |
          量化投资平台发布了新版本！
          
          版本: ${{ github.ref_name }}
          发布者: ${{ github.actor }}
          时间: ${{ github.event.repository.updated_at }}
          
          查看发布详情: https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}
          
          Docker镜像:
          - 后端: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.ref_name }}
          - 前端: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.ref_name }}
          
          感谢您的关注！