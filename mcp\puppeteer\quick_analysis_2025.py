#!/usr/bin/env python3
"""
2025年量化投资平台快速分析脚本
使用Puppeteer MCP进行真实用户体验测试
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

class QuickPlatformAnalysis:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'platform_status': 'unknown',
            'pages_tested': [],
            'issues_found': [],
            'performance_metrics': {},
            'user_experience_score': 0
        }
        
    async def run_analysis(self):
        """运行快速分析"""
        print("🚀 启动量化投资平台快速分析...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 1. 基础访问测试
                await self._test_basic_access(page)
                
                # 2. 核心页面测试
                await self._test_core_pages(page)
                
                # 3. 用户体验测试
                await self._test_user_experience(page)
                
                # 4. 生成报告
                self._generate_report()
                
            except Exception as e:
                print(f"❌ 测试过程中发生错误: {e}")
                self.test_results['issues_found'].append({
                    'type': 'critical_error',
                    'message': str(e),
                    'timestamp': datetime.now().isoformat()
                })
            finally:
                await browser.close()
    
    async def _test_basic_access(self, page):
        """测试基础访问"""
        print("📡 测试基础访问...")
        
        start_time = time.time()
        try:
            response = await page.goto(self.base_url, wait_until='networkidle')
            load_time = time.time() - start_time
            
            if response and response.status == 200:
                self.test_results['platform_status'] = 'accessible'
                self.test_results['performance_metrics']['homepage_load_time'] = load_time
                print(f"✅ 平台可访问，加载时间: {load_time:.2f}秒")
            else:
                self.test_results['platform_status'] = 'error'
                self.test_results['issues_found'].append({
                    'type': 'access_error',
                    'message': f'HTTP状态码: {response.status if response else "无响应"}',
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            self.test_results['platform_status'] = 'unreachable'
            self.test_results['issues_found'].append({
                'type': 'connection_error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    async def _test_core_pages(self, page):
        """测试核心页面"""
        print("🔍 测试核心页面...")
        
        core_pages = [
            {'name': '仪表盘', 'url': '/dashboard', 'selector': '.improved-dashboard'},
            {'name': '市场数据', 'url': '/market', 'selector': '.market-view-optimized'},
            {'name': '交易终端', 'url': '/trading/terminal', 'selector': '.trading-terminal'},
            {'name': '策略中心', 'url': '/strategy', 'selector': '.strategy-hub'},
            {'name': '投资组合', 'url': '/portfolio', 'selector': '.portfolio-page'},
            {'name': '风险管理', 'url': '/risk', 'selector': '.risk-monitor-page'}
        ]
        
        for page_info in core_pages:
            await self._test_single_page(page, page_info)
    
    async def _test_single_page(self, page, page_info):
        """测试单个页面"""
        print(f"  📄 测试页面: {page_info['name']}")
        
        start_time = time.time()
        page_result = {
            'name': page_info['name'],
            'url': page_info['url'],
            'status': 'unknown',
            'load_time': 0,
            'issues': []
        }
        
        try:
            # 导航到页面
            full_url = self.base_url + page_info['url']
            await page.goto(full_url, wait_until='networkidle')
            load_time = time.time() - start_time
            page_result['load_time'] = load_time
            
            # 等待页面加载
            await page.wait_for_timeout(2000)
            
            # 检查页面标题
            title = await page.title()
            if not title or title == "":
                page_result['issues'].append("页面标题为空")
            
            # 检查是否有错误信息
            error_elements = await page.query_selector_all('.error, .error-message, [class*="error"]')
            if error_elements:
                page_result['issues'].append(f"发现{len(error_elements)}个错误元素")
            
            # 检查控制台错误
            console_errors = []
            page.on('console', lambda msg: console_errors.append(msg.text) if msg.type == 'error' else None)
            
            # 检查页面是否正确加载
            if page_info['selector']:
                try:
                    await page.wait_for_selector(page_info['selector'], timeout=5000)
                    page_result['status'] = 'loaded'
                    print(f"    ✅ {page_info['name']} 加载成功 ({load_time:.2f}秒)")
                except:
                    page_result['status'] = 'failed'
                    page_result['issues'].append("页面主要内容未加载")
                    print(f"    ❌ {page_info['name']} 加载失败")
            else:
                page_result['status'] = 'loaded'
                
        except Exception as e:
            page_result['status'] = 'error'
            page_result['issues'].append(str(e))
            print(f"    ❌ {page_info['name']} 测试出错: {e}")
        
        self.test_results['pages_tested'].append(page_result)
    
    async def _test_user_experience(self, page):
        """测试用户体验"""
        print("👤 测试用户体验...")
        
        try:
            # 回到首页
            await page.goto(self.base_url)
            await page.wait_for_timeout(2000)
            
            # 检查导航菜单
            nav_items = await page.query_selector_all('nav a, .nav-item, [class*="nav"]')
            if len(nav_items) > 0:
                print(f"    ✅ 发现 {len(nav_items)} 个导航项")
                self.test_results['user_experience_score'] += 20
            else:
                print("    ❌ 未发现导航菜单")
                self.test_results['issues_found'].append({
                    'type': 'ux_issue',
                    'message': '缺少导航菜单',
                    'timestamp': datetime.now().isoformat()
                })
            
            # 检查响应式设计
            await page.set_viewport_size({"width": 768, "height": 1024})
            await page.wait_for_timeout(1000)
            
            await page.set_viewport_size({"width": 375, "height": 667})
            await page.wait_for_timeout(1000)
            
            await page.set_viewport_size({"width": 1920, "height": 1080})
            print("    ✅ 响应式设计测试完成")
            self.test_results['user_experience_score'] += 15
            
        except Exception as e:
            print(f"    ❌ 用户体验测试出错: {e}")
    
    def _generate_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        # 计算总体评分
        total_pages = len(self.test_results['pages_tested'])
        successful_pages = len([p for p in self.test_results['pages_tested'] if p['status'] == 'loaded'])
        
        if total_pages > 0:
            page_success_rate = (successful_pages / total_pages) * 60
            self.test_results['user_experience_score'] += page_success_rate
        
        # 保存报告
        report_file = f"quick_analysis_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print(f"\n🎯 测试摘要:")
        print(f"   平台状态: {self.test_results['platform_status']}")
        print(f"   测试页面: {total_pages}")
        print(f"   成功加载: {successful_pages}")
        print(f"   发现问题: {len(self.test_results['issues_found'])}")
        print(f"   用户体验评分: {self.test_results['user_experience_score']:.1f}/100")
        print(f"   报告文件: {report_file}")
        
        if self.test_results['platform_status'] == 'accessible':
            print("\n✅ 平台基本可用，建议查看详细报告了解具体问题")
        else:
            print("\n❌ 平台存在严重问题，需要立即修复")

async def main():
    analyzer = QuickPlatformAnalysis()
    await analyzer.run_analysis()

if __name__ == "__main__":
    asyncio.run(main())
