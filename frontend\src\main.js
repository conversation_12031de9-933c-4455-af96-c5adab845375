// 纯JavaScript版本的主入口文件
console.log('🚀 JavaScript版本开始执行')

// 立即执行，不等待DOM
const app = document.getElementById('app')
if (app) {
  console.log('✅ 找到app元素')
  app.innerHTML = `
    <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
      <div style="background: white; padding: 40px; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2c3e50; margin-bottom: 20px;">🚀 量化投资平台</h1>
        <p style="color: #6b7280; margin-bottom: 30px;">JavaScript已成功执行！</p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 30px;">
          <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
            <h3 style="color: #2c3e50; margin-bottom: 8px;">📊 仪表盘</h3>
            <p style="color: #6b7280; font-size: 14px;">投资数据概览</p>
          </div>
          <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="color: #2c3e50; margin-bottom: 8px;">📈 市场行情</h3>
            <p style="color: #6b7280; font-size: 14px;">实时市场数据</p>
          </div>
          <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <h3 style="color: #2c3e50; margin-bottom: 8px;">💰 智能交易</h3>
            <p style="color: #6b7280; font-size: 14px;">自动化交易系统</p>
          </div>
          <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #8b5cf6;">
            <h3 style="color: #2c3e50; margin-bottom: 8px;">🧠 策略研发</h3>
            <p style="color: #6b7280; font-size: 14px;">量化策略开发</p>
          </div>
        </div>
        <button onclick="alert('Vue应用功能开发中...')" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
          🔄 启动完整应用
        </button>
      </div>
    </div>
  `
  console.log('✅ 页面内容已更新')
} else {
  console.error('❌ 未找到app元素')
}

console.log('✅ JavaScript版本执行完成')
