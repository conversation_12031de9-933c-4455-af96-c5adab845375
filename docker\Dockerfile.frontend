# Multi-stage Frontend Dockerfile for Quantitative Trading Platform
# Supports both development and production builds

ARG BUILD_ENV=production
ARG NODE_VERSION=18

# Base stage with common setup
FROM node:${NODE_VERSION}-alpine AS base

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Development stage
FROM base AS development

# Copy package files
COPY package*.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install --frozen-lockfile || npm install --legacy-peer-deps

# Copy source code
COPY . .

# Set development environment variables
ENV NODE_ENV=development \
    VITE_API_BASE_URL=http://backend:${API_PORT:-8000}/api/v1 \
    VITE_WS_URL=ws://backend:${API_PORT:-8000}/ws

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:${FRONTEND_PORT:-5173}/ || exit 1

# Expose port
EXPOSE ${FRONTEND_PORT:-5173}

# Development command
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "${FRONTEND_PORT:-5173}"]

# Build stage
FROM base AS builder

# Copy package files
COPY package*.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install --frozen-lockfile || npm install --legacy-peer-deps

# Copy source code
COPY . .

# Set production build environment
ENV NODE_ENV=production \
    VITE_API_BASE_URL=/api/v1

# Build application
RUN pnpm build || npm run build

# Production stage
FROM nginx:alpine AS production

# Copy built files
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf 2>/dev/null || echo "server { listen 80; location / { try_files \$uri \$uri/ /index.html; } location /api/ { proxy_pass http://backend:${API_PORT:-8000}/api/; } }" > /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# Final stage selection
FROM ${BUILD_ENV} AS final