# Multi-stage Backend Dockerfile for Quantitative Trading Platform
# Supports both development and production builds

ARG BUILD_ENV=production
ARG PYTHON_VERSION=3.11

# Development stage
FROM python:${PYTHON_VERSION}-slim AS development

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Copy requirements
COPY requirements*.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/cache

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:${API_PORT:-8000}/health || exit 1

# Expose port
EXPOSE ${API_PORT:-8000}

# Development command
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "${API_PORT:-8000}", "--reload"]

# Production stage
FROM development AS production

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app

# Switch to non-root user
USER app

# Production command with gunicorn
CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:${API_PORT:-8000}"]

# Simple stage for minimal deployments
FROM development AS simple

# Install minimal dependencies
RUN pip install --no-cache-dir fastapi uvicorn pydantic python-multipart || echo "Using existing dependencies"

# Simple command
CMD ["python", "-m", "uvicorn", "app.main_simple:app", "--host", "0.0.0.0", "--port", "${API_PORT:-8000}"]

# Final stage selection
FROM ${BUILD_ENV} AS final