"""
简化缓存模块
用于提升API响应性能
"""

import time
import json
import hashlib
from typing import Any, Dict, Optional, Callable
from functools import wraps
import asyncio

class SimpleCache:
    """简单内存缓存"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.hits = 0
        self.misses = 0
    
    def _is_expired(self, item: Dict[str, Any]) -> bool:
        """检查是否过期"""
        if 'expires_at' not in item:
            return False
        return time.time() > item['expires_at']
    
    def _cleanup(self):
        """清理过期项"""
        expired_keys = [
            key for key, item in self._cache.items() 
            if self._is_expired(item)
        ]
        for key in expired_keys:
            del self._cache[key]
    
    def _make_room(self):
        """为新项腾出空间"""
        if len(self._cache) >= self.max_size:
            # 删除最旧的项
            oldest_key = min(
                self._cache.keys(),
                key=lambda k: self._cache[k].get('created_at', 0)
            )
            del self._cache[oldest_key]
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        self._cleanup()
        
        if key not in self._cache:
            self.misses += 1
            return None
        
        item = self._cache[key]
        if self._is_expired(item):
            del self._cache[key]
            self.misses += 1
            return None
        
        self.hits += 1
        item['last_accessed'] = time.time()
        return item['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        self._cleanup()
        self._make_room()
        
        ttl = ttl or self.default_ttl
        now = time.time()
        
        self._cache[key] = {
            'value': value,
            'created_at': now,
            'last_accessed': now,
            'expires_at': now + ttl if ttl > 0 else None
        }
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        return self._cache.pop(key, None) is not None
    
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self.hits = 0
        self.misses = 0
    
    def stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        self._cleanup()
        total_requests = self.hits + self.misses
        hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_items': len(self._cache),
            'max_size': self.max_size,
            'usage_percent': (len(self._cache) / self.max_size) * 100,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': round(hit_rate, 2),
            'total_requests': total_requests
        }

# 全局缓存实例
cache = SimpleCache(max_size=1000, default_ttl=300)

def make_cache_key(func_name: str, *args, **kwargs) -> str:
    """生成缓存键"""
    key_data = {
        'func': func_name,
        'args': [str(arg) for arg in args],
        'kwargs': {k: str(v) for k, v in sorted(kwargs.items())}
    }
    key_str = json.dumps(key_data, sort_keys=True)
    return hashlib.md5(key_str.encode()).hexdigest()

def cached(ttl: int = 300, key_prefix: str = "api"):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{make_cache_key(func.__name__, *args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{make_cache_key(func.__name__, *args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def invalidate_pattern(pattern: str):
    """清除匹配模式的缓存"""
    keys_to_delete = [
        key for key in cache._cache.keys()
        if pattern in key
    ]
    for key in keys_to_delete:
        cache.delete(key)
    return len(keys_to_delete)

# 缓存管理函数
def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计"""
    return cache.stats()

def clear_cache(pattern: str = None) -> Dict[str, Any]:
    """清除缓存"""
    if pattern:
        cleared = invalidate_pattern(pattern)
        return {
            "status": "success",
            "cleared_items": cleared,
            "pattern": pattern
        }
    else:
        cache.clear()
        return {
            "status": "success",
            "message": "All cache cleared"
        }

def warm_cache():
    """预热缓存 - 可以在这里添加预热逻辑"""
    pass
