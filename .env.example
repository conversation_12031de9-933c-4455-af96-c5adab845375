# 量化投资平台 - 环境配置示例文件
# 复制此文件为 .env 并修改相应的值

# =============================================================================
# 基础配置
# =============================================================================

# 应用环境 (development, staging, production)
ENVIRONMENT=development

# 应用密钥 (生产环境必须修改)
SECRET_KEY=your-super-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# 应用端口
BACKEND_PORT=8000
FRONTEND_PORT=5173

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL 数据库配置 (生产环境)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=quant_db
POSTGRES_USER=quant_user
POSTGRES_PASSWORD=your-strong-postgres-password

# 数据库连接字符串
# 开发环境（SQLite）
DATABASE_URL=sqlite:///./data/quant.db
# 生产环境（PostgreSQL）
# DATABASE_URL=*******************************************************************/quant_db

# =============================================================================
# Redis 配置
# =============================================================================

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# 外部 API 配置
# =============================================================================

# Tushare API Token
TUSHARE_TOKEN=your-tushare-token-here

# 其他数据源 API
MARKET_DATA_API_KEY=your-market-data-api-key
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key

# =============================================================================
# CTP 交易接口配置
# =============================================================================

# CTP 模拟环境配置
CTP_BROKER_ID=9999
CTP_INVESTOR_ID=your-investor-id
CTP_PASSWORD=your-ctp-password
CTP_TRADE_SERVER=tcp://***************:10201
CTP_MARKET_SERVER=tcp://***************:10211

# CTP 生产环境配置（谨慎使用）
# CTP_PROD_BROKER_ID=your-prod-broker-id
# CTP_PROD_INVESTOR_ID=your-prod-investor-id
# CTP_PROD_PASSWORD=your-prod-password
# CTP_PROD_TRADE_SERVER=tcp://your-prod-trade-server
# CTP_PROD_MARKET_SERVER=tcp://your-prod-market-server

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
LOG_FORMAT=detailed
LOG_FILE_SIZE=10MB
LOG_BACKUP_COUNT=5

# 日志文件路径
LOG_DIR=./logs
TRADING_LOG_FILE=trading.log
ERROR_LOG_FILE=error.log

# =============================================================================
# 安全配置
# =============================================================================

# CORS 配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,https://yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# 允许的主机
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# SSL 配置
SSL_ENABLED=false
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 会话配置
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=900

# =============================================================================
# 性能配置
# =============================================================================

# 工作进程数
WORKERS=4
MAX_WORKERS=8

# 数据库连接池
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis 连接池
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=50

# 缓存配置
CACHE_TTL=3600
MARKET_DATA_CACHE_TTL=60
USER_SESSION_TTL=1800

# =============================================================================
# 监控配置
# =============================================================================

# Prometheus 配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
METRICS_PATH=/metrics

# Grafana 配置
GRAFANA_ENABLED=false
GRAFANA_PORT=3001
GRAFANA_ADMIN_PASSWORD=admin-password-change-me

# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=30

# =============================================================================
# 邮件配置
# =============================================================================

# SMTP 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# =============================================================================
# 第三方集成
# =============================================================================

# 钉钉机器人
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=your-token
DINGTALK_SECRET=your-dingtalk-secret

# Slack 集成
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
SLACK_CHANNEL=#alerts

# =============================================================================
# 开发配置
# =============================================================================

# 开发模式配置
DEBUG=false
DEVELOPMENT_MODE=false
AUTO_RELOAD=false

# 测试配置
TEST_DATABASE_URL=sqlite:///./data/quant_test.db
TEST_REDIS_URL=redis://localhost:6379/1

# =============================================================================
# Docker 和部署配置
# =============================================================================

# Docker 配置
DOCKER_ENV=false
CONTAINER_NAME_PREFIX=quant

# 数据存储路径
DATA_PATH=./data
LOG_PATH=./logs
BACKUP_PATH=./backups

# 用户和组配置（Linux 容器）
UID=1000
GID=1000

# =============================================================================
# 业务配置
# =============================================================================

# 交易配置
TRADING_ENABLED=false
PAPER_TRADING=true
MAX_ORDERS_PER_SECOND=10
ORDER_TIMEOUT=30

# 风险管理
MAX_POSITION_SIZE=100000
MAX_DAILY_LOSS=10000
STOP_LOSS_PERCENTAGE=5.0

# 市场数据
MARKET_DATA_ENABLED=true
REALTIME_DATA_ENABLED=false
HISTORICAL_DATA_RETENTION_DAYS=365

# =============================================================================
# 特性开关
# =============================================================================

# 功能开关
ENABLE_BACKTESTING=true
ENABLE_PAPER_TRADING=true
ENABLE_LIVE_TRADING=false
ENABLE_MARKET_SCANNER=true
ENABLE_ALERTS=true
ENABLE_REPORTS=true

# 实验性功能
EXPERIMENTAL_FEATURES=false
AI_PREDICTIONS_ENABLED=false
ADVANCED_ANALYTICS=false