#!/usr/bin/env python3
"""
Screenshot capture and responsive design testing
"""

import requests
import time
from datetime import datetime
import json
import os

def capture_page_info(url):
    """Capture page information and simulate screenshot"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Extract useful information from HTML
            info = {
                "url": url,
                "status_code": response.status_code,
                "content_length": len(content),
                "title": extract_title(content),
                "has_vue": "vue" in content.lower() or "Vue" in content,
                "has_element_plus": "element-plus" in content.lower(),
                "has_trading_keywords": any(keyword in content.lower() for keyword in 
                    ["trading", "market", "stock", "strategy", "backtest", "portfolio"]),
                "timestamp": datetime.now().isoformat()
            }
            return info
        else:
            return {"url": url, "status_code": response.status_code, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"url": url, "error": str(e)}

def extract_title(html_content):
    """Extract title from HTML content"""
    try:
        start = html_content.lower().find('<title>')
        if start != -1:
            start += 7
            end = html_content.lower().find('</title>', start)
            if end != -1:
                return html_content[start:end].strip()
    except:
        pass
    return "Unknown"

def test_responsive_scenarios():
    """Test different responsive scenarios by checking content"""
    base_url = "http://localhost:5173"
    
    scenarios = [
        {"name": "Homepage", "url": base_url},
        {"name": "Dashboard", "url": f"{base_url}/#/dashboard"},
        {"name": "Trading Center", "url": f"{base_url}/#/trading"},
        {"name": "Market Data", "url": f"{base_url}/#/market"},
        {"name": "Strategy Center", "url": f"{base_url}/#/strategies"},
    ]
    
    results = []
    print("=== RESPONSIVE DESIGN & SCREENSHOT TESTING ===")
    
    for scenario in scenarios:
        print(f"\nTesting: {scenario['name']}")
        info = capture_page_info(scenario["url"])
        
        if "error" not in info:
            print(f"  [PASS] {scenario['name']} - {info['title']}")
            print(f"    Content: {info['content_length']} bytes")
            print(f"    Vue App: {'Yes' if info['has_vue'] else 'No'}")
            print(f"    Trading Content: {'Yes' if info['has_trading_keywords'] else 'No'}")
        else:
            print(f"  [FAIL] {scenario['name']} - {info.get('error', 'Unknown error')}")
        
        results.append(info)
        time.sleep(1)  # Prevent overwhelming the server
    
    return results

def test_interactive_elements():
    """Test for presence of interactive elements by examining HTML"""
    url = "http://localhost:5173"
    
    print("\n=== INTERACTIVE ELEMENTS TESTING ===")
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            content = response.text.lower()
            
            elements_found = {
                "buttons": content.count('<button') + content.count('type="button"'),
                "forms": content.count('<form'),
                "inputs": content.count('<input'),
                "links": content.count('<a ') + content.count('<a\n'),
                "nav_elements": content.count('<nav') + content.count('class="nav"'),
                "vue_components": content.count('v-') + content.count('@click'),
            }
            
            print("Interactive Elements Detection:")
            for element, count in elements_found.items():
                status = "[PASS]" if count > 0 else "[WARN]"
                print(f"  {status} {element.title()}: {count} found")
            
            return elements_found
        else:
            print(f"[FAIL] Could not access page: HTTP {response.status_code}")
            return {}
            
    except Exception as e:
        print(f"[FAIL] Error accessing page: {e}")
        return {}

def check_broken_links():
    """Check for potential broken links by testing common API endpoints"""
    base_url = "http://localhost:8001"
    
    print("\n=== BROKEN LINKS & API CONNECTIVITY TESTING ===")
    
    endpoints = [
        "/api/v1/health",
        "/api/v1/market/stocks", 
        "/api/v1/strategies",
        "/api/v1/auth/login",
        "/docs",
        "/openapi.json"
    ]
    
    broken_links = []
    working_links = []
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint
            response = requests.get(url, timeout=5)
            
            if response.status_code == 404:
                print(f"  [FAIL] Broken link: {endpoint} (404 Not Found)")
                broken_links.append(endpoint)
            elif response.status_code == 500:
                print(f"  [WARN] Server error: {endpoint} (500 Internal Error)")
            elif response.status_code in [200, 401, 422]:
                print(f"  [PASS] Working: {endpoint} (HTTP {response.status_code})")
                working_links.append(endpoint)
            else:
                print(f"  [WARN] Unexpected: {endpoint} (HTTP {response.status_code})")
                
        except Exception as e:
            print(f"  [FAIL] Connection error: {endpoint} - {e}")
            broken_links.append(endpoint)
    
    return {"broken": broken_links, "working": working_links}

def simulate_user_workflows():
    """Simulate real trader workflows"""
    print("\n=== USER WORKFLOW SIMULATION ===")
    
    workflows = [
        {
            "name": "New Trader Onboarding",
            "steps": [
                ("Access Platform", "http://localhost:5173"),
                ("View Market Data", "http://localhost:5173/#/market"),
                ("Check Strategies", "http://localhost:5173/#/strategies"),
                ("Login Attempt", "http://localhost:8001/api/v1/auth/login")
            ]
        },
        {
            "name": "Experienced Trader Session", 
            "steps": [
                ("Dashboard Access", "http://localhost:5173/#/dashboard"),
                ("Portfolio Check", "http://localhost:5173/#/portfolio"),
                ("Trading Interface", "http://localhost:5173/#/trading"),
                ("Risk Management", "http://localhost:5173/#/risk")
            ]
        }
    ]
    
    workflow_results = []
    
    for workflow in workflows:
        print(f"\nSimulating: {workflow['name']}")
        workflow_result = {"name": workflow["name"], "steps": []}
        
        for step_name, url in workflow["steps"]:
            try:
                if url.startswith("http://localhost:5173"):
                    # Frontend test
                    response = requests.get(url, timeout=10)
                    success = response.status_code == 200
                    details = f"HTTP {response.status_code}"
                elif url.startswith("http://localhost:8001"):
                    # API test (POST for login)
                    if "login" in url:
                        response = requests.post(url, json={"username":"demo","password":"demo"}, timeout=5)
                    else:
                        response = requests.get(url, timeout=5)
                    success = response.status_code in [200, 401, 422]  # Expected responses
                    details = f"HTTP {response.status_code}"
                else:
                    success = False
                    details = "Invalid URL"
                
                status = "[PASS]" if success else "[FAIL]"
                print(f"    {status} {step_name}: {details}")
                
                workflow_result["steps"].append({
                    "step": step_name,
                    "url": url,
                    "success": success,
                    "details": details
                })
                
                time.sleep(0.5)  # Small delay between requests
                
            except Exception as e:
                print(f"    [FAIL] {step_name}: {e}")
                workflow_result["steps"].append({
                    "step": step_name,
                    "url": url,
                    "success": False,
                    "details": str(e)
                })
        
        workflow_results.append(workflow_result)
    
    return workflow_results

def generate_final_testing_report():
    """Generate comprehensive testing report"""
    print("\n" + "="*70)
    print("COMPREHENSIVE REAL USER TESTING - FINAL REPORT")
    print("="*70)
    
    # Run all tests
    screenshot_results = test_responsive_scenarios()
    interactive_results = test_interactive_elements()
    link_results = check_broken_links()
    workflow_results = simulate_user_workflows()
    
    # Compile final report
    final_report = {
        "test_summary": {
            "date": datetime.now().isoformat(),
            "frontend_url": "http://localhost:5173",
            "backend_url": "http://localhost:8001",
            "total_tests_run": len(screenshot_results) + len(interactive_results) + len(link_results["working"]) + len(link_results["broken"])
        },
        "responsive_testing": screenshot_results,
        "interactive_elements": interactive_results,
        "link_testing": link_results,
        "user_workflows": workflow_results,
        "overall_assessment": generate_assessment(screenshot_results, interactive_results, link_results, workflow_results)
    }
    
    # Save report
    report_filename = f"final_real_user_test_report_{int(time.time())}.json"
    with open(report_filename, "w", encoding="utf-8") as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False)
    
    print(f"\nFinal testing report saved to: {report_filename}")
    return final_report

def generate_assessment(screenshot_results, interactive_results, link_results, workflow_results):
    """Generate overall assessment"""
    
    # Count successful tests
    successful_pages = sum(1 for r in screenshot_results if r.get("status_code") == 200)
    interactive_elements_found = sum(interactive_results.values())
    working_endpoints = len(link_results["working"]) 
    broken_endpoints = len(link_results["broken"])
    
    successful_workflows = 0
    total_workflow_steps = 0
    
    for workflow in workflow_results:
        successful_steps = sum(1 for step in workflow["steps"] if step["success"])
        total_steps = len(workflow["steps"])
        total_workflow_steps += total_steps
        if successful_steps / total_steps > 0.5:  # More than half steps successful
            successful_workflows += 1
    
    assessment = {
        "frontend_health": "Good" if successful_pages >= 4 else "Poor",
        "interactive_elements": "Present" if interactive_elements_found > 5 else "Limited",
        "api_connectivity": "Partial" if working_endpoints > broken_endpoints else "Poor",
        "user_experience": "Functional" if successful_workflows > 0 else "Needs Work",
        "overall_rating": calculate_overall_rating(successful_pages, interactive_elements_found, working_endpoints, successful_workflows),
        "key_strengths": [],
        "critical_issues": [],
        "recommendations": []
    }
    
    # Determine strengths and issues
    if successful_pages >= 4:
        assessment["key_strengths"].append("Frontend pages load successfully")
    
    if interactive_elements_found > 10:
        assessment["key_strengths"].append("Rich interactive interface detected")
    
    if working_endpoints > 2:
        assessment["key_strengths"].append("Some API endpoints working")
    
    if broken_endpoints > working_endpoints:
        assessment["critical_issues"].append("More API endpoints failing than working")
    
    if interactive_elements_found < 5:
        assessment["critical_issues"].append("Limited interactive elements detected")
    
    if successful_workflows == 0:
        assessment["critical_issues"].append("User workflows experiencing failures")
    
    # Generate recommendations
    if broken_endpoints > 0:
        assessment["recommendations"].append("Fix broken API endpoints and server errors")
    
    if interactive_elements_found < 10:
        assessment["recommendations"].append("Enhance user interface with more interactive elements")
    
    if successful_workflows < len(workflow_results):
        assessment["recommendations"].append("Improve user workflow reliability")
    
    assessment["recommendations"].append("Implement proper error handling and user feedback")
    assessment["recommendations"].append("Add comprehensive logging for debugging issues")
    
    return assessment

def calculate_overall_rating(pages, interactive, api_endpoints, workflows):
    """Calculate overall rating out of 5 stars"""
    score = 0
    
    # Frontend (max 2 points)
    if pages >= 4:
        score += 2
    elif pages >= 2:
        score += 1
    
    # Interactivity (max 1 point)
    if interactive > 10:
        score += 1
    elif interactive > 5:
        score += 0.5
    
    # API (max 1 point)
    if api_endpoints >= 3:
        score += 1
    elif api_endpoints >= 1:
        score += 0.5
    
    # Workflows (max 1 point)
    if workflows >= 2:
        score += 1
    elif workflows >= 1:
        score += 0.5
    
    return min(5, int(score))

if __name__ == "__main__":
    generate_final_testing_report()