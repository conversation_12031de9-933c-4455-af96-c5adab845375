"""Add performance optimization features

Revision ID: 003
Revises: 002
Create Date: 2025-01-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create performance optimization indexes for frequently queried tables
    
    # Market data performance indexes
    op.create_index(
        'ix_market_data_symbol_timestamp_desc',
        'market_data',
        ['symbol_id', sa.text('timestamp DESC')]
    )
    
    # K-line data performance indexes
    op.create_index(
        'ix_kline_data_symbol_type_timestamp_desc',
        'kline_data',
        ['symbol_id', 'kline_type', sa.text('timestamp DESC')]
    )
    
    # Orders performance indexes
    op.create_index(
        'ix_orders_user_status_created',
        'orders',
        ['user_id', 'status', 'created_at']
    )
    
    op.create_index(
        'ix_orders_symbol_status',
        'orders', 
        ['symbol_id', 'status']
    )
    
    # Trades performance indexes
    op.create_index(
        'ix_trades_user_executed_at',
        'trades',
        ['user_id', 'executed_at']
    )
    
    op.create_index(
        'ix_trades_symbol_executed_at',
        'trades',
        ['symbol_id', 'executed_at']
    )
    
    # Strategy instances performance indexes
    op.create_index(
        'ix_strategy_instances_user_status',
        'strategy_instances',
        ['user_id', 'status']
    )
    
    # Backtest tasks performance indexes
    op.create_index(
        'ix_backtest_tasks_user_status_created',
        'backtest_tasks',
        ['user_id', 'status', 'created_at']
    )
    
    # CTP tick data partitioning preparation (add check constraint for future partitioning)
    op.execute("""
        ALTER TABLE ctp_tick_data 
        ADD CONSTRAINT check_trading_day_format 
        CHECK (trading_day ~ '^[0-9]{8}$')
    """)
    
    # Create materialized view for market data summary
    op.execute("""
        CREATE MATERIALIZED VIEW market_data_daily_summary AS
        SELECT 
            s.symbol,
            s.name,
            s.market,
            md.timestamp::date as trade_date,
            FIRST_VALUE(md.open_price) OVER (
                PARTITION BY md.symbol_id, md.timestamp::date 
                ORDER BY md.timestamp ASC 
                ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            ) as day_open,
            MAX(md.high_price) as day_high,
            MIN(md.low_price) as day_low,
            LAST_VALUE(md.close_price) OVER (
                PARTITION BY md.symbol_id, md.timestamp::date 
                ORDER BY md.timestamp ASC 
                ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            ) as day_close,
            SUM(md.volume) as day_volume,
            SUM(md.amount) as day_amount,
            COUNT(*) as tick_count
        FROM market_data md
        JOIN symbols s ON md.symbol_id = s.id
        GROUP BY s.symbol, s.name, s.market, md.symbol_id, md.timestamp::date
        ORDER BY md.timestamp::date DESC, s.symbol
    """)
    
    # Create index on materialized view
    op.create_index(
        'ix_market_data_daily_summary_symbol_date',
        'market_data_daily_summary',
        ['symbol', 'trade_date']
    )
    
    # Create function to refresh materialized view
    op.execute("""
        CREATE OR REPLACE FUNCTION refresh_market_data_summary()
        RETURNS void AS $$
        BEGIN
            REFRESH MATERIALIZED VIEW CONCURRENTLY market_data_daily_summary;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Create table for storing query performance statistics
    op.create_table('query_performance_stats',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('query_hash', sa.String(length=64), nullable=False),
        sa.Column('query_type', sa.String(length=50), nullable=False),
        sa.Column('table_name', sa.String(length=100)),
        sa.Column('execution_time_ms', sa.Integer(), nullable=False),
        sa.Column('row_count', sa.Integer()),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id')),
        sa.Column('endpoint', sa.String(length=200)),
        sa.Column('parameters', sa.JSON()),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    
    op.create_index('ix_query_performance_stats_hash', 'query_performance_stats', ['query_hash'])
    op.create_index('ix_query_performance_stats_type_time', 'query_performance_stats', ['query_type', 'created_at'])
    op.create_index('ix_query_performance_stats_execution_time', 'query_performance_stats', ['execution_time_ms'])
    
    # Create table for database health monitoring
    op.create_table('database_health_metrics',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('metric_name', sa.String(length=100), nullable=False),
        sa.Column('metric_value', sa.Numeric(20, 4), nullable=False),
        sa.Column('metric_unit', sa.String(length=20)),
        sa.Column('threshold_warning', sa.Numeric(20, 4)),
        sa.Column('threshold_critical', sa.Numeric(20, 4)),
        sa.Column('status', sa.Enum('HEALTHY', 'WARNING', 'CRITICAL', name='healthstatus'), nullable=False, default='HEALTHY'),
        sa.Column('details', sa.JSON()),
        sa.Column('recorded_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    
    op.create_index('ix_database_health_metrics_name_time', 'database_health_metrics', ['metric_name', 'recorded_at'])
    op.create_index('ix_database_health_metrics_status', 'database_health_metrics', ['status'])
    
    # Create table for storing schema audit results
    op.create_table('schema_audit_results',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('audit_id', sa.String(length=36), nullable=False, unique=True),
        sa.Column('status', sa.Enum('HEALTHY', 'WARNING', 'ERROR', 'CRITICAL', name='auditstatus'), nullable=False),
        sa.Column('total_tables', sa.Integer(), nullable=False),
        sa.Column('total_indexes', sa.Integer(), nullable=False),
        sa.Column('total_foreign_keys', sa.Integer(), nullable=False),
        sa.Column('database_size_mb', sa.Numeric(20, 4), nullable=False),
        sa.Column('database_version', sa.String(length=50)),
        sa.Column('alembic_version', sa.String(length=50)),
        sa.Column('issues_count', sa.Integer(), nullable=False, default=0),
        sa.Column('critical_issues_count', sa.Integer(), nullable=False, default=0),
        sa.Column('issues_details', sa.JSON()),
        sa.Column('table_metrics', sa.JSON()),
        sa.Column('recommendations', sa.JSON()),
        sa.Column('audit_duration_seconds', sa.Integer()),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    
    op.create_index('ix_schema_audit_results_status', 'schema_audit_results', ['status'])
    op.create_index('ix_schema_audit_results_created_at', 'schema_audit_results', ['created_at'])
    
    # Create function for automatic statistics collection
    op.execute("""
        CREATE OR REPLACE FUNCTION update_table_statistics()
        RETURNS void AS $$
        DECLARE
            table_record RECORD;
        BEGIN
            FOR table_record IN 
                SELECT schemaname, tablename 
                FROM pg_tables 
                WHERE schemaname = 'public'
            LOOP
                EXECUTE 'ANALYZE ' || quote_ident(table_record.schemaname) || '.' || quote_ident(table_record.tablename);
            END LOOP;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Create connection pool monitoring table
    op.create_table('connection_pool_metrics',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('pool_name', sa.String(length=50), nullable=False),
        sa.Column('pool_size', sa.Integer(), nullable=False),
        sa.Column('checked_out', sa.Integer(), nullable=False),
        sa.Column('overflow', sa.Integer(), nullable=False),
        sa.Column('checked_in', sa.Integer(), nullable=False),
        sa.Column('total_connections', sa.Integer(), nullable=False),
        sa.Column('utilization_percent', sa.Numeric(5, 2), nullable=False),
        sa.Column('average_checkout_time_ms', sa.Integer()),
        sa.Column('recorded_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    
    op.create_index('ix_connection_pool_metrics_pool_time', 'connection_pool_metrics', ['pool_name', 'recorded_at'])
    
    # Create slow query log table
    op.create_table('slow_query_log',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('query_hash', sa.String(length=64), nullable=False),
        sa.Column('query_text', sa.Text(), nullable=False),
        sa.Column('execution_time_ms', sa.Integer(), nullable=False),
        sa.Column('rows_examined', sa.Integer()),
        sa.Column('rows_returned', sa.Integer()),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id')),
        sa.Column('database_name', sa.String(length=100)),
        sa.Column('table_names', sa.ARRAY(sa.String(length=100))),
        sa.Column('query_plan', sa.JSON()),
        sa.Column('parameters', sa.JSON()),
        sa.Column('endpoint', sa.String(length=200)),
        sa.Column('ip_address', sa.String(length=45)),
        sa.Column('user_agent', sa.String(length=500)),
        sa.Column('execution_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    
    op.create_index('ix_slow_query_log_execution_time', 'slow_query_log', ['execution_time_ms'])
    op.create_index('ix_slow_query_log_timestamp', 'slow_query_log', ['execution_timestamp'])
    op.create_index('ix_slow_query_log_hash', 'slow_query_log', ['query_hash'])
    op.create_index('ix_slow_query_log_user_id', 'slow_query_log', ['user_id'])
    
    print("✅ Performance optimization features added successfully")


def downgrade() -> None:
    # Drop tables
    op.drop_table('slow_query_log')
    op.drop_table('connection_pool_metrics')
    op.drop_table('schema_audit_results')
    op.drop_table('database_health_metrics')
    op.drop_table('query_performance_stats')
    
    # Drop functions
    op.execute('DROP FUNCTION IF EXISTS update_table_statistics()')
    op.execute('DROP FUNCTION IF EXISTS refresh_market_data_summary()')
    
    # Drop materialized view
    op.execute('DROP MATERIALIZED VIEW IF EXISTS market_data_daily_summary')
    
    # Drop constraints
    op.execute('ALTER TABLE ctp_tick_data DROP CONSTRAINT IF EXISTS check_trading_day_format')
    
    # Drop performance indexes
    op.drop_index('ix_market_data_symbol_timestamp_desc')
    op.drop_index('ix_kline_data_symbol_type_timestamp_desc')
    op.drop_index('ix_orders_user_status_created')
    op.drop_index('ix_orders_symbol_status')
    op.drop_index('ix_trades_user_executed_at')
    op.drop_index('ix_trades_symbol_executed_at')
    op.drop_index('ix_strategy_instances_user_status')
    op.drop_index('ix_backtest_tasks_user_status_created')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS healthstatus')
    op.execute('DROP TYPE IF EXISTS auditstatus')
    
    print("✅ Performance optimization features removed successfully")