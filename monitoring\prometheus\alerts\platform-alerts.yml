# Quantitative Trading Platform - Core Alert Rules
# Production-ready alerting for critical system components

groups:
  - name: quant_platform_critical
    interval: 30s
    rules:
      # === Service Availability ===
      
      - alert: BackendServiceDown
        expr: up{job="quant-platform-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: backend
          team: platform
          runbook: "https://docs.company.com/runbooks/backend-down"
        annotations:
          title: "🚨 Backend Service is Down"
          summary: "Quant Platform backend service is unreachable"
          description: "Backend service at {{ $labels.instance }} has been down for more than 1 minute"
          impact: "All API functionality is unavailable"
          action: "Check service status, review logs, restart if necessary"

      - alert: FrontendServiceDown
        expr: up{job="quant-platform-frontend"} == 0
        for: 2m
        labels:
          severity: critical
          service: frontend
          team: platform
        annotations:
          title: "🚨 Frontend Service is Down"
          summary: "Web interface is unreachable"
          description: "Frontend service at {{ $labels.instance }} has been down for more than 2 minutes"

      - alert: DatabaseDown
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
          team: platform
        annotations:
          title: "🚨 Database is Down"
          summary: "PostgreSQL database is unreachable"
          description: "Database at {{ $labels.instance }} has been unreachable for more than 1 minute"
          impact: "All data operations are blocked"

      - alert: RedisDown
        expr: up{job="redis-exporter"} == 0
        for: 2m
        labels:
          severity: critical
          service: cache
          team: platform
        annotations:
          title: "🚨 Redis Cache is Down"
          summary: "Redis cache service is unreachable"
          description: "Redis at {{ $labels.instance }} has been down for more than 2 minutes"

      # === Performance Degradation ===
      
      - alert: HighAPILatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="quant-platform-backend"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: backend
          team: platform
        annotations:
          title: "⚠️ High API Latency"
          summary: "95th percentile API response time is elevated"
          description: "95% of API requests are taking longer than 2 seconds (current: {{ $value | humanizeDuration }})"
          dashboard: "https://grafana.company.com/d/api-performance"

      - alert: CriticalAPILatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="quant-platform-backend"}[5m])) > 5
        for: 2m
        labels:
          severity: critical
          service: backend
          team: platform
        annotations:
          title: "🚨 Critical API Latency"
          summary: "API response time critically elevated"
          description: "95% of API requests taking longer than 5 seconds (current: {{ $value | humanizeDuration }})"

      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{job="quant-platform-backend",code=~"5.."}[5m]) /
            rate(http_requests_total{job="quant-platform-backend"}[5m])
          ) * 100 > 5
        for: 3m
        labels:
          severity: warning
          service: backend
          team: platform
        annotations:
          title: "⚠️ High Error Rate"
          summary: "Elevated 5xx error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} over the last 5 minutes"

      - alert: CriticalErrorRate
        expr: |
          (
            rate(http_requests_total{job="quant-platform-backend",code=~"5.."}[5m]) /
            rate(http_requests_total{job="quant-platform-backend"}[5m])
          ) * 100 > 20
        for: 1m
        labels:
          severity: critical
          service: backend
          team: platform
        annotations:
          title: "🚨 Critical Error Rate"
          summary: "Critically high 5xx error rate"
          description: "Error rate is {{ $value | humanizePercentage }} - immediate investigation required"

  - name: quant_platform_resources
    interval: 30s
    rules:
      # === Resource Utilization ===
      
      - alert: HighCPUUsage
        expr: |
          (
            rate(process_cpu_seconds_total{job="quant-platform-backend"}[5m]) * 100
          ) > 80
        for: 10m
        labels:
          severity: warning
          service: backend
          team: platform
        annotations:
          title: "⚠️ High CPU Usage"
          summary: "Backend service CPU usage is elevated"
          description: "CPU usage at {{ $labels.instance }} is {{ $value | humanizePercentage }} for 10+ minutes"

      - alert: CriticalCPUUsage
        expr: |
          (
            rate(process_cpu_seconds_total{job="quant-platform-backend"}[5m]) * 100
          ) > 95
        for: 5m
        labels:
          severity: critical
          service: backend
          team: platform
        annotations:
          title: "🚨 Critical CPU Usage"
          summary: "Backend service CPU usage critically high"
          description: "CPU usage at {{ $labels.instance }} is {{ $value | humanizePercentage }}"

      - alert: HighMemoryUsage
        expr: |
          (
            process_resident_memory_bytes{job="quant-platform-backend"} / 1024 / 1024 / 1024
          ) > 6
        for: 10m
        labels:
          severity: warning
          service: backend
          team: platform
        annotations:
          title: "⚠️ High Memory Usage"
          summary: "Backend service memory usage is elevated"
          description: "Memory usage at {{ $labels.instance }} is {{ $value | humanize }}GB"

      - alert: CriticalMemoryUsage
        expr: |
          (
            process_resident_memory_bytes{job="quant-platform-backend"} / 1024 / 1024 / 1024
          ) > 10
        for: 5m
        labels:
          severity: critical
          service: backend
          team: platform
        annotations:
          title: "🚨 Critical Memory Usage"
          summary: "Backend service memory usage critically high"
          description: "Memory usage at {{ $labels.instance }} is {{ $value | humanize }}GB"

      # === Storage & Disk Space ===
      
      - alert: LowDiskSpace
        expr: |
          (
            node_filesystem_avail_bytes{mountpoint="/",fstype!="tmpfs"} /
            node_filesystem_size_bytes{mountpoint="/",fstype!="tmpfs"}
          ) * 100 < 20
        for: 5m
        labels:
          severity: warning
          service: system
          team: platform
        annotations:
          title: "⚠️ Low Disk Space"
          summary: "Disk space is running low"
          description: "Available disk space at {{ $labels.instance }} is {{ $value | humanizePercentage }}"

      - alert: CriticalDiskSpace
        expr: |
          (
            node_filesystem_avail_bytes{mountpoint="/",fstype!="tmpfs"} /
            node_filesystem_size_bytes{mountpoint="/",fstype!="tmpfs"}
          ) * 100 < 10
        for: 2m
        labels:
          severity: critical
          service: system
          team: platform
        annotations:
          title: "🚨 Critical Disk Space"
          summary: "Disk space critically low"
          description: "Available disk space at {{ $labels.instance }} is only {{ $value | humanizePercentage }}"

      # === Database Performance ===
      
      - alert: DatabaseConnectionsHigh
        expr: |
          (
            pg_stat_database_numbackends{datname="quant_db"} /
            pg_settings_max_connections
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: database
          team: platform
        annotations:
          title: "⚠️ High Database Connections"
          summary: "Database connection pool usage is high"
          description: "Database connections at {{ $value | humanizePercentage }} of maximum"

      - alert: DatabaseSlowQueries
        expr: |
          rate(pg_stat_database_tup_returned{datname="quant_db"}[5m]) /
          rate(pg_stat_database_tup_fetched{datname="quant_db"}[5m]) > 100
        for: 10m
        labels:
          severity: warning
          service: database
          team: platform
        annotations:
          title: "⚠️ Database Slow Queries"
          summary: "Potential slow queries detected"
          description: "Query performance may be degraded"

      # === Redis Performance ===
      
      - alert: RedisMemoryHigh
        expr: |
          (
            redis_memory_used_bytes /
            redis_config_maxmemory
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: cache
          team: platform
        annotations:
          title: "⚠️ High Redis Memory Usage"
          summary: "Redis memory usage is elevated"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      - alert: RedisConnectionsHigh
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: cache
          team: platform
        annotations:
          title: "⚠️ High Redis Connections"
          summary: "High number of Redis connections"
          description: "Redis has {{ $value }} active connections"