# Quantitative Trading Platform - Docker Management
# Standardized commands for container orchestration

.PHONY: help build up down logs clean dev prod monitoring health status

# Default environment
ENV ?= development

# Docker compose files
COMPOSE_BASE = -f docker-compose.base.yml
COMPOSE_DEV = $(COMPOSE_BASE) -f docker-compose.development.yml
COMPOSE_PROD = $(COMPOSE_BASE) -f docker-compose.production.yml
COMPOSE_MONITORING = -f docker-compose.monitoring.yml

# Help command
help: ## Show this help message
	@echo "Quantitative Trading Platform - Docker Management"
	@echo "================================================="
	@echo
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Environment setup
init: ## Initialize environment (create .env from template)
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env file from template"; \
		echo "⚠️  Please edit .env with your configuration"; \
	else \
		echo "❌ .env file already exists"; \
	fi

# Build commands
build: ## Build all containers
	docker-compose $(COMPOSE_DEV) build

build-prod: ## Build production containers
	docker-compose $(COMPOSE_PROD) build

build-no-cache: ## Build containers without cache
	docker-compose $(COMPOSE_DEV) build --no-cache

# Development environment
dev: ## Start development environment
	docker-compose $(COMPOSE_DEV) up -d
	@echo "🚀 Development environment started"
	@echo "   Backend:  http://localhost:8000"
	@echo "   Frontend: http://localhost:5173"
	@echo "   API Docs: http://localhost:8000/docs"

dev-logs: ## Show development logs
	docker-compose $(COMPOSE_DEV) logs -f

dev-down: ## Stop development environment
	docker-compose $(COMPOSE_DEV) down

# Production environment
prod: ## Start production environment
	docker-compose $(COMPOSE_PROD) up -d
	@echo "🚀 Production environment started"
	@echo "   Application: http://localhost"
	@echo "   API: http://localhost/api"

prod-logs: ## Show production logs
	docker-compose $(COMPOSE_PROD) logs -f

prod-down: ## Stop production environment
	docker-compose $(COMPOSE_PROD) down

# Monitoring stack
monitoring: ## Start monitoring stack
	docker-compose $(COMPOSE_MONITORING) up -d
	@echo "📊 Monitoring stack started"
	@echo "   Prometheus: http://localhost:9090"
	@echo "   Grafana:    http://localhost:3001"
	@echo "   AlertMgr:   http://localhost:9093"

monitoring-logs: ## Show monitoring logs
	docker-compose $(COMPOSE_MONITORING) logs -f

monitoring-down: ## Stop monitoring stack
	docker-compose $(COMPOSE_MONITORING) down

# Full stack management
full-dev: ## Start full development stack with monitoring
	$(MAKE) dev
	$(MAKE) monitoring

full-prod: ## Start full production stack with monitoring
	$(MAKE) prod
	$(MAKE) monitoring

# Database management
db-migrate: ## Run database migrations
	docker-compose $(COMPOSE_DEV) exec backend alembic upgrade head

db-reset: ## Reset database (WARNING: destroys data)
	docker-compose $(COMPOSE_DEV) exec backend alembic downgrade base
	docker-compose $(COMPOSE_DEV) exec backend alembic upgrade head

db-shell: ## Open database shell
	docker-compose $(COMPOSE_DEV) exec postgres psql -U quant_user -d quant_db

# Service management
restart: ## Restart all services
	docker-compose $(COMPOSE_DEV) restart

restart-backend: ## Restart backend service
	docker-compose $(COMPOSE_DEV) restart backend

restart-frontend: ## Restart frontend service
	docker-compose $(COMPOSE_DEV) restart frontend

# Logs and debugging
logs: ## Show all logs
	docker-compose $(COMPOSE_DEV) logs -f

logs-backend: ## Show backend logs
	docker-compose $(COMPOSE_DEV) logs -f backend

logs-frontend: ## Show frontend logs
	docker-compose $(COMPOSE_DEV) logs -f frontend

shell-backend: ## Open backend shell
	docker-compose $(COMPOSE_DEV) exec backend bash

shell-frontend: ## Open frontend shell
	docker-compose $(COMPOSE_DEV) exec frontend sh

# Health and status
health: ## Check service health
	@echo "🏥 Checking service health..."
	@docker-compose $(COMPOSE_DEV) exec backend curl -f http://localhost:8000/health || echo "❌ Backend unhealthy"
	@docker-compose $(COMPOSE_DEV) exec frontend wget -q --spider http://localhost:5173 || echo "❌ Frontend unhealthy"

status: ## Show service status
	docker-compose $(COMPOSE_DEV) ps

# Cleanup commands
clean: ## Stop and remove all containers, networks, and volumes
	docker-compose $(COMPOSE_DEV) down -v --remove-orphans
	docker-compose $(COMPOSE_PROD) down -v --remove-orphans
	docker-compose $(COMPOSE_MONITORING) down -v --remove-orphans
	docker system prune -f

clean-images: ## Remove all project-related images
	docker images | grep quant | awk '{print $$3}' | xargs docker rmi -f

clean-volumes: ## Remove all project volumes (WARNING: destroys data)
	docker volume ls | grep quant | awk '{print $$2}' | xargs docker volume rm

# Testing commands
test: ## Run backend tests
	docker-compose $(COMPOSE_DEV) exec backend python -m pytest

test-coverage: ## Run tests with coverage
	docker-compose $(COMPOSE_DEV) exec backend python -m pytest --cov=app --cov-report=html

lint: ## Run code linting
	docker-compose $(COMPOSE_DEV) exec backend python -m flake8 app/
	docker-compose $(COMPOSE_DEV) exec frontend npm run lint

# Backup and restore
backup: ## Backup database
	@mkdir -p ./backups
	docker-compose $(COMPOSE_DEV) exec postgres pg_dump -U quant_user -d quant_db > ./backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Database backup created in ./backups/"

restore: ## Restore database from backup (set BACKUP_FILE variable)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "❌ Please set BACKUP_FILE variable: make restore BACKUP_FILE=backup_20231201_120000.sql"; \
		exit 1; \
	fi
	docker-compose $(COMPOSE_DEV) exec -T postgres psql -U quant_user -d quant_db < ./backups/$(BACKUP_FILE)
	@echo "✅ Database restored from $(BACKUP_FILE)"