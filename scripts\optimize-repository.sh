#!/bin/bash
# 量化投资平台 - Git仓库优化脚本
# 用于优化Git仓库大小和性能

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 脚本目录
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 默认配置
AGGRESSIVE_CLEANUP=false
BACKUP_ENABLED=true
DRY_RUN=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
量化投资平台 Git 仓库优化脚本

用法: $0 [选项]

选项:
    -a, --aggressive    激进清理模式 (删除所有历史大文件)
    -b, --no-backup     跳过备份
    -d, --dry-run       预演模式，不实际执行
    -h, --help          显示此帮助信息

功能:
    1. 检查仓库状态和大小
    2. 清理未跟踪的文件
    3. 优化Git对象存储
    4. 清理悬挂对象
    5. 重新打包仓库
    6. 清理大文件历史记录 (可选)

示例:
    $0                  # 标准优化
    $0 -a              # 激进优化模式
    $0 -d              # 预演模式
    
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--aggressive)
                AGGRESSIVE_CLEANUP=true
                shift
                ;;
            -b|--no-backup)
                BACKUP_ENABLED=false
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Git仓库状态
check_git_repo() {
    log_info "检查Git仓库状态..."
    
    if [[ ! -d ".git" ]]; then
        log_error "当前目录不是Git仓库"
        exit 1
    fi
    
    # 检查是否有未提交的更改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        log_warning "仓库有未提交的更改"
        if [[ "$DRY_RUN" == "false" ]]; then
            read -p "是否继续? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "操作已取消"
                exit 0
            fi
        fi
    fi
    
    log_success "Git仓库状态检查完成"
}

# 获取仓库大小信息
get_repo_size() {
    local git_dir_size=$(du -sh .git 2>/dev/null | cut -f1 || echo "unknown")
    local work_dir_size=$(du -sh --exclude='.git' . 2>/dev/null | cut -f1 || echo "unknown")
    
    echo "仓库信息:"
    echo "  Git目录大小: $git_dir_size"
    echo "  工作目录大小: $work_dir_size"
    echo "  分支数量: $(git branch -a | wc -l)"
    echo "  提交数量: $(git rev-list --all --count)"
    echo "  远程仓库: $(git remote -v | wc -l)"
}

# 备份仓库
backup_repo() {
    if [[ "$BACKUP_ENABLED" == "false" ]]; then
        log_info "跳过备份"
        return
    fi
    
    log_info "创建仓库备份..."
    
    local backup_name="repo_backup_$(date +%Y%m%d_%H%M%S)"
    local backup_path="../${backup_name}.tar.gz"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预演模式：将创建备份到 $backup_path"
        return
    fi
    
    tar -czf "$backup_path" --exclude='.git/objects/pack/*.tmp*' .git
    
    if [[ -f "$backup_path" ]]; then
        log_success "备份已创建: $backup_path"
    else
        log_error "备份创建失败"
        exit 1
    fi
}

# 清理未跟踪的文件
clean_untracked() {
    log_info "清理未跟踪的文件..."
    
    # 显示将要删除的文件
    local untracked_files=$(git clean -n -d -x)
    if [[ -n "$untracked_files" ]]; then
        echo "将要删除的未跟踪文件:"
        echo "$untracked_files"
        
        if [[ "$DRY_RUN" == "false" ]]; then
            read -p "确定要删除这些文件? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                git clean -f -d -x
                log_success "未跟踪文件清理完成"
            else
                log_info "跳过未跟踪文件清理"
            fi
        else
            log_info "预演模式：将清理上述未跟踪文件"
        fi
    else
        log_info "没有发现未跟踪的文件"
    fi
}

# 查找历史中的大文件
find_large_files() {
    log_info "查找历史记录中的大文件..."
    
    # 查找大于1MB的文件
    local large_files=$(git rev-list --objects --all | \
        git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
        awk '/^blob/ {if($3 > 1048576) print $3, $4}' | \
        sort -nr | \
        head -20)
    
    if [[ -n "$large_files" ]]; then
        echo "历史记录中的大文件 (>1MB):"
        echo "$large_files" | while read size file; do
            size_mb=$(echo "scale=2; $size / 1048576" | bc)
            echo "  ${size_mb}MB - $file"
        done
        
        return 0
    else
        log_info "没有发现大文件"
        return 1
    fi
}

# 清理大文件历史
clean_large_files() {
    if [[ "$AGGRESSIVE_CLEANUP" == "false" ]]; then
        log_info "跳过大文件清理 (使用 -a 选项启用)"
        return
    fi
    
    log_warning "激进清理模式：这将重写Git历史!"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        read -p "确定要继续? 这个操作不可逆! (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过大文件清理"
            return
        fi
    fi
    
    log_info "清理历史中的大文件..."
    
    # 使用 git filter-branch 清理大文件
    local patterns=(
        "*.log"
        "*.db"
        "*.sqlite*"
        "*.tmp"
        "*.cache"
        "node_modules/"
        "venv/"
        "dist/"
        "build/"
        "__pycache__/"
        "*.png"
        "*.jpg"
        "*.jpeg"
        "*.gif"
        "*.mp4"
        "*.zip"
        "*.tar.gz"
        "logs/"
        "cache/"
        "data/"
        "screenshots/"
    )
    
    for pattern in "${patterns[@]}"; do
        if [[ "$DRY_RUN" == "false" ]]; then
            git filter-branch --force --index-filter \
                "git rm --cached --ignore-unmatch -r '$pattern'" \
                --prune-empty --tag-name-filter cat -- --all || true
        else
            log_info "预演模式：将从历史中移除 $pattern"
        fi
    done
    
    if [[ "$DRY_RUN" == "false" ]]; then
        # 清理filter-branch的备份
        rm -rf .git/refs/original/
        
        # 使所有引用过期
        git reflog expire --expire=now --all
        
        log_success "大文件历史清理完成"
    fi
}

# Git仓库垃圾回收
git_garbage_collection() {
    log_info "执行Git垃圾回收..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预演模式：将执行垃圾回收操作"
        return
    fi
    
    # 清理悬挂对象
    git fsck --unreachable 2>/dev/null | wc -l | xargs echo "悬挂对象数量:"
    
    # 过期reflog条目
    git reflog expire --expire-unreachable=now --all
    
    # 激进的垃圾回收
    git gc --aggressive --prune=now
    
    # 重新打包仓库
    git repack -ad
    
    log_success "垃圾回收完成"
}

# 优化Git配置
optimize_git_config() {
    log_info "优化Git配置..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预演模式：将优化Git配置"
        return
    fi
    
    # 设置合理的配置值
    git config gc.auto 1000
    git config gc.autopacklimit 50
    git config core.preloadindex true
    git config core.fscache true
    git config gc.pruneexpire "30 days ago"
    
    log_success "Git配置优化完成"
}

# 检查.gitignore效果
check_gitignore() {
    log_info "检查.gitignore配置效果..."
    
    # 检查是否有被忽略但仍在索引中的文件
    local ignored_in_index=$(git ls-files -i --exclude-standard)
    
    if [[ -n "$ignored_in_index" ]]; then
        log_warning "发现被忽略但仍在Git索引中的文件:"
        echo "$ignored_in_index"
        
        if [[ "$DRY_RUN" == "false" ]]; then
            read -p "是否从索引中移除这些文件? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                echo "$ignored_in_index" | xargs git rm --cached
                log_success "已从索引中移除被忽略的文件"
            fi
        fi
    else
        log_success ".gitignore配置正确"
    fi
}

# 生成优化报告
generate_report() {
    log_info "生成优化报告..."
    
    local report_file="optimization_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Git仓库优化报告"
        echo "================"
        echo "优化时间: $(date)"
        echo "仓库路径: $(pwd)"
        echo ""
        
        echo "优化前后对比:"
        get_repo_size
        echo ""
        
        echo "执行的优化操作:"
        echo "- 清理未跟踪文件"
        echo "- Git垃圾回收"
        echo "- 重新打包对象"
        echo "- 配置优化"
        if [[ "$AGGRESSIVE_CLEANUP" == "true" ]]; then
            echo "- 清理大文件历史 (激进模式)"
        fi
        echo ""
        
        echo "当前仓库统计:"
        echo "Git对象数量: $(git count-objects -v | grep '^count' | cut -d' ' -f2)"
        echo "打包文件数量: $(git count-objects -v | grep '^packs' | cut -d' ' -f2)"
        echo "松散对象数量: $(git count-objects -v | grep '^count' | cut -d' ' -f2)"
        
    } > "$report_file"
    
    log_success "优化报告已保存: $report_file"
}

# 主函数
main() {
    log_info "开始Git仓库优化"
    echo "=================="
    
    cd "$PROJECT_ROOT"
    
    # 检查Git仓库
    check_git_repo
    
    # 显示优化前的仓库信息
    echo ""
    log_info "优化前的仓库信息:"
    get_repo_size
    echo ""
    
    # 备份仓库
    backup_repo
    
    # 检查.gitignore
    check_gitignore
    
    # 清理未跟踪文件
    clean_untracked
    
    # 查找大文件
    if find_large_files; then
        clean_large_files
    fi
    
    # Git垃圾回收
    git_garbage_collection
    
    # 优化Git配置
    optimize_git_config
    
    # 显示优化后的仓库信息
    echo ""
    log_info "优化后的仓库信息:"
    get_repo_size
    echo ""
    
    # 生成报告
    generate_report
    
    log_success "🎉 仓库优化完成!"
    echo ""
    echo "建议的后续操作:"
    echo "1. 检查仓库状态: git status"
    echo "2. 验证重要文件: git log --oneline -10"
    echo "3. 推送更改: git push --force-with-lease (谨慎使用)"
    echo "4. 通知团队成员重新克隆仓库 (如果执行了激进清理)"
}

# 错误处理
trap 'log_error "优化过程中发生错误"; exit 1' ERR

# 解析参数并运行主函数
parse_args "$@"
main