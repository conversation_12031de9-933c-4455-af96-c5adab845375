# Consolidated Prometheus Configuration
# Quantitative Trading Platform - Production Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s
  external_labels:
    cluster: 'quant-platform'
    environment: 'production'
    region: 'us-east-1'

# Rule files
rule_files:
  - "/etc/prometheus/rules/*.yml"
  - "/etc/prometheus/alerts/*.yml"

# Alertmanager configuration
alerting:
  alert_relabel_configs:
    - source_labels: [dc]
      target_label: datacenter
  alertmanagers:
    - static_configs:
        - targets:
            - 'quant-platform-alertmanager:9093'
      timeout: 10s
      api_version: v1

# Scrape configurations
scrape_configs:
  # === Core Platform Services ===
  
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # Quantitative Trading Platform Backend
  - job_name: 'quant-platform-backend'
    static_configs:
      - targets: ['quant-platform-backend:8000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: '(.*):.*'
        target_label: hostname
        replacement: '${1}'

  # Application Metrics (Business Logic)
  - job_name: 'quant-platform-business-metrics'
    static_configs:
      - targets: ['quant-platform-backend:8001']
    scrape_interval: 30s
    metrics_path: /metrics/business
    honor_labels: true

  # Frontend/Nginx
  - job_name: 'quant-platform-frontend'
    static_configs:
      - targets: ['quant-platform-frontend:80']
    scrape_interval: 15s
    metrics_path: /nginx_status
    honor_labels: true

  # === Database Services ===
  
  # PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s
    honor_labels: true

  # Redis Exporter
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    honor_labels: true

  # === Task Processing ===
  
  # Celery Worker Monitoring
  - job_name: 'celery-worker'
    static_configs:
      - targets: ['quant-platform-celery-worker:9100']
    scrape_interval: 15s
    honor_labels: true

  # Celery Beat Monitoring
  - job_name: 'celery-beat'
    static_configs:
      - targets: ['quant-platform-celery-beat:9100']
    scrape_interval: 30s
    honor_labels: true

  # === Infrastructure Monitoring ===
  
  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: 
          - 'node-exporter:9100'
          - 'quant-platform-backend:9100'
          - 'quant-platform-celery-worker:9100'
    scrape_interval: 15s
    honor_labels: true

  # cAdvisor (Container Metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    honor_labels: true
    relabel_configs:
      - source_labels: [__name__]
        regex: 'container_.*'
        target_label: __tmp_container_metric
      - source_labels: [__tmp_container_metric]
        regex: 'container_.*'
        target_label: metric_type
        replacement: 'container'

  # === External Health Monitoring ===
  
  # Blackbox Exporter (External Probes)
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
          - https://your-domain.com
          - https://your-domain.com/api/v1/health
          - https://your-domain.com/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # TCP/SSL Certificate Monitoring
  - job_name: 'blackbox-tcp'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
          - 'quant-platform-postgres:5432'
          - 'quant-platform-redis:6379'
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # === Kubernetes Integration (if deployed on K8s) ===
  
  # Kubernetes API Server
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names: ['default']
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  # Kubernetes Nodes
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      insecure_skip_verify: true
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)

  # Kubernetes Pods
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
        namespaces:
          names: ['quant-platform']
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"
#     write_relabel_configs:
#       - source_labels: [__name__]
#         regex: 'go_.*|prometheus_.*|up|scrape_.*'
#         action: drop

# Remote read configuration
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"