#!/bin/bash

# 项目清理脚本
# 用于清理临时文件、缓存和不必要的文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "开始清理项目: $PROJECT_ROOT"

# 清理前端
cleanup_frontend() {
    log_info "清理前端项目..."
    
    if [ -d "frontend" ]; then
        cd frontend
        
        # 清理 node_modules (可选)
        if [ "$1" = "--deep" ]; then
            log_warning "深度清理: 删除 node_modules"
            rm -rf node_modules
            rm -f package-lock.json
        fi
        
        # 清理构建产物
        log_info "清理构建产物..."
        rm -rf dist/
        rm -rf .vite/
        rm -rf .nuxt/
        rm -rf .output/
        
        # 清理缓存文件
        log_info "清理缓存文件..."
        rm -rf .cache/
        rm -rf .temp/
        rm -rf .tmp/
        
        # 清理测试报告
        log_info "清理测试报告..."
        rm -rf coverage/
        rm -rf test-results/
        rm -rf playwright-report/
        
        # 清理日志文件
        log_info "清理日志文件..."
        find . -name "*.log" -type f -delete 2>/dev/null || true
        find . -name "npm-debug.log*" -type f -delete 2>/dev/null || true
        find . -name "yarn-debug.log*" -type f -delete 2>/dev/null || true
        find . -name "yarn-error.log*" -type f -delete 2>/dev/null || true
        
        # 清理临时文件
        log_info "清理临时文件..."
        find . -name ".DS_Store" -type f -delete 2>/dev/null || true
        find . -name "Thumbs.db" -type f -delete 2>/dev/null || true
        find . -name "*.tmp" -type f -delete 2>/dev/null || true
        find . -name "*.temp" -type f -delete 2>/dev/null || true
        
        cd ..
        log_success "前端清理完成"
    else
        log_warning "前端目录不存在"
    fi
}

# 清理后端
cleanup_backend() {
    log_info "清理后端项目..."
    
    if [ -d "backend" ]; then
        cd backend
        
        # 清理 Python 缓存
        log_info "清理 Python 缓存..."
        find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
        find . -name "*.pyc" -type f -delete 2>/dev/null || true
        find . -name "*.pyo" -type f -delete 2>/dev/null || true
        find . -name "*.pyd" -type f -delete 2>/dev/null || true
        
        # 清理虚拟环境 (可选)
        if [ "$1" = "--deep" ]; then
            log_warning "深度清理: 删除虚拟环境"
            rm -rf venv/
            rm -rf .venv/
            rm -rf env/
        fi
        
        # 清理测试和覆盖率报告
        log_info "清理测试报告..."
        rm -rf .coverage
        rm -rf htmlcov/
        rm -rf .pytest_cache/
        rm -rf .tox/
        
        # 清理构建产物
        log_info "清理构建产物..."
        rm -rf build/
        rm -rf dist/
        rm -rf *.egg-info/
        
        # 清理日志文件
        log_info "清理日志文件..."
        find . -name "*.log" -type f -delete 2>/dev/null || true
        rm -rf logs/*.log 2>/dev/null || true
        
        # 清理临时文件
        log_info "清理临时文件..."
        find . -name ".DS_Store" -type f -delete 2>/dev/null || true
        find . -name "Thumbs.db" -type f -delete 2>/dev/null || true
        find . -name "*.tmp" -type f -delete 2>/dev/null || true
        find . -name "*.temp" -type f -delete 2>/dev/null || true
        
        cd ..
        log_success "后端清理完成"
    else
        log_warning "后端目录不存在"
    fi
}

# 清理根目录
cleanup_root() {
    log_info "清理根目录..."
    
    # 清理日志文件
    log_info "清理根目录日志..."
    rm -rf logs/*.log 2>/dev/null || true
    find . -maxdepth 1 -name "*.log" -type f -delete 2>/dev/null || true
    
    # 清理临时文件
    log_info "清理临时文件..."
    find . -maxdepth 1 -name ".DS_Store" -type f -delete 2>/dev/null || true
    find . -maxdepth 1 -name "Thumbs.db" -type f -delete 2>/dev/null || true
    find . -maxdepth 1 -name "*.tmp" -type f -delete 2>/dev/null || true
    find . -maxdepth 1 -name "*.temp" -type f -delete 2>/dev/null || true
    
    # 清理数据缓存
    log_info "清理数据缓存..."
    rm -rf data/cache/ 2>/dev/null || true
    rm -rf data/daily_cache/ 2>/dev/null || true
    rm -rf cache/ 2>/dev/null || true
    
    # 清理Docker相关
    if [ "$1" = "--docker" ]; then
        log_info "清理Docker相关..."
        docker system prune -f 2>/dev/null || true
        docker volume prune -f 2>/dev/null || true
    fi
    
    log_success "根目录清理完成"
}

# 清理测试相关
cleanup_tests() {
    log_info "清理测试相关文件..."
    
    # 清理测试报告
    rm -rf tests/reports/ 2>/dev/null || true
    rm -rf test-results/ 2>/dev/null || true
    rm -rf coverage/ 2>/dev/null || true
    
    # 清理截图和视频
    rm -rf tests/screenshots/ 2>/dev/null || true
    rm -rf tests/videos/ 2>/dev/null || true
    rm -rf mcp/puppeteer/screenshots/ 2>/dev/null || true
    
    log_success "测试文件清理完成"
}

# 显示帮助信息
show_help() {
    echo "项目清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help          显示此帮助信息"
    echo "  --deep          深度清理 (删除 node_modules, venv 等)"
    echo "  --docker        清理 Docker 相关"
    echo "  --frontend-only 仅清理前端"
    echo "  --backend-only  仅清理后端"
    echo "  --tests-only    仅清理测试文件"
    echo ""
    echo "示例:"
    echo "  $0                    # 标准清理"
    echo "  $0 --deep            # 深度清理"
    echo "  $0 --frontend-only   # 仅清理前端"
}

# 主函数
main() {
    case "$1" in
        --help)
            show_help
            exit 0
            ;;
        --frontend-only)
            cleanup_frontend "$2"
            ;;
        --backend-only)
            cleanup_backend "$2"
            ;;
        --tests-only)
            cleanup_tests
            ;;
        *)
            cleanup_frontend "$1"
            cleanup_backend "$1"
            cleanup_root "$1"
            cleanup_tests
            ;;
    esac
    
    log_success "项目清理完成! 🎉"
    
    # 显示清理后的磁盘使用情况
    if command -v du &> /dev/null; then
        log_info "当前项目大小:"
        du -sh . 2>/dev/null || echo "无法计算项目大小"
    fi
}

# 执行主函数
main "$@"
