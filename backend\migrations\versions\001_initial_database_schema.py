"""Initial database schema

Revision ID: 001
Revises: 
Create Date: 2025-01-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.<PERSON>umn('username', sa.String(length=50), nullable=False, unique=True),
        sa.Column('email', sa.String(length=100), nullable=False, unique=True),
        sa.Column('password_hash', sa.String(length=255), nullable=False),
        sa.Column('role', sa.Enum('ADMIN', 'USER', 'VIEWER', name='userrole'), nullable=False, default='USER'),
        sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'BANNED', name='userstatus'), nullable=False, default='ACTIVE'),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
        sa.Column('is_verified', sa.Boolean(), nullable=False, default=False),
        sa.Column('full_name', sa.String(length=100)),
        sa.Column('phone', sa.String(length=20)),
        sa.Column('avatar_url', sa.String(length=255)),
        sa.Column('last_login', sa.DateTime(timezone=True)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_users_username', 'users', ['username'])
    op.create_index('ix_users_email', 'users', ['email'])
    op.create_index('ix_users_status', 'users', ['status'])

    # Create user_sessions table
    op.create_table('user_sessions',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('session_token', sa.String(length=255), nullable=False, unique=True),
        sa.Column('ip_address', sa.String(length=45)),
        sa.Column('user_agent', sa.String(length=500)),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
    )
    op.create_index('ix_user_sessions_token', 'user_sessions', ['session_token'])
    op.create_index('ix_user_sessions_user_id', 'user_sessions', ['user_id'])

    # Create symbols table
    op.create_table('symbols',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('symbol', sa.String(length=20), nullable=False, unique=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('market', sa.Enum('SH', 'SZ', 'BJ', name='markettype'), nullable=False),
        sa.Column('sector', sa.String(length=50)),
        sa.Column('industry', sa.String(length=50)),
        sa.Column('list_date', sa.Date()),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_symbols_symbol', 'symbols', ['symbol'])
    op.create_index('ix_symbols_market', 'symbols', ['market'])
    op.create_index('ix_symbols_sector', 'symbols', ['sector'])

    # Create market_data table
    op.create_table('market_data',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('symbol_id', sa.Integer(), sa.ForeignKey('symbols.id'), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('open_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('high_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('low_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('close_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('volume', sa.BigInteger(), nullable=False),
        sa.Column('amount', sa.Numeric(20, 4)),
        sa.Column('turnover_rate', sa.Numeric(8, 4)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_market_data_symbol_timestamp', 'market_data', ['symbol_id', 'timestamp'], unique=True)
    op.create_index('ix_market_data_timestamp', 'market_data', ['timestamp'])

    # Create kline_data table
    op.create_table('kline_data',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('symbol_id', sa.Integer(), sa.ForeignKey('symbols.id'), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('kline_type', sa.Enum('1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M', name='klinetype'), nullable=False),
        sa.Column('open_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('high_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('low_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('close_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('volume', sa.BigInteger(), nullable=False),
        sa.Column('amount', sa.Numeric(20, 4)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_kline_data_symbol_type_timestamp', 'kline_data', ['symbol_id', 'kline_type', 'timestamp'], unique=True)
    op.create_index('ix_kline_data_timestamp', 'kline_data', ['timestamp'])

    # Create accounts table
    op.create_table('accounts',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('account_id', sa.String(length=50), nullable=False, unique=True),
        sa.Column('account_name', sa.String(length=100), nullable=False),
        sa.Column('account_type', sa.String(length=20), nullable=False, default='SIMULATED'),
        sa.Column('broker', sa.String(length=50)),
        sa.Column('total_value', sa.Numeric(20, 4), nullable=False, default=100000),
        sa.Column('available_cash', sa.Numeric(20, 4), nullable=False, default=100000),
        sa.Column('frozen_cash', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('market_value', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('total_pnl', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('daily_pnl', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_accounts_user_id', 'accounts', ['user_id'])
    op.create_index('ix_accounts_account_id', 'accounts', ['account_id'])

    # Create orders table
    op.create_table('orders',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('account_id', sa.Integer(), sa.ForeignKey('accounts.id'), nullable=False),
        sa.Column('symbol_id', sa.Integer(), sa.ForeignKey('symbols.id'), nullable=False),
        sa.Column('order_id', sa.String(length=50), nullable=False, unique=True),
        sa.Column('client_order_id', sa.String(length=50)),
        sa.Column('order_type', sa.Enum('MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT', name='ordertype'), nullable=False),
        sa.Column('side', sa.Enum('BUY', 'SELL', name='orderside'), nullable=False),
        sa.Column('quantity', sa.Integer(), nullable=False),
        sa.Column('price', sa.Numeric(10, 4)),
        sa.Column('stop_price', sa.Numeric(10, 4)),
        sa.Column('status', sa.Enum('PENDING', 'PARTIAL', 'FILLED', 'CANCELLED', 'REJECTED', name='orderstatus'), nullable=False, default='PENDING'),
        sa.Column('filled_quantity', sa.Integer(), nullable=False, default=0),
        sa.Column('remaining_quantity', sa.Integer(), nullable=False),
        sa.Column('avg_fill_price', sa.Numeric(10, 4)),
        sa.Column('commission', sa.Numeric(10, 4)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('filled_at', sa.DateTime(timezone=True)),
    )
    op.create_index('ix_orders_user_id', 'orders', ['user_id'])
    op.create_index('ix_orders_account_id', 'orders', ['account_id'])
    op.create_index('ix_orders_symbol_id', 'orders', ['symbol_id'])
    op.create_index('ix_orders_status', 'orders', ['status'])
    op.create_index('ix_orders_created_at', 'orders', ['created_at'])

    # Create trades table
    op.create_table('trades',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('account_id', sa.Integer(), sa.ForeignKey('accounts.id'), nullable=False),
        sa.Column('order_id', sa.Integer(), sa.ForeignKey('orders.id'), nullable=False),
        sa.Column('symbol_id', sa.Integer(), sa.ForeignKey('symbols.id'), nullable=False),
        sa.Column('trade_id', sa.String(length=50), nullable=False, unique=True),
        sa.Column('side', sa.Enum('BUY', 'SELL', name='orderside'), nullable=False),
        sa.Column('quantity', sa.Integer(), nullable=False),
        sa.Column('price', sa.Numeric(10, 4), nullable=False),
        sa.Column('amount', sa.Numeric(20, 4), nullable=False),
        sa.Column('commission', sa.Numeric(10, 4)),
        sa.Column('executed_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_trades_user_id', 'trades', ['user_id'])
    op.create_index('ix_trades_account_id', 'trades', ['account_id'])
    op.create_index('ix_trades_order_id', 'trades', ['order_id'])
    op.create_index('ix_trades_symbol_id', 'trades', ['symbol_id'])
    op.create_index('ix_trades_executed_at', 'trades', ['executed_at'])

    # Create positions table
    op.create_table('positions',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('account_id', sa.Integer(), sa.ForeignKey('accounts.id'), nullable=False),
        sa.Column('symbol_id', sa.Integer(), sa.ForeignKey('symbols.id'), nullable=False),
        sa.Column('side', sa.Enum('LONG', 'SHORT', name='positionside'), nullable=False),
        sa.Column('quantity', sa.Integer(), nullable=False, default=0),
        sa.Column('frozen_quantity', sa.Integer(), nullable=False, default=0),
        sa.Column('avg_price', sa.Numeric(10, 4)),
        sa.Column('market_value', sa.Numeric(20, 4)),
        sa.Column('unrealized_pnl', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('realized_pnl', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('total_cost', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_positions_user_id', 'positions', ['user_id'])
    op.create_index('ix_positions_account_id', 'positions', ['account_id'])
    op.create_index('ix_positions_symbol_id', 'positions', ['symbol_id'])
    op.create_index('ix_positions_account_symbol', 'positions', ['account_id', 'symbol_id'], unique=True)

    # Create strategies table
    op.create_table('strategies',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text()),
        sa.Column('strategy_type', sa.Enum('MOMENTUM', 'MEAN_REVERSION', 'PAIRS_TRADING', 'ARBITRAGE', 'FACTOR', 'ML', name='strategytype'), nullable=False),
        sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'PAUSED', 'TESTING', name='strategystatus'), nullable=False, default='INACTIVE'),
        sa.Column('risk_level', sa.Enum('LOW', 'MEDIUM', 'HIGH', name='risklevel'), nullable=False, default='MEDIUM'),
        sa.Column('code', sa.Text(), nullable=False),
        sa.Column('parameters', sa.JSON()),
        sa.Column('universe', sa.JSON()),  # List of symbols
        sa.Column('max_positions', sa.Integer(), nullable=False, default=10),
        sa.Column('position_size', sa.Numeric(8, 4), nullable=False, default=0.1),
        sa.Column('stop_loss', sa.Numeric(8, 4)),
        sa.Column('take_profit', sa.Numeric(8, 4)),
        sa.Column('max_drawdown', sa.Numeric(8, 4), nullable=False, default=0.2),
        sa.Column('benchmark', sa.String(length=20), nullable=False, default='000300.SH'),
        sa.Column('version', sa.String(length=20), nullable=False, default='1.0.0'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_strategies_user_id', 'strategies', ['user_id'])
    op.create_index('ix_strategies_status', 'strategies', ['status'])
    op.create_index('ix_strategies_type', 'strategies', ['strategy_type'])
    op.create_index('ix_strategies_name', 'strategies', ['name'])

    # Create strategy_instances table
    op.create_table('strategy_instances',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('strategy_id', sa.Integer(), sa.ForeignKey('strategies.id'), nullable=False),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('account_id', sa.Integer(), sa.ForeignKey('accounts.id'), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('status', sa.Enum('RUNNING', 'STOPPED', 'ERROR', name='strategystatus'), nullable=False, default='STOPPED'),
        sa.Column('allocated_capital', sa.Numeric(20, 4), nullable=False),
        sa.Column('current_value', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('total_pnl', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('daily_pnl', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('max_drawdown', sa.Numeric(8, 4), nullable=False, default=0),
        sa.Column('sharpe_ratio', sa.Numeric(8, 4)),
        sa.Column('win_rate', sa.Numeric(8, 4)),
        sa.Column('trade_count', sa.Integer(), nullable=False, default=0),
        sa.Column('last_signal_time', sa.DateTime(timezone=True)),
        sa.Column('started_at', sa.DateTime(timezone=True)),
        sa.Column('stopped_at', sa.DateTime(timezone=True)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_strategy_instances_strategy_id', 'strategy_instances', ['strategy_id'])
    op.create_index('ix_strategy_instances_user_id', 'strategy_instances', ['user_id'])
    op.create_index('ix_strategy_instances_status', 'strategy_instances', ['status'])

    # Create backtest_tasks table
    op.create_table('backtest_tasks',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('strategy_id', sa.Integer(), sa.ForeignKey('strategies.id'), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text()),
        sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='backteststatus'), nullable=False, default='PENDING'),
        sa.Column('backtest_type', sa.Enum('SIMPLE', 'WALK_FORWARD', 'MONTE_CARLO', name='backtesttype'), nullable=False, default='SIMPLE'),
        sa.Column('start_date', sa.Date(), nullable=False),
        sa.Column('end_date', sa.Date(), nullable=False),
        sa.Column('initial_capital', sa.Numeric(20, 4), nullable=False, default=1000000),
        sa.Column('commission_rate', sa.Numeric(8, 6), nullable=False, default=0.0003),
        sa.Column('slippage_rate', sa.Numeric(8, 6), nullable=False, default=0.001),
        sa.Column('benchmark', sa.String(length=20), nullable=False, default='000300.SH'),
        sa.Column('parameters', sa.JSON()),
        sa.Column('progress', sa.Integer(), nullable=False, default=0),
        sa.Column('error_message', sa.Text()),
        sa.Column('started_at', sa.DateTime(timezone=True)),
        sa.Column('completed_at', sa.DateTime(timezone=True)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_backtest_tasks_user_id', 'backtest_tasks', ['user_id'])
    op.create_index('ix_backtest_tasks_strategy_id', 'backtest_tasks', ['strategy_id'])
    op.create_index('ix_backtest_tasks_status', 'backtest_tasks', ['status'])

    # Create backtest_results table
    op.create_table('backtest_results',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('task_id', sa.Integer(), sa.ForeignKey('backtest_tasks.id'), nullable=False),
        sa.Column('total_return', sa.Numeric(8, 4), nullable=False),
        sa.Column('annual_return', sa.Numeric(8, 4), nullable=False),
        sa.Column('max_drawdown', sa.Numeric(8, 4), nullable=False),
        sa.Column('sharpe_ratio', sa.Numeric(8, 4)),
        sa.Column('calmar_ratio', sa.Numeric(8, 4)),
        sa.Column('sortino_ratio', sa.Numeric(8, 4)),
        sa.Column('volatility', sa.Numeric(8, 4)),
        sa.Column('win_rate', sa.Numeric(8, 4)),
        sa.Column('profit_factor', sa.Numeric(8, 4)),
        sa.Column('total_trades', sa.Integer(), nullable=False, default=0),
        sa.Column('winning_trades', sa.Integer(), nullable=False, default=0),
        sa.Column('losing_trades', sa.Integer(), nullable=False, default=0),
        sa.Column('avg_trade_return', sa.Numeric(8, 4)),
        sa.Column('avg_win_return', sa.Numeric(8, 4)),
        sa.Column('avg_loss_return', sa.Numeric(8, 4)),
        sa.Column('benchmark_return', sa.Numeric(8, 4)),
        sa.Column('alpha', sa.Numeric(8, 4)),
        sa.Column('beta', sa.Numeric(8, 4)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_backtest_results_task_id', 'backtest_results', ['task_id'], unique=True)

    # Create watchlists table
    op.create_table('watchlists',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('symbol_id', sa.Integer(), sa.ForeignKey('symbols.id'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_watchlists_user_symbol', 'watchlists', ['user_id', 'symbol_id'], unique=True)

    print("✅ Initial database schema created successfully")


def downgrade() -> None:
    # Drop tables in reverse order to respect foreign key constraints
    op.drop_table('watchlists')
    op.drop_table('backtest_results')
    op.drop_table('backtest_tasks')
    op.drop_table('strategy_instances')
    op.drop_table('strategies')
    op.drop_table('positions')
    op.drop_table('trades')
    op.drop_table('orders')
    op.drop_table('accounts')
    op.drop_table('kline_data')
    op.drop_table('market_data')
    op.drop_table('symbols')
    op.drop_table('user_sessions')
    op.drop_table('users')

    # Drop enums
    op.execute('DROP TYPE IF EXISTS userrole')
    op.execute('DROP TYPE IF EXISTS userstatus')
    op.execute('DROP TYPE IF EXISTS markettype')
    op.execute('DROP TYPE IF EXISTS klinetype')
    op.execute('DROP TYPE IF EXISTS ordertype')
    op.execute('DROP TYPE IF EXISTS orderside')
    op.execute('DROP TYPE IF EXISTS orderstatus')
    op.execute('DROP TYPE IF EXISTS positionside')
    op.execute('DROP TYPE IF EXISTS strategytype')
    op.execute('DROP TYPE IF EXISTS strategystatus')
    op.execute('DROP TYPE IF EXISTS risklevel')
    op.execute('DROP TYPE IF EXISTS backteststatus')
    op.execute('DROP TYPE IF EXISTS backtesttype')

    print("✅ Database schema downgraded successfully")