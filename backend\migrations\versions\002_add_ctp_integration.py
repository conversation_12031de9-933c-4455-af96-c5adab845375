"""Add CTP integration tables

Revision ID: 002
Revises: 001
Create Date: 2025-01-08 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create CTP accounts table
    op.create_table('ctp_accounts',
        sa.<PERSON>umn('id', sa.Integer(), primary_key=True),
        sa.<PERSON>umn('user_id', sa.Integer(), sa.<PERSON>('users.id'), nullable=False),
        sa.Column('broker_id', sa.String(length=20), nullable=False),
        sa.Column('account_id', sa.String(length=20), nullable=False),
        sa.Column('password', sa.String(length=255), nullable=False),  # 加密存储
        sa.Column('trading_day', sa.String(length=10)),
        sa.Column('front_id', sa.Integer()),
        sa.Column('session_id', sa.Integer()),
        sa.Column('max_order_ref', sa.Integer(), default=0),
        sa.Column('status', sa.Enum('CONNECTED', 'DISCONNECTED', 'CONNECTING', 'ERROR', name='ctpaccountstatus'), nullable=False, default='DISCONNECTED'),
        sa.Column('last_login', sa.DateTime(timezone=True)),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_ctp_accounts_user_id', 'ctp_accounts', ['user_id'])
    op.create_index('ix_ctp_accounts_broker_account', 'ctp_accounts', ['broker_id', 'account_id'], unique=True)
    
    # Create CTP market data subscriptions table
    op.create_table('ctp_subscriptions',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('instrument_id', sa.String(length=50), nullable=False),
        sa.Column('exchange_id', sa.String(length=10)),
        sa.Column('subscription_type', sa.Enum('TICK', 'KLINE', 'DEPTH', name='subscriptiontype'), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_ctp_subscriptions_user_instrument', 'ctp_subscriptions', ['user_id', 'instrument_id'])
    
    # Create CTP orders table (extends the general orders table)
    op.create_table('ctp_orders',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('order_id', sa.Integer(), sa.ForeignKey('orders.id'), nullable=False),
        sa.Column('ctp_account_id', sa.Integer(), sa.ForeignKey('ctp_accounts.id'), nullable=False),
        sa.Column('instrument_id', sa.String(length=50), nullable=False),
        sa.Column('exchange_id', sa.String(length=10)),
        sa.Column('order_ref', sa.String(length=20), nullable=False),
        sa.Column('front_id', sa.Integer()),
        sa.Column('session_id', sa.Integer()),
        sa.Column('order_sys_id', sa.String(length=50)),
        sa.Column('direction', sa.Enum('BUY', 'SELL', name='ctpdirection'), nullable=False),
        sa.Column('offset_flag', sa.Enum('OPEN', 'CLOSE', 'CLOSE_TODAY', 'CLOSE_YESTERDAY', name='ctpoffsetflag'), nullable=False),
        sa.Column('hedge_flag', sa.Enum('SPECULATION', 'ARBITRAGE', 'HEDGE', name='ctphedgeflag'), nullable=False, default='SPECULATION'),
        sa.Column('order_price_type', sa.Enum('ANY_PRICE', 'LIMIT_PRICE', 'BEST_PRICE', 'LAST_PRICE', name='ctppricetype'), nullable=False),
        sa.Column('time_condition', sa.Enum('IOC', 'GFS', 'GFD', 'GTD', 'GTC', 'GFA', name='ctptimecondition'), nullable=False, default='GFD'),
        sa.Column('volume_condition', sa.Enum('AV', 'MV', 'CV', name='ctpvolumecondition'), nullable=False, default='AV'),
        sa.Column('contingent_condition', sa.Enum('IMMEDIATELY', 'TOUCH', 'TOUCH_PROFIT', 'PARKED_ORDER', name='ctpcontingentcondition'), nullable=False, default='IMMEDIATELY'),
        sa.Column('force_close_reason', sa.Enum('NOT_FORCE_CLOSE', 'LACK_DEPOSIT', 'CLIENT_OVER_POSITION_LIMIT', name='ctpforceclosereason'), nullable=False, default='NOT_FORCE_CLOSE'),
        sa.Column('status_msg', sa.String(length=255)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_ctp_orders_order_id', 'ctp_orders', ['order_id'], unique=True)
    op.create_index('ix_ctp_orders_account_id', 'ctp_orders', ['ctp_account_id'])
    op.create_index('ix_ctp_orders_instrument_id', 'ctp_orders', ['instrument_id'])
    op.create_index('ix_ctp_orders_order_ref', 'ctp_orders', ['order_ref'])
    
    # Create CTP positions table
    op.create_table('ctp_positions',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('ctp_account_id', sa.Integer(), sa.ForeignKey('ctp_accounts.id'), nullable=False),
        sa.Column('instrument_id', sa.String(length=50), nullable=False),
        sa.Column('broker_id', sa.String(length=20), nullable=False),
        sa.Column('investor_id', sa.String(length=20), nullable=False),
        sa.Column('position_date', sa.Enum('TODAY', 'HISTORY', name='ctppositiondate'), nullable=False),
        sa.Column('hedge_flag', sa.Enum('SPECULATION', 'ARBITRAGE', 'HEDGE', name='ctphedgeflag'), nullable=False),
        sa.Column('position_direction', sa.Enum('NET', 'LONG', 'SHORT', name='ctppositiondirection'), nullable=False),
        sa.Column('yd_position', sa.Integer(), nullable=False, default=0),  # 昨仓
        sa.Column('position', sa.Integer(), nullable=False, default=0),     # 今仓
        sa.Column('long_frozen', sa.Integer(), nullable=False, default=0),  # 多头冻结
        sa.Column('short_frozen', sa.Integer(), nullable=False, default=0), # 空头冻结
        sa.Column('long_frozen_amount', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('short_frozen_amount', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('open_volume', sa.Integer(), nullable=False, default=0),
        sa.Column('close_volume', sa.Integer(), nullable=False, default=0),
        sa.Column('open_amount', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('close_amount', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('position_cost', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('pre_margin', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('use_margin', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('frozen_margin', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('frozen_cash', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('frozen_commission', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('cash_in', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('commission', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('close_profit', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('position_profit', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('pre_settlement_price', sa.Numeric(10, 4), nullable=False, default=0),
        sa.Column('settlement_price', sa.Numeric(10, 4), nullable=False, default=0),
        sa.Column('trading_day', sa.String(length=10)),
        sa.Column('settlement_id', sa.Integer(), default=0),
        sa.Column('open_cost', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('exchange_margin', sa.Numeric(20, 4), nullable=False, default=0),
        sa.Column('combine_position', sa.Integer(), nullable=False, default=0),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_ctp_positions_account_instrument', 'ctp_positions', ['ctp_account_id', 'instrument_id'])
    op.create_index('ix_ctp_positions_investor_instrument', 'ctp_positions', ['investor_id', 'instrument_id'])
    
    # Create CTP tick data table
    op.create_table('ctp_tick_data',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('trading_day', sa.String(length=10), nullable=False),
        sa.Column('instrument_id', sa.String(length=50), nullable=False),
        sa.Column('exchange_id', sa.String(length=10)),
        sa.Column('exchange_inst_id', sa.String(length=50)),
        sa.Column('last_price', sa.Numeric(10, 4), nullable=False),
        sa.Column('pre_settlement_price', sa.Numeric(10, 4)),
        sa.Column('pre_close_price', sa.Numeric(10, 4)),
        sa.Column('pre_open_interest', sa.BigInteger(), default=0),
        sa.Column('open_price', sa.Numeric(10, 4)),
        sa.Column('highest_price', sa.Numeric(10, 4)),
        sa.Column('lowest_price', sa.Numeric(10, 4)),
        sa.Column('volume', sa.Integer(), default=0),
        sa.Column('turnover', sa.Numeric(20, 4), default=0),
        sa.Column('open_interest', sa.BigInteger(), default=0),
        sa.Column('close_price', sa.Numeric(10, 4)),
        sa.Column('settlement_price', sa.Numeric(10, 4)),
        sa.Column('upper_limit_price', sa.Numeric(10, 4)),
        sa.Column('lower_limit_price', sa.Numeric(10, 4)),
        sa.Column('pre_delta', sa.Numeric(10, 4)),
        sa.Column('curr_delta', sa.Numeric(10, 4)),
        sa.Column('update_time', sa.String(length=10)),
        sa.Column('update_millisec', sa.Integer()),
        sa.Column('bid_price_1', sa.Numeric(10, 4)),
        sa.Column('bid_volume_1', sa.Integer()),
        sa.Column('ask_price_1', sa.Numeric(10, 4)),
        sa.Column('ask_volume_1', sa.Integer()),
        sa.Column('bid_price_2', sa.Numeric(10, 4)),
        sa.Column('bid_volume_2', sa.Integer()),
        sa.Column('ask_price_2', sa.Numeric(10, 4)),
        sa.Column('ask_volume_2', sa.Integer()),
        sa.Column('bid_price_3', sa.Numeric(10, 4)),
        sa.Column('bid_volume_3', sa.Integer()),
        sa.Column('ask_price_3', sa.Numeric(10, 4)),
        sa.Column('ask_volume_3', sa.Integer()),
        sa.Column('bid_price_4', sa.Numeric(10, 4)),
        sa.Column('bid_volume_4', sa.Integer()),
        sa.Column('ask_price_4', sa.Numeric(10, 4)),
        sa.Column('ask_volume_4', sa.Integer()),
        sa.Column('bid_price_5', sa.Numeric(10, 4)),
        sa.Column('bid_volume_5', sa.Integer()),
        sa.Column('ask_price_5', sa.Numeric(10, 4)),
        sa.Column('ask_volume_5', sa.Integer()),
        sa.Column('average_price', sa.Numeric(10, 4)),
        sa.Column('action_day', sa.String(length=10)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    op.create_index('ix_ctp_tick_data_instrument_time', 'ctp_tick_data', ['instrument_id', 'trading_day', 'update_time'])
    op.create_index('ix_ctp_tick_data_trading_day', 'ctp_tick_data', ['trading_day'])
    
    # Create system configuration table for CTP settings
    op.create_table('ctp_configurations',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('name', sa.String(length=100), nullable=False, unique=True),
        sa.Column('value', sa.Text()),
        sa.Column('description', sa.Text()),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    )
    
    # Insert default CTP configurations
    op.execute("""
        INSERT INTO ctp_configurations (name, value, description) VALUES
        ('ctp_trade_front_addr', 'tcp://***************:10130', 'CTP交易前置地址'),
        ('ctp_md_front_addr', 'tcp://***************:10131', 'CTP行情前置地址'),
        ('ctp_broker_id', '9999', 'CTP期货公司代码'),
        ('ctp_app_id', 'simnow_client_test', 'CTP应用标识'),
        ('ctp_auth_code', '0000000000000000', 'CTP授权编码'),
        ('ctp_user_product_info', 'QuantPlatform', 'CTP用户产品信息'),
        ('ctp_heartbeat_interval', '30', 'CTP心跳间隔(秒)'),
        ('ctp_max_order_ref', '1', 'CTP最大报单引用'),
        ('ctp_enable_tick_data', 'true', '是否启用Tick数据'),
        ('ctp_enable_order_flow', 'true', '是否启用委托流')
    """)
    
    print("✅ CTP integration tables created successfully")


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('ctp_configurations')
    op.drop_table('ctp_tick_data')
    op.drop_table('ctp_positions')
    op.drop_table('ctp_orders')
    op.drop_table('ctp_subscriptions')
    op.drop_table('ctp_accounts')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS ctpaccountstatus')
    op.execute('DROP TYPE IF EXISTS subscriptiontype')
    op.execute('DROP TYPE IF EXISTS ctpdirection')
    op.execute('DROP TYPE IF EXISTS ctpoffsetflag')
    op.execute('DROP TYPE IF EXISTS ctphedgeflag')
    op.execute('DROP TYPE IF EXISTS ctppricetype')
    op.execute('DROP TYPE IF EXISTS ctptimecondition')
    op.execute('DROP TYPE IF EXISTS ctpvolumecondition')
    op.execute('DROP TYPE IF EXISTS ctpcontingentcondition')
    op.execute('DROP TYPE IF EXISTS ctpforceclosereason')
    op.execute('DROP TYPE IF EXISTS ctppositiondate')
    op.execute('DROP TYPE IF EXISTS ctppositiondirection')
    
    print("✅ CTP integration tables dropped successfully")