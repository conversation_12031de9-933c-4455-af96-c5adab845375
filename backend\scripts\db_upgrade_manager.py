#!/usr/bin/env python3
"""
数据库升级管理工具

提供安全的数据库升级、回滚和验证功能
"""

import os
import sys
import asyncio
import argparse
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import logging

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from alembic import command
    from alembic.config import Config
    from sqlalchemy import create_engine, text
    from sqlalchemy.ext.asyncio import create_async_engine
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Please run: pip install alembic sqlalchemy")
    sys.exit(1)

class DatabaseUpgradeManager:
    """数据库升级管理器"""
    
    def __init__(self, config_path: str = "alembic.ini"):
        self.config_path = config_path
        self.alembic_cfg = Config(config_path)
        self.logger = self._setup_logger()
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger("db_upgrade")
        logger.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件日志
        fh = logging.FileHandler(f'logs/db_upgrade_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        
        # 控制台日志
        ch = logging.StreamHandler()
        ch.setFormatter(formatter)
        logger.addHandler(ch)
        
        return logger
    
    def create_backup(self) -> str:
        """创建数据库备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"db_backup_{timestamp}"
        backup_path = self.backup_dir / f"{backup_name}.sql"
        
        db_url = self.alembic_cfg.get_main_option("sqlalchemy.url")
        
        try:
            if "sqlite" in db_url:
                # SQLite 备份
                db_file = db_url.split("///")[1] if "///" in db_url else db_url.split("//")[1]
                shutil.copy2(db_file, self.backup_dir / f"{backup_name}.db")
                self.logger.info(f"SQLite backup created: {backup_name}.db")
                return str(self.backup_dir / f"{backup_name}.db")
            
            elif "postgresql" in db_url:
                # PostgreSQL 备份
                os.system(f"pg_dump {db_url} > {backup_path}")
                self.logger.info(f"PostgreSQL backup created: {backup_path}")
                return str(backup_path)
            
            else:
                self.logger.error(f"Unsupported database type: {db_url}")
                return ""
                
        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            return ""
    
    def validate_migration_safety(self) -> bool:
        """验证迁移安全性"""
        self.logger.info("Validating migration safety...")
        
        try:
            # 检查待执行的迁移
            from alembic.script import ScriptDirectory
            from alembic.runtime.migration import MigrationContext
            
            script = ScriptDirectory.from_config(self.alembic_cfg)
            
            # 连接数据库检查当前版本
            db_url = self.alembic_cfg.get_main_option("sqlalchemy.url")
            engine = create_engine(db_url)
            
            with engine.connect() as connection:
                context = MigrationContext.configure(connection)
                current_rev = context.get_current_revision()
                
                # 获取待执行的迁移
                revisions = list(script.walk_revisions(current_rev, "head"))
                
                if not revisions:
                    self.logger.info("No pending migrations")
                    return True
                
                self.logger.info(f"Found {len(revisions)} pending migrations")
                
                # 检查危险操作
                dangerous_ops = [
                    "drop_table", "drop_column", "alter_column"
                ]
                
                for rev in revisions:
                    if rev.down_revision is None:
                        continue
                        
                    # 读取迁移文件内容
                    revision_path = script.get_revision(rev.revision).path
                    with open(revision_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        for dangerous_op in dangerous_ops:
                            if dangerous_op in content:
                                self.logger.warning(f"Dangerous operation '{dangerous_op}' found in {rev.revision}")
                                
                return True
                
        except Exception as e:
            self.logger.error(f"Migration validation failed: {e}")
            return False
    
    def run_pre_migration_checks(self) -> bool:
        """运行迁移前检查"""
        self.logger.info("Running pre-migration checks...")
        
        checks = [
            self._check_disk_space,
            self._check_database_connectivity,
            self._check_backup_integrity,
            self._check_migration_dependencies
        ]
        
        for check in checks:
            if not check():
                return False
                
        self.logger.info("All pre-migration checks passed")
        return True
    
    def _check_disk_space(self) -> bool:
        """检查磁盘空间"""
        try:
            statvfs = os.statvfs('.')
            free_space = statvfs.f_frsize * statvfs.f_bavail
            required_space = 1024 * 1024 * 1024  # 1GB
            
            if free_space < required_space:
                self.logger.error(f"Insufficient disk space: {free_space / 1024**3:.2f}GB available, {required_space / 1024**3:.2f}GB required")
                return False
                
            return True
        except Exception as e:
            self.logger.error(f"Disk space check failed: {e}")
            return False
    
    def _check_database_connectivity(self) -> bool:
        """检查数据库连接"""
        try:
            db_url = self.alembic_cfg.get_main_option("sqlalchemy.url")
            engine = create_engine(db_url)
            
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                
            self.logger.info("Database connectivity check passed")
            return True
        except Exception as e:
            self.logger.error(f"Database connectivity check failed: {e}")
            return False
    
    def _check_backup_integrity(self) -> bool:
        """检查备份完整性"""
        # 简单的备份完整性检查
        backup_files = list(self.backup_dir.glob("*"))
        if not backup_files:
            self.logger.warning("No backup files found")
            
        return True
    
    def _check_migration_dependencies(self) -> bool:
        """检查迁移依赖"""
        try:
            # 检查必要的包是否安装
            import alembic
            import sqlalchemy
            self.logger.info("Migration dependencies check passed")
            return True
        except ImportError as e:
            self.logger.error(f"Missing migration dependencies: {e}")
            return False
    
    def perform_upgrade(self, target_revision: str = "head") -> bool:
        """执行数据库升级"""
        self.logger.info(f"Starting database upgrade to {target_revision}")
        
        try:
            # 创建备份
            backup_path = self.create_backup()
            if not backup_path:
                self.logger.error("Failed to create backup")
                return False
            
            # 运行迁移前检查
            if not self.run_pre_migration_checks():
                self.logger.error("Pre-migration checks failed")
                return False
            
            # 验证迁移安全性
            if not self.validate_migration_safety():
                self.logger.error("Migration safety validation failed")
                return False
            
            # 执行升级
            self.logger.info("Executing database upgrade...")
            command.upgrade(self.alembic_cfg, target_revision)
            
            # 验证升级结果
            if self.validate_upgrade():
                self.logger.info("Database upgrade completed successfully")
                return True
            else:
                self.logger.error("Upgrade validation failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Upgrade failed: {e}")
            return False
    
    def validate_upgrade(self) -> bool:
        """验证升级结果"""
        self.logger.info("Validating upgrade results...")
        
        try:
            db_url = self.alembic_cfg.get_main_option("sqlalchemy.url")
            engine = create_engine(db_url)
            
            # 检查表是否存在
            expected_tables = [
                'users', 'user_profiles', 'user_settings', 'roles', 'permissions',
                'accounts', 'orders', 'trades', 'positions', 
                'strategies', 'backtests', 'strategy_performance',
                'instruments', 'market_klines', 'market_ticks',
                'ctp_orders', 'ctp_trades', 'ctp_positions', 'ctp_accounts'
            ]
            
            with engine.connect() as conn:
                # 获取所有表名
                if "sqlite" in db_url:
                    result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                    existing_tables = [row[0] for row in result]
                elif "postgresql" in db_url:
                    result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname='public'"))
                    existing_tables = [row[0] for row in result]
                else:
                    self.logger.error("Unsupported database type for validation")
                    return False
                
                # 检查期望的表是否存在
                missing_tables = set(expected_tables) - set(existing_tables)
                if missing_tables:
                    self.logger.error(f"Missing tables after upgrade: {missing_tables}")
                    return False
                
                # 检查关键表是否有数据结构
                for table in ['users', 'accounts', 'orders']:
                    if table in existing_tables:
                        count_result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = count_result.scalar()
                        self.logger.info(f"Table {table}: {count} records")
            
            self.logger.info("Upgrade validation completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Upgrade validation failed: {e}")
            return False
    
    def rollback(self, target_revision: str) -> bool:
        """回滚到指定版本"""
        self.logger.info(f"Rolling back to revision {target_revision}")
        
        try:
            # 创建回滚前备份
            backup_path = self.create_backup()
            if backup_path:
                self.logger.info(f"Pre-rollback backup created: {backup_path}")
            
            # 执行回滚
            command.downgrade(self.alembic_cfg, target_revision)
            
            self.logger.info(f"Rollback to {target_revision} completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return False
    
    def get_migration_status(self) -> Dict:
        """获取迁移状态"""
        try:
            from alembic.script import ScriptDirectory
            from alembic.runtime.migration import MigrationContext
            
            script = ScriptDirectory.from_config(self.alembic_cfg)
            db_url = self.alembic_cfg.get_main_option("sqlalchemy.url")
            engine = create_engine(db_url)
            
            with engine.connect() as connection:
                context = MigrationContext.configure(connection)
                current_rev = context.get_current_revision()
                
                # 获取所有迁移
                revisions = list(script.walk_revisions("head", "base"))
                
                status = {
                    "current_revision": current_rev,
                    "head_revision": script.get_current_head(),
                    "total_migrations": len(revisions),
                    "pending_migrations": []
                }
                
                # 获取待执行的迁移
                if current_rev:
                    pending = list(script.walk_revisions(current_rev, "head"))
                    status["pending_migrations"] = [
                        {"revision": rev.revision, "description": rev.doc}
                        for rev in pending if rev.revision != current_rev
                    ]
                
                return status
                
        except Exception as e:
            self.logger.error(f"Failed to get migration status: {e}")
            return {}
    
    def generate_upgrade_report(self) -> str:
        """生成升级报告"""
        status = self.get_migration_status()
        
        report = f"""
数据库升级状态报告
==================

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

当前版本: {status.get('current_revision', 'Unknown')}
最新版本: {status.get('head_revision', 'Unknown')}
总迁移数: {status.get('total_migrations', 0)}
待执行迁移数: {len(status.get('pending_migrations', []))}

待执行迁移:
"""
        
        for migration in status.get('pending_migrations', []):
            report += f"  - {migration['revision']}: {migration['description']}\n"
        
        return report

def main():
    parser = argparse.ArgumentParser(description="数据库升级管理工具")
    parser.add_argument('action', choices=['upgrade', 'rollback', 'status', 'backup', 'validate', 'report'])
    parser.add_argument('--target', help='目标版本 (用于upgrade和rollback)')
    parser.add_argument('--config', default='alembic.ini', help='Alembic配置文件路径')
    
    args = parser.parse_args()
    
    manager = DatabaseUpgradeManager(args.config)
    
    if args.action == 'upgrade':
        target = args.target or 'head'
        success = manager.perform_upgrade(target)
        sys.exit(0 if success else 1)
        
    elif args.action == 'rollback':
        if not args.target:
            print("Error: --target is required for rollback")
            sys.exit(1)
        success = manager.rollback(args.target)
        sys.exit(0 if success else 1)
        
    elif args.action == 'status':
        status = manager.get_migration_status()
        print(json.dumps(status, indent=2, ensure_ascii=False))
        
    elif args.action == 'backup':
        backup_path = manager.create_backup()
        if backup_path:
            print(f"Backup created: {backup_path}")
        else:
            print("Backup failed")
            sys.exit(1)
            
    elif args.action == 'validate':
        success = manager.validate_upgrade()
        sys.exit(0 if success else 1)
        
    elif args.action == 'report':
        report = manager.generate_upgrade_report()
        print(report)

if __name__ == "__main__":
    main()