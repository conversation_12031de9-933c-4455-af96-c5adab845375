/**
 * 设备检测组合式函数
 * 检测设备类型、屏幕尺寸、方向等信息
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 屏幕方向
export type Orientation = 'portrait' | 'landscape'

// 操作系统类型
export type OSType = 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown'

// 浏览器类型
export type BrowserType = 'chrome' | 'firefox' | 'safari' | 'edge' | 'ie' | 'unknown'

// 设备信息接口
export interface DeviceInfo {
  type: DeviceType
  os: OSType
  browser: BrowserType
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isIOS: boolean
  isAndroid: boolean
  isTouchDevice: boolean
  pixelRatio: number
  screenWidth: number
  screenHeight: number
  viewportWidth: number
  viewportHeight: number
  orientation: Orientation
  userAgent: string
}

export function useDeviceDetection() {
  // 响应式数据
  const screenWidth = ref(0)
  const screenHeight = ref(0)
  const viewportWidth = ref(0)
  const viewportHeight = ref(0)
  const pixelRatio = ref(1)
  const orientation = ref<Orientation>('portrait')

  // 获取用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()

  // 检测操作系统
  const detectOS = (): OSType => {
    if (/iphone|ipad|ipod/.test(userAgent)) return 'ios'
    if (/android/.test(userAgent)) return 'android'
    if (/windows/.test(userAgent)) return 'windows'
    if (/macintosh|mac os x/.test(userAgent)) return 'macos'
    if (/linux/.test(userAgent)) return 'linux'
    return 'unknown'
  }

  // 检测浏览器
  const detectBrowser = (): BrowserType => {
    if (/chrome/.test(userAgent) && !/edge/.test(userAgent)) return 'chrome'
    if (/firefox/.test(userAgent)) return 'firefox'
    if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) return 'safari'
    if (/edge/.test(userAgent)) return 'edge'
    if (/msie|trident/.test(userAgent)) return 'ie'
    return 'unknown'
  }

  // 检测设备类型
  const detectDeviceType = (): DeviceType => {
    const width = Math.max(screenWidth.value, viewportWidth.value)
    
    if (width <= 768) return 'mobile'
    if (width <= 1024) return 'tablet'
    return 'desktop'
  }

  // 检测是否为触摸设备
  const detectTouchDevice = (): boolean => {
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 || 
           (navigator as any).msMaxTouchPoints > 0
  }

  // 更新屏幕信息
  const updateScreenInfo = () => {
    screenWidth.value = screen.width
    screenHeight.value = screen.height
    viewportWidth.value = window.innerWidth
    viewportHeight.value = window.innerHeight
    pixelRatio.value = window.devicePixelRatio || 1
    
    // 更新方向
    if (viewportWidth.value > viewportHeight.value) {
      orientation.value = 'landscape'
    } else {
      orientation.value = 'portrait'
    }
  }

  // 计算属性
  const os = computed(() => detectOS())
  const browser = computed(() => detectBrowser())
  const deviceType = computed(() => detectDeviceType())
  const isTouchDevice = computed(() => detectTouchDevice())

  const isMobile = computed(() => deviceType.value === 'mobile')
  const isTablet = computed(() => deviceType.value === 'tablet')
  const isDesktop = computed(() => deviceType.value === 'desktop')
  const isIOS = computed(() => os.value === 'ios')
  const isAndroid = computed(() => os.value === 'android')

  // 设备信息对象
  const deviceInfo = computed<DeviceInfo>(() => ({
    type: deviceType.value,
    os: os.value,
    browser: browser.value,
    isMobile: isMobile.value,
    isTablet: isTablet.value,
    isDesktop: isDesktop.value,
    isIOS: isIOS.value,
    isAndroid: isAndroid.value,
    isTouchDevice: isTouchDevice.value,
    pixelRatio: pixelRatio.value,
    screenWidth: screenWidth.value,
    screenHeight: screenHeight.value,
    viewportWidth: viewportWidth.value,
    viewportHeight: viewportHeight.value,
    orientation: orientation.value,
    userAgent: navigator.userAgent
  }))

  // 检测特定功能支持
  const supportsWebGL = computed(() => {
    try {
      const canvas = document.createElement('canvas')
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    } catch {
      return false
    }
  })

  const supportsWebRTC = computed(() => {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
  })

  const supportsServiceWorker = computed(() => {
    return 'serviceWorker' in navigator
  })

  const supportsWebAssembly = computed(() => {
    return 'WebAssembly' in window
  })

  const supportsWebGL2 = computed(() => {
    try {
      const canvas = document.createElement('canvas')
      return !!canvas.getContext('webgl2')
    } catch {
      return false
    }
  })

  const supportsOffscreenCanvas = computed(() => {
    return 'OffscreenCanvas' in window
  })

  // 网络信息
  const networkInfo = computed(() => {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection

    if (!connection) {
      return {
        effectiveType: 'unknown',
        downlink: 0,
        rtt: 0,
        saveData: false
      }
    }

    return {
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false
    }
  })

  // 电池信息
  const batteryInfo = ref({
    charging: true,
    level: 1,
    chargingTime: 0,
    dischargingTime: Infinity
  })

  const updateBatteryInfo = async () => {
    try {
      const battery = await (navigator as any).getBattery?.()
      if (battery) {
        batteryInfo.value = {
          charging: battery.charging,
          level: battery.level,
          chargingTime: battery.chargingTime,
          dischargingTime: battery.dischargingTime
        }

        // 监听电池状态变化
        battery.addEventListener('chargingchange', () => {
          batteryInfo.value.charging = battery.charging
        })
        battery.addEventListener('levelchange', () => {
          batteryInfo.value.level = battery.level
        })
      }
    } catch {
      // 不支持电池API
    }
  }

  // 内存信息
  const memoryInfo = computed(() => {
    const memory = (performance as any).memory
    if (!memory) {
      return {
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0
      }
    }

    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    }
  })

  // 获取设备性能等级
  const getPerformanceLevel = (): 'low' | 'medium' | 'high' => {
    const cores = navigator.hardwareConcurrency || 1
    const memory = memoryInfo.value.jsHeapSizeLimit / 1024 / 1024 / 1024 // GB
    
    if (cores >= 8 && memory >= 4) return 'high'
    if (cores >= 4 && memory >= 2) return 'medium'
    return 'low'
  }

  const performanceLevel = computed(() => getPerformanceLevel())

  // 事件监听器
  const handleResize = () => {
    updateScreenInfo()
  }

  const handleOrientationChange = () => {
    // 延迟更新，等待浏览器完成方向切换
    setTimeout(updateScreenInfo, 100)
  }

  // 生命周期
  onMounted(() => {
    updateScreenInfo()
    updateBatteryInfo()

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleOrientationChange)
  })

  // 工具方法
  const isLowEndDevice = computed(() => {
    return performanceLevel.value === 'low' || 
           (isMobile.value && pixelRatio.value < 2)
  })

  const shouldReduceAnimations = computed(() => {
    return isLowEndDevice.value || 
           networkInfo.value.saveData ||
           batteryInfo.value.level < 0.2
  })

  const getOptimalImageFormat = (): string => {
    if (supportsWebGL.value) return 'webp'
    if (browser.value === 'safari') return 'jpeg'
    return 'png'
  }

  const getRecommendedQuality = (): 'low' | 'medium' | 'high' => {
    if (isLowEndDevice.value) return 'low'
    if (networkInfo.value.effectiveType === '4g') return 'high'
    if (networkInfo.value.effectiveType === '3g') return 'medium'
    return 'low'
  }

  return {
    // 基本信息
    deviceInfo,
    deviceType,
    os,
    browser,
    orientation,
    
    // 设备类型判断
    isMobile,
    isTablet,
    isDesktop,
    isIOS,
    isAndroid,
    isTouchDevice,
    
    // 屏幕信息
    screenWidth,
    screenHeight,
    viewportWidth,
    viewportHeight,
    pixelRatio,
    
    // 功能支持
    supportsWebGL,
    supportsWebGL2,
    supportsWebRTC,
    supportsServiceWorker,
    supportsWebAssembly,
    supportsOffscreenCanvas,
    
    // 性能和网络
    performanceLevel,
    networkInfo,
    batteryInfo,
    memoryInfo,
    
    // 优化建议
    isLowEndDevice,
    shouldReduceAnimations,
    getOptimalImageFormat,
    getRecommendedQuality,
    
    // 方法
    updateScreenInfo
  }
}
