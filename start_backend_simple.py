#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单启动后端服务
"""

import os
import sys
import subprocess
import time

def main():
    print("🚀 启动量化投资平台后端服务...")
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
        import pydantic
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("📦 正在安装依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn", "pydantic"])
    
    # 启动服务
    try:
        print("🌐 启动服务...")
        os.chdir("backend")
        subprocess.run([sys.executable, "simple_backend.py"])
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        
        # 尝试直接运行
        print("🔄 尝试直接运行...")
        try:
            import uvicorn
            from fastapi import FastAPI
            from fastapi.middleware.cors import CORSMiddleware
            from fastapi.responses import JSONResponse
            
            app = FastAPI(title="量化投资平台 API")
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            @app.get("/")
            async def root():
                return {"message": "量化投资平台后端服务运行中", "status": "ok"}
            
            @app.get("/health")
            async def health():
                return {"status": "healthy", "timestamp": time.time()}
            
            @app.get("/api/v1/market/stocks")
            async def get_stocks():
                return {
                    "success": True,
                    "data": [
                        {"symbol": "000001", "name": "平安银行", "price": 12.50},
                        {"symbol": "000002", "name": "万科A", "price": 18.30},
                        {"symbol": "600000", "name": "浦发银行", "price": 8.90},
                    ]
                }
            
            print("🌐 服务启动在 http://localhost:8000")
            uvicorn.run(app, host="0.0.0.0", port=8000)
            
        except Exception as e2:
            print(f"❌ 直接运行也失败: {e2}")

if __name__ == "__main__":
    main()
