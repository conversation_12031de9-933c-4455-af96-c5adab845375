<template>
  <div class="mobile-notifications">
    <div v-if="notifications.length === 0" class="empty-state">
      <el-icon size="48" color="#909399">
        <Bell />
      </el-icon>
      <p>暂无通知</p>
    </div>
    
    <div v-else class="notification-list">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification-item"
        :class="{ unread: !notification.read }"
        @click="handleNotificationClick(notification)"
      >
        <div class="notification-icon">
          <el-icon :color="getNotificationColor(notification.type)">
            <component :is="getNotificationIcon(notification.type)" />
          </el-icon>
        </div>
        
        <div class="notification-content">
          <h4 class="notification-title">{{ notification.title }}</h4>
          <p class="notification-message">{{ notification.message }}</p>
          <span class="notification-time">{{ formatTime(notification.time) }}</span>
        </div>
        
        <div class="notification-actions">
          <el-button
            size="small"
            text
            @click.stop="markAsRead(notification)"
            v-if="!notification.read"
          >
            标记已读
          </el-button>
          <el-button
            size="small"
            text
            type="danger"
            @click.stop="deleteNotification(notification)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Bell, Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'

interface Notification {
  id: string
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  message: string
  time: Date
  read: boolean
}

const emit = defineEmits<{
  notificationAction: [action: string, notification: Notification]
}>()

const notifications = ref<Notification[]>([
  {
    id: '1',
    type: 'info',
    title: '系统通知',
    message: '欢迎使用量化投资平台',
    time: new Date(),
    read: false
  }
])

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'warning':
      return Warning
    case 'success':
      return SuccessFilled
    case 'error':
      return Warning
    default:
      return InfoFilled
  }
}

const getNotificationColor = (type: string) => {
  switch (type) {
    case 'warning':
      return '#E6A23C'
    case 'success':
      return '#67C23A'
    case 'error':
      return '#F56C6C'
    default:
      return '#409EFF'
  }
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return `${Math.floor(minutes / 1440)}天前`
}

const handleNotificationClick = (notification: Notification) => {
  emit('notificationAction', 'click', notification)
}

const markAsRead = (notification: Notification) => {
  notification.read = true
  emit('notificationAction', 'read', notification)
}

const deleteNotification = (notification: Notification) => {
  const index = notifications.value.findIndex(n => n.id === notification.id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
  emit('notificationAction', 'delete', notification)
}
</script>

<style scoped>
.mobile-notifications {
  padding: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.notification-list {
  space-y: 12px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.notification-item.unread {
  border-left: 4px solid #409eff;
  background: #f0f9ff;
}

.notification-item:active {
  background: #f5f7fa;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #303133;
}

.notification-message {
  font-size: 13px;
  color: #606266;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-actions {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
</style>
