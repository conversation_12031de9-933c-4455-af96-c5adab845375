@echo off
REM 量化投资平台 - 仓库清理脚本 (Windows批处理版本)
REM 用于快速清理常见的不必要文件和目录

echo ========================================
echo 量化投资平台 - 仓库清理工具
echo ========================================
echo.

REM 检查是否在Git仓库中
if not exist ".git" (
    echo 错误: 当前目录不是Git仓库
    pause
    exit /b 1
)

echo 正在扫描需要清理的文件...
echo.

REM 设置计数器
set /a DIR_COUNT=0
set /a FILE_COUNT=0

REM 清理目录函数
call :CleanDirectory "backend\venv" "Python虚拟环境"
call :CleanDirectory "frontend\node_modules" "Node.js依赖包"
call :CleanDirectory "frontend\dist" "前端构建输出"
call :CleanDirectory "data" "数据目录"
call :CleanDirectory "logs" "日志目录"
call :CleanDirectory "cache" "缓存目录"
call :CleanDirectory "screenshots" "截图目录"
call :CleanDirectory "test_screenshots" "测试截图"
call :CleanDirectory "final_test_screenshots" "最终测试截图"
call :CleanDirectory "smart_test_screenshots" "智能测试截图"
call :CleanDirectory "archive" "归档目录"
call :CleanDirectory "mcp\browser-tools-mcp" "MCP浏览器工具"
call :CleanDirectory "mcp\mcp-use" "MCP使用工具"
call :CleanDirectory "mcp\puppeteer" "MCP Puppeteer"
call :CleanDirectory "__pycache__" "Python缓存"
call :CleanDirectory ".pytest_cache" "Pytest缓存"
call :CleanDirectory ".mypy_cache" "MyPy缓存"
call :CleanDirectory "htmlcov" "测试覆盖率报告"
call :CleanDirectory "test-results" "测试结果"
call :CleanDirectory "playwright-report" "Playwright报告"

REM 清理特定文件
echo 正在清理特定文件...

call :CleanFile "backend_api_test.js"
call :CleanFile "backend_api_test_report.json"
call :CleanFile "comprehensive_*.js"
call :CleanFile "comprehensive_*.py"
call :CleanFile "comprehensive_*.md"
call :CleanFile "final_*.js"
call :CleanFile "final_*.py"
call :CleanFile "final_*.json"
call :CleanFile "minimal_backend.py"
call :CleanFile "package-lock.json"
call :CleanFile "package.json"
call :CleanFile "puppeteer_test.*"
call :CleanFile "real_user_test.*"
call :CleanFile "server_diagnostic*.*"
call :CleanFile "simple_*.py"
call :CleanFile "smart_*.js"
call :CleanFile "test_*.py"
call :CleanFile "user_experience_*.*"
call :CleanFile "verify_*.py"
call :CleanFile "*.tmp"
call :CleanFile "*.log"
call :CleanFile "*.db"
call :CleanFile "*.sqlite*"
call :CleanFile "nul"

REM 清理测试和临时启动脚本
call :CleanFile "start_backend_simple.py"
call :CleanFile "start_fixed_platform.bat"
call :CleanFile "start_frontend.py"
call :CleanFile "start_frontend_simple.bat"
call :CleanFile "start_platform_*.bat"
call :CleanFile "start_simple_frontend.py"
call :CleanFile "start_trading_platform.bat"
call :CleanFile "stop_platform.bat"

REM 清理大量测试报告文档
call :CleanFile "AUTHORITATIVE_DOCS_INDEX.md"
call :CleanFile "COMPREHENSIVE_*.md"
call :CleanFile "DEPLOYMENT_CONSOLIDATION_SUMMARY.md"
call :CleanFile "DOCUMENTATION_CLEANUP_*.md"
call :CleanFile "FINAL_*.md"
call :CleanFile "PRODUCTION_READY_SUMMARY.md"
call :CleanFile "PROJECT_COMPLETION_REPORT.md"
call :CleanFile "PUPPETEER_MCP_*.md"
call :CleanFile "REDUNDANT_FILES_TO_CLEAN.md"
call :CleanFile "SCRIPT_CONSOLIDATION_SUMMARY.md"
call :CleanFile "SECURITY_FIXES_GUIDE.md"
call :CleanFile "TESTING_CONFIG_CONSOLIDATION.md"

REM 清理中文文件名
call :CleanFile "完整深度测试总结报告.md"
call :CleanFile "实盘交易系统升级说明.md"

echo.
echo ========================================
echo 清理完成统计:
echo 清理的目录数量: %DIR_COUNT%
echo 清理的文件数量: %FILE_COUNT%
echo ========================================
echo.

echo 建议的后续操作:
echo 1. 检查 .gitignore 文件
echo 2. 运行: git add .gitignore
echo 3. 运行: git commit -m "feat: 清理仓库并完善.gitignore配置"
echo 4. 运行: git push
echo.

echo 清理完成! 请检查仓库状态。
pause
goto :eof

REM 清理目录函数
:CleanDirectory
set "dir_path=%~1"
set "description=%~2"

if exist "%dir_path%" (
    echo 正在删除目录: %dir_path% (%description%)
    
    REM 从Git中移除
    git rm -r --cached "%dir_path%" >nul 2>&1
    
    REM 删除目录
    rmdir /s /q "%dir_path%"
    
    if not exist "%dir_path%" (
        echo   ✓ 已删除: %dir_path%
        set /a DIR_COUNT+=1
    ) else (
        echo   ✗ 删除失败: %dir_path%
    )
)
goto :eof

REM 清理文件函数
:CleanFile
set "file_pattern=%~1"

REM 检查文件是否存在（通配符支持有限）
for %%f in ("%file_pattern%") do (
    if exist "%%f" (
        echo 正在删除文件: %%f
        
        REM 从Git中移除
        git rm --cached "%%f" >nul 2>&1
        
        REM 删除文件
        del /q "%%f"
        
        if not exist "%%f" (
            echo   ✓ 已删除: %%f
            set /a FILE_COUNT+=1
        ) else (
            echo   ✗ 删除失败: %%f
        )
    )
)
goto :eof