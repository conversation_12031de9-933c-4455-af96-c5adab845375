# Development Environment Configuration
# Extends base configuration with development-specific services

version: '3.8'

services:
  # Backend API (Development)
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
      args:
        BUILD_ENV: development
        PYTHON_VERSION: "3.11"
    container_name: quant_backend_dev
    restart: unless-stopped
    environment:
      - API_PORT=${API_PORT:-8000}
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD:-quant_password}@postgres:${DB_PORT:-5432}/quant_db
      - REDIS_URL=redis://redis:${REDIS_PORT:-6379}/0
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-secret-key-change-in-production}
      - CORS_ORIGINS=http://localhost:${FRONTEND_PORT:-5173},http://localhost:3000
      - LOG_LEVEL=DEBUG
    ports:
      - "${API_PORT:-8000}:${API_PORT:-8000}"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quant_network
    volumes:
      - ../backend:/app
      - /app/__pycache__
      - backend_logs:/app/logs
      - backend_data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${API_PORT:-8000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend (Development)
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
      args:
        BUILD_ENV: development
        NODE_VERSION: "18"
    container_name: quant_frontend_dev
    restart: unless-stopped
    environment:
      - FRONTEND_PORT=${FRONTEND_PORT:-5173}
      - API_PORT=${API_PORT:-8000}
      - VITE_API_BASE_URL=http://localhost:${API_PORT:-8000}/api/v1
      - VITE_WS_URL=ws://localhost:${API_PORT:-8000}/ws
      - NODE_ENV=development
    ports:
      - "${FRONTEND_PORT:-5173}:${FRONTEND_PORT:-5173}"
    depends_on:
      - backend
    networks:
      - quant_network
    volumes:
      - ../frontend:/app
      - /app/node_modules
      - /app/dist
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:${FRONTEND_PORT:-5173}/"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3

volumes:
  backend_logs:
    name: quant_backend_logs_dev
    driver: local
  backend_data:
    name: quant_backend_data_dev
    driver: local

networks:
  quant_network:
    external: true