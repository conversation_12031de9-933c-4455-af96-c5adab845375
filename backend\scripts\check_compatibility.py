#!/usr/bin/env python3
"""
数据库模型兼容性检查工具

检查新旧ORM模型之间的兼容性，确保平滑升级
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple
import importlib.util
import inspect
from dataclasses import dataclass

# Add the backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from sqlalchemy import Column, Table
    from sqlalchemy.ext.declarative import DeclarativeMeta
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Please run: pip install sqlalchemy")
    sys.exit(1)

@dataclass
class ModelInfo:
    """模型信息"""
    name: str
    table_name: str
    columns: Dict[str, str]
    relationships: List[str]
    indexes: List[str]

class CompatibilityChecker:
    """兼容性检查器"""
    
    def __init__(self):
        self.issues: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def check_model_compatibility(self) -> bool:
        """检查模型兼容性"""
        print("检查数据库模型兼容性...")
        
        # 获取新的统一模型
        new_models = self._get_unified_models()
        
        # 获取旧的分散模型
        old_models = self._get_legacy_models()
        
        # 比较模型
        success = self._compare_models(old_models, new_models)
        
        # 输出结果
        self._print_results()
        
        return success and len(self.issues) == 0
    
    def _get_unified_models(self) -> Dict[str, ModelInfo]:
        """获取统一ORM模型"""
        models = {}
        
        try:
            # 导入新的统一模型
            from app.db.models.user import User, Role, Permission
            from app.db.models.trading import Order, Trade, Position, Account
            from app.db.models.strategy import Strategy, Backtest
            from app.db.models.market import Instrument, MarketKline, MarketTick
            from app.db.models.ctp_models import CTPOrder, CTPTrade, CTPPosition, CTPAccount
            
            model_classes = [
                User, UserProfile, UserSettings, Role, Permission,
                Order, Trade, Position, Account,
                Strategy, Backtest,
                Instrument, MarketKline, MarketTick,
                CTPOrder, CTPTrade, CTPPosition, CTPAccount
            ]
            
            for model_class in model_classes:
                model_info = self._extract_model_info(model_class)
                models[model_info.name] = model_info
                
            self.info.append(f"SUCCESS: 成功加载 {len(models)} 个统一模型")
            
        except Exception as e:
            self.issues.append(f"ERROR: 无法加载统一模型: {e}")
            
        return models
    
    def _get_legacy_models(self) -> Dict[str, ModelInfo]:
        """获取旧的分散模型"""
        models = {}
        
        try:
            # 尝试导入旧模型
            legacy_paths = [
                'app.db.models.user',
                'app.db.models.trading', 
                'app.db.models.ctp_models',
                'generated_fixes.database_models'
            ]
            
            for path in legacy_paths:
                try:
                    module = importlib.import_module(path)
                    
                    # 查找模型类
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and 
                            hasattr(obj, '__tablename__') and 
                            isinstance(obj, DeclarativeMeta)):
                            
                            model_info = self._extract_model_info(obj)
                            models[model_info.name] = model_info
                            
                except ImportError:
                    self.warnings.append(f"⚠️ 无法导入旧模块: {path}")
                    continue
            
            self.info.append(f"✅ 成功加载 {len(models)} 个旧模型")
            
        except Exception as e:
            self.warnings.append(f"⚠️ 加载旧模型时出现问题: {e}")
            
        return models
    
    def _extract_model_info(self, model_class) -> ModelInfo:
        """提取模型信息"""
        columns = {}
        relationships = []
        indexes = []
        
        # 提取列信息
        if hasattr(model_class, '__table__'):
            table = model_class.__table__
            for column in table.columns:
                columns[column.name] = str(column.type)
            
            # 提取索引信息
            for index in table.indexes:
                indexes.append(index.name or f"idx_{table.name}_{','.join([c.name for c in index.columns])}")
        
        # 提取关系信息
        if hasattr(model_class, '__mapper__'):
            mapper = model_class.__mapper__
            for relationship in mapper.relationships:
                relationships.append(relationship.key)
        
        return ModelInfo(
            name=model_class.__name__,
            table_name=getattr(model_class, '__tablename__', ''),
            columns=columns,
            relationships=relationships,
            indexes=indexes
        )
    
    def _compare_models(self, old_models: Dict[str, ModelInfo], new_models: Dict[str, ModelInfo]) -> bool:
        """比较新旧模型"""
        success = True
        
        # 检查缺失的模型
        old_model_names = set(old_models.keys())
        new_model_names = set(new_models.keys())
        
        missing_models = old_model_names - new_model_names
        if missing_models:
            self.issues.extend([f"❌ 缺失模型: {name}" for name in missing_models])
            success = False
        
        new_models_added = new_model_names - old_model_names
        if new_models_added:
            self.info.extend([f"ℹ️ 新增模型: {name}" for name in new_models_added])
        
        # 比较共同模型
        common_models = old_model_names & new_model_names
        for model_name in common_models:
            if not self._compare_single_model(old_models[model_name], new_models[model_name]):
                success = False
        
        return success
    
    def _compare_single_model(self, old_model: ModelInfo, new_model: ModelInfo) -> bool:
        """比较单个模型"""
        success = True
        
        # 检查表名
        if old_model.table_name != new_model.table_name:
            self.warnings.append(
                f"⚠️ 模型 {old_model.name} 表名变更: {old_model.table_name} -> {new_model.table_name}"
            )
        
        # 检查列变更
        old_columns = set(old_model.columns.keys())
        new_columns = set(new_model.columns.keys())
        
        # 检查删除的列
        removed_columns = old_columns - new_columns
        if removed_columns:
            self.issues.extend([
                f"❌ 模型 {old_model.name} 删除了列: {col}" for col in removed_columns
            ])
            success = False
        
        # 检查新增的列
        added_columns = new_columns - old_columns
        if added_columns:
            self.info.extend([
                f"ℹ️ 模型 {old_model.name} 新增了列: {col}" for col in added_columns
            ])
        
        # 检查列类型变更
        common_columns = old_columns & new_columns
        for col_name in common_columns:
            old_type = old_model.columns[col_name]
            new_type = new_model.columns[col_name]
            if old_type != new_type:
                self.warnings.append(
                    f"⚠️ 模型 {old_model.name} 列 {col_name} 类型变更: {old_type} -> {new_type}"
                )
        
        return success
    
    def check_database_constraints(self) -> bool:
        """检查数据库约束"""
        print("检查数据库约束...")
        
        try:
            # 导入数据库配置
            from app.core.database import Base
            from sqlalchemy import create_engine
            
            # 检查是否有循环引用
            success = self._check_circular_references()
            
            if success:
                self.info.append("✅ 数据库约束检查通过")
            
            return success
            
        except Exception as e:
            self.issues.append(f"❌ 数据库约束检查失败: {e}")
            return False
    
    def _check_circular_references(self) -> bool:
        """检查循环引用"""
        # 简化的循环引用检查
        # 在实际生产环境中应该更加详细
        
        try:
            import app.models
            self.info.append("✅ 模型导入成功，无明显循环引用")
            return True
        except Exception as e:
            if "circular import" in str(e).lower():
                self.issues.append(f"❌ 检测到循环引用: {e}")
                return False
            else:
                self.warnings.append(f"⚠️ 模型导入警告: {e}")
                return True
    
    def check_migration_safety(self) -> bool:
        """检查迁移安全性"""
        print("检查迁移安全性...")
        
        try:
            # 检查Alembic配置
            alembic_ini = Path("alembic.ini")
            if not alembic_ini.exists():
                self.issues.append("❌ 缺少 alembic.ini 配置文件")
                return False
            
            # 检查迁移目录
            migrations_dir = Path("migrations")
            if not migrations_dir.exists():
                self.issues.append("❌ 缺少 migrations 目录")
                return False
            
            versions_dir = migrations_dir / "versions"
            if not versions_dir.exists():
                self.issues.append("❌ 缺少 migrations/versions 目录")
                return False
            
            # 统计迁移文件
            migration_files = list(versions_dir.glob("*.py"))
            self.info.append(f"ℹ️ 找到 {len(migration_files)} 个迁移文件")
            
            # 检查最新迁移文件
            if migration_files:
                latest_migration = max(migration_files, key=lambda p: p.name)
                self.info.append(f"ℹ️ 最新迁移文件: {latest_migration.name}")
            
            self.info.append("✅ 迁移安全性检查通过")
            return True
            
        except Exception as e:
            self.issues.append(f"❌ 迁移安全性检查失败: {e}")
            return False
    
    def check_dependencies(self) -> bool:
        """检查依赖项"""
        print("检查依赖项...")
        
        required_packages = [
            'sqlalchemy',
            'alembic',
            'pydantic',
            'fastapi'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                importlib.import_module(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.issues.extend([f"❌ 缺少依赖包: {pkg}" for pkg in missing_packages])
            return False
        else:
            self.info.append("✅ 所有必需依赖项已安装")
            return True
    
    def _print_results(self):
        """打印检查结果"""
        print("\n" + "="*60)
        print("兼容性检查结果")
        print("="*60)
        
        if self.info:
            print("\n信息:")
            for info in self.info:
                print(f"  {info}")
        
        if self.warnings:
            print("\n警告:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.issues:
            print("\n问题:")
            for issue in self.issues:
                print(f"  {issue}")
        
        print(f"\n统计:")
        print(f"  信息: {len(self.info)}")
        print(f"  警告: {len(self.warnings)}")
        print(f"  问题: {len(self.issues)}")
        
        if len(self.issues) == 0:
            print(f"\n兼容性检查通过!")
        else:
            print(f"\n发现 {len(self.issues)} 个问题需要解决")

def main():
    """主函数"""
    print("数据库模型兼容性检查器")
    print("="*60)
    
    checker = CompatibilityChecker()
    
    # 执行所有检查
    checks = [
        checker.check_dependencies,
        checker.check_model_compatibility,
        checker.check_database_constraints,
        checker.check_migration_safety
    ]
    
    all_passed = True
    for check in checks:
        try:
            result = check()
            all_passed = all_passed and result
        except Exception as e:
            print(f"❌ 检查过程中出现错误: {e}")
            all_passed = False
    
    # 返回结果
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    main()