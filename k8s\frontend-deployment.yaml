# Consolidated Frontend Deployment
# Production-ready Nginx serving with autoscaling

apiVersion: apps/v1
kind: Deployment
metadata:
  name: quant-platform-frontend
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-frontend
    app.kubernetes.io/instance: quant-platform
    app.kubernetes.io/version: "v1"
    app.kubernetes.io/component: frontend
    app.kubernetes.io/part-of: quant-platform
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: quant-platform-frontend
      app.kubernetes.io/instance: quant-platform
  template:
    metadata:
      labels:
        app.kubernetes.io/name: quant-platform-frontend
        app.kubernetes.io/instance: quant-platform
        app.kubernetes.io/version: "v1"
        app.kubernetes.io/component: frontend
      annotations:
        config.kubernetes.io/depends-on: "quant-platform-backend"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 101  # nginx user
        runAsGroup: 101
        fsGroup: 101
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: frontend
        image: quant-platform/frontend:latest
        imagePullPolicy: Always
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE  # Allow binding to port 80
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        env:
        - name: FRONTEND_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: FRONTEND_PORT
        - name: API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: API_BASE_URL
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: quant-platform-config
              key: ENVIRONMENT
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
            ephemeral-storage: "100Mi"
          limits:
            memory: "512Mi"
            cpu: "500m"
            ephemeral-storage: "500Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 12
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-pid
          mountPath: /var/run
        - name: nginx-conf
          mountPath: /etc/nginx/conf.d
          readOnly: true
      volumes:
      - name: tmp
        emptyDir: {}
      - name: nginx-cache
        emptyDir: {}
      - name: nginx-pid
        emptyDir: {}
      - name: nginx-conf
        configMap:
          name: quant-platform-nginx-config
      nodeSelector:
        kubernetes.io/arch: amd64
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - quant-platform-frontend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: quant-platform-frontend
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-frontend
    app.kubernetes.io/instance: quant-platform
    app.kubernetes.io/component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: quant-platform-frontend
    app.kubernetes.io/instance: quant-platform
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: quant-platform-frontend-hpa
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-frontend
    app.kubernetes.io/component: autoscaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: quant-platform-frontend
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 30
      policies:
      - type: Percent
        value: 50
        periodSeconds: 15
---
# Nginx Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: quant-platform-nginx-config
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform-frontend
    app.kubernetes.io/component: nginx-config
data:
  default.conf: |
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html index.htm;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' ws: wss:;" always;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
        }
        
        # API proxy
        location /api/ {
            proxy_pass http://quant-platform-backend:8000/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # WebSocket proxy
        location /ws/ {
            proxy_pass http://quant-platform-backend:8000/ws/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
        }
        
        # SPA routing
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }