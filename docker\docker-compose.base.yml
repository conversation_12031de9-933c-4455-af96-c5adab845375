# Base Docker Compose Configuration
# Common services and network definition

version: '3.8'

# Shared environment variables
x-common-variables: &common-variables
  API_PORT: 8000
  FRONTEND_PORT: 5173
  DB_PORT: 5432
  REDIS_PORT: 6379
  PROMETHEUS_PORT: 9090
  GRAFANA_PORT: 3001

# Common service configurations
x-common-restart: &common-restart
  restart: unless-stopped

x-common-network: &common-network
  networks:
    - quant_network

x-common-healthcheck: &api-healthcheck
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:${API_PORT:-8000}/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

services:
  # PostgreSQL Database
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: quant_postgres
    <<: [*common-restart, *common-network]
    environment:
      <<: *common-variables
      POSTGRES_DB: quant_db
      POSTGRES_USER: quant_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-quant_password}
      PGPORT: ${DB_PORT}
    ports:
      - "${DB_PORT}:${DB_PORT}"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quant_user -d quant_db -p ${DB_PORT}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: quant_redis
    <<: [*common-restart, *common-network]
    environment:
      <<: *common-variables
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --port ${REDIS_PORT:-6379}
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "${REDIS_PORT:-6379}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  quant_network:
    name: quant_network
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    name: quant_postgres_data
    driver: local
  redis_data:
    name: quant_redis_data
    driver: local
  prometheus_data:
    name: quant_prometheus_data
    driver: local
  grafana_data:
    name: quant_grafana_data
    driver: local