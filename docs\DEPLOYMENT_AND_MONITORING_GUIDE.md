# 量化投资平台 - 部署与监控指南

## 概述

本指南提供了量化投资平台在不同环境下的部署和监控解决方案，包括集中化的CI/CD pipeline、跨平台启动脚本和全面的监控体系。

## 🚀 快速开始

### Windows 环境
```bash
# 使用统一启动脚本
python scripts/platform-start.py --env development

# 或使用传统批处理脚本
start_trading_platform.bat
```

### Linux/macOS 环境
```bash
# 使用统一启动脚本
python3 scripts/platform-start.py --env development

# 或使用 Shell 脚本
./start.sh --env development
```

### Docker 环境
```bash
# 开发环境
docker-compose up -d

# Linux 生产环境（优化版）
docker-compose -f docker/docker-compose.linux.yml up -d
```

## 📋 部署方案

### 1. 本地开发部署

#### Python 统一启动脚本
- **文件**: `scripts/platform-start.py`
- **配置**: `scripts/platform-config.json`
- **特点**: 
  - 跨平台支持（Windows/Linux/macOS）
  - 自动依赖检查和安装
  - 健康检查和服务监控
  - 环境变量自动配置

```bash
# 基本用法
python scripts/platform-start.py

# 指定环境和服务
python scripts/platform-start.py --env production --services backend

# 使用 Docker
python scripts/platform-start.py --docker --env production

# 仅检查依赖
python scripts/platform-start.py --check
```

#### Shell 脚本（Linux/macOS）
- **文件**: `start.sh`
- **特点**:
  - 完整的命令行参数支持
  - 彩色日志输出
  - 自动服务健康检查
  - 信号处理和优雅关闭

```bash
# 基本用法
./start.sh

# 高级选项
./start.sh -e production -s backend -d -v
```

### 2. 容器化部署

#### 标准 Docker Compose
- **文件**: `docker-compose.yml`
- **用途**: 开发和测试环境
- **服务**: backend, frontend, redis, postgres, nginx

#### Linux 优化版本
- **文件**: `docker/docker-compose.linux.yml`
- **特点**:
  - 生产级性能优化
  - 安全加固配置
  - 资源限制和监控
  - 多网络隔离
  - 自动备份支持

```bash
# 最小化部署
docker-compose -f docker/docker-compose.linux.yml --profile minimal up -d

# 完整部署（含监控）
docker-compose -f docker/docker-compose.linux.yml --profile monitoring up -d
```

#### 部署脚本
- **文件**: `scripts/deploy-linux.sh`
- **功能**:
  - 自动化生产部署
  - 系统环境检查
  - 数据库迁移
  - 服务健康检查
  - 自动回滚机制

```bash
# 生产环境部署
./scripts/deploy-linux.sh -e production

# 预演模式
./scripts/deploy-linux.sh -e production -d

# 跳过备份和迁移
./scripts/deploy-linux.sh -b -m
```

### 3. Kubernetes 部署

#### K8s 配置文件
```
k8s/
├── namespace.yaml          # 命名空间
├── configmap.yaml         # 配置映射
├── secrets.yaml           # 密钥配置
├── backend-deployment.yaml # 后端部署
├── frontend-deployment.yaml # 前端部署
└── ingress.yaml           # 入口配置
```

#### 部署命令
```bash
# 创建命名空间和配置
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# 部署应用
kubectl apply -f k8s/backend-deployment.yaml
kubectl apply -f k8s/frontend-deployment.yaml
kubectl apply -f k8s/ingress.yaml
```

## 🔄 CI/CD Pipeline

### 主要 Pipeline
- **文件**: `.github/workflows/main.yml`
- **触发条件**: 
  - Push to main/develop 分支
  - Pull Request to main 分支
  - 手动触发（workflow_dispatch）

### Pipeline 阶段

#### 1. 变更检测
- 智能检测代码变更
- 避免不必要的构建
- 支持强制部署

#### 2. 代码质量检查
- SonarCloud 代码扫描
- 安全扫描（TruffleHog）
- 格式检查和类型检查

#### 3. 构建和测试
- **后端**: Python 单元测试、覆盖率报告
- **前端**: Node.js 单元测试、构建验证
- **E2E**: Playwright 端到端测试

#### 4. 镜像构建
- Docker 镜像构建和缓存
- 多架构支持
- 安全扫描

#### 5. 自动部署
- **开发环境**: develop 分支自动部署
- **生产环境**: main 分支部署（需审批）
- 健康检查和回滚机制

### 环境管理
```
environments:
├── development    # 开发环境
├── staging       # 预生产环境
└── production    # 生产环境
```

### 部署策略
- **滚动更新**: 零停机部署
- **蓝绿部署**: 快速回滚
- **金丝雀发布**: 渐进式发布

## 📊 监控体系

### 1. 系统监控

#### Prometheus 配置
- **文件**: `config/prometheus/prometheus.yml`
- **功能**:
  - 应用指标收集
  - 系统资源监控
  - 自定义业务指标
  - 告警规则配置

#### 关键指标
```yaml
# 应用性能指标
- http_requests_total
- http_request_duration_seconds
- database_connections_active
- redis_commands_processed

# 业务指标
- trading_orders_total
- market_data_updates
- strategy_executions
- user_sessions_active
```

#### Grafana 仪表板
- **端口**: 3001
- **功能**:
  - 实时监控面板
  - 历史数据分析
  - 自定义告警
  - 多维度视图

### 2. 日志管理

#### 日志配置
```
logs/
├── backend.log      # 后端应用日志
├── frontend.log     # 前端构建日志
├── nginx/          # Web 服务器日志
├── database.log     # 数据库日志
└── trading.log      # 交易系统日志
```

#### 日志级别
- **DEBUG**: 开发环境详细信息
- **INFO**: 一般运行信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 3. 健康检查

#### 健康检查端点
```
# 后端健康检查
GET /health
{
  "status": "healthy",
  "timestamp": "2025-01-XX",
  "services": {
    "database": "connected",
    "redis": "connected",
    "market_data": "active"
  }
}

# 数据库健康检查
GET /health/db
{
  "status": "healthy",
  "connections": 5,
  "query_time": 0.003
}
```

#### 监控检查项
- 服务存活状态
- 响应时间监控
- 错误率统计
- 资源使用情况
- 业务指标监控

### 4. 告警配置

#### 告警规则
```yaml
# 服务不可用告警
- alert: ServiceDown
  expr: up == 0
  for: 1m
  
# 响应时间过长告警  
- alert: HighResponseTime
  expr: http_request_duration_seconds > 1
  for: 2m
  
# 错误率过高告警
- alert: HighErrorRate
  expr: rate(http_requests_total{status="5xx"}[5m]) > 0.1
  for: 1m
```

#### 通知渠道
- Email 通知
- Slack 集成
- 钉钉通知
- 短信告警

## 🔧 运维操作

### 1. 日常维护

#### 服务状态检查
```bash
# Docker 环境
docker-compose ps
docker-compose logs -f [service_name]

# Kubernetes 环境
kubectl get pods -n quant-platform
kubectl logs -f deployment/quant-backend -n quant-platform
```

#### 性能监控
```bash
# 系统资源
htop
iotop -a
nethogs

# 容器资源
docker stats
kubectl top pods -n quant-platform
```

### 2. 备份和恢复

#### 数据备份
```bash
# 数据库备份
docker exec quant-postgres pg_dump -U quant_user -d quant_db > backup.sql

# 完整备份（使用部署脚本）
./scripts/deploy-linux.sh --backup-only
```

#### 数据恢复
```bash
# 数据库恢复
docker exec -i quant-postgres psql -U quant_user -d quant_db < backup.sql

# 使用备份恢复
./scripts/deploy-linux.sh --restore-from backup_20250101_120000
```

### 3. 扩容和优化

#### 水平扩容
```bash
# Docker Compose
docker-compose up -d --scale backend=3

# Kubernetes
kubectl scale deployment quant-backend --replicas=3 -n quant-platform
```

#### 性能优化
- 数据库连接池调优
- Redis 缓存策略
- 静态资源 CDN
- 负载均衡配置

### 4. 故障排除

#### 常见问题
1. **服务无法启动**
   - 检查端口占用
   - 验证环境变量
   - 查看日志文件

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 检查网络连接

3. **前端页面空白**
   - 检查构建过程
   - 验证API连接
   - 查看浏览器控制台

#### 诊断工具
```bash
# 网络诊断
curl -v http://localhost:8000/health
nslookup api.yourdomain.com

# 容器诊断  
docker exec -it quant-backend /bin/bash
kubectl exec -it pod/quant-backend-xxx -n quant-platform -- /bin/bash

# 日志分析
tail -f logs/backend.log | grep ERROR
journalctl -u docker.service -f
```

## 🛡️ 安全配置

### 1. 容器安全
- 使用非 root 用户
- 只读文件系统
- 资源限制
- 安全扫描

### 2. 网络安全
- 内部网络隔离
- TLS/SSL 加密
- 防火墙配置
- API 访问控制

### 3. 数据安全
- 密钥管理
- 数据库加密
- 备份加密
- 访问审计

## 📈 性能优化

### 1. 数据库优化
- 索引优化
- 连接池配置
- 查询优化
- 分区策略

### 2. 缓存策略
- Redis 缓存
- 应用缓存
- CDN 缓存
- 浏览器缓存

### 3. 负载均衡
- 服务负载均衡
- 数据库读写分离
- 静态资源分离
- 地理分布

## 📞 支持和维护

### 联系方式
- 技术支持: [<EMAIL>]
- 紧急联系: [<EMAIL>]
- 文档更新: [<EMAIL>]

### 维护窗口
- 常规维护: 每周日 02:00-04:00
- 紧急维护: 随时
- 系统更新: 每月第一个周六

### SLA 目标
- 系统可用性: 99.9%
- 响应时间: < 200ms (95th percentile)
- 错误率: < 0.1%
- 恢复时间: < 15 分钟

---

## 更新日志

- **2025-01-XX**: 初始版本发布
- **2025-01-XX**: 添加 Linux 容器优化
- **2025-01-XX**: 完善 CI/CD Pipeline
- **2025-01-XX**: 增加监控和告警配置