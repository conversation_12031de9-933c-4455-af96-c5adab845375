/**
 * 优化的API客户端
 * 包含缓存、重试、批量请求等性能优化功能
 */

import { performanceMonitor, cacheManager } from '@/utils/performance'

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  headers?: Record<string, string>
  cache?: boolean
  cacheTTL?: number
  retry?: number
  timeout?: number
  priority?: 'high' | 'normal' | 'low'
}

// 响应接口
interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  timestamp?: string
}

// 请求队列项
interface QueueItem {
  config: RequestConfig
  resolve: (value: any) => void
  reject: (reason: any) => void
  priority: number
  timestamp: number
}

class OptimizedApiClient {
  private baseURL: string
  private requestQueue: QueueItem[] = []
  private activeRequests = new Set<string>()
  private maxConcurrentRequests = 6
  private batchRequests = new Map<string, QueueItem[]>()
  private batchTimeout = 50 // 50ms批量延迟

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.startRequestProcessor()
  }

  // 主要请求方法
  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const fullUrl = `${this.baseURL}${config.url}`
    const cacheKey = this.getCacheKey(config)

    // 检查缓存
    if (config.cache !== false) {
      const cached = cacheManager.get(cacheKey)
      if (cached) {
        console.log('📦 使用缓存数据:', config.url)
        return cached
      }
    }

    // 检查是否可以批量处理
    if (this.canBatch(config)) {
      return this.addToBatch(config)
    }

    // 添加到请求队列
    return new Promise((resolve, reject) => {
      const priority = this.getPriorityValue(config.priority || 'normal')
      this.requestQueue.push({
        config,
        resolve,
        reject,
        priority,
        timestamp: Date.now()
      })
      
      // 按优先级排序
      this.requestQueue.sort((a, b) => b.priority - a.priority)
    })
  }

  // 启动请求处理器
  private startRequestProcessor() {
    setInterval(() => {
      this.processQueue()
      this.processBatches()
    }, 10)
  }

  // 处理请求队列
  private async processQueue() {
    while (
      this.requestQueue.length > 0 && 
      this.activeRequests.size < this.maxConcurrentRequests
    ) {
      const item = this.requestQueue.shift()!
      this.executeRequest(item)
    }
  }

  // 执行单个请求
  private async executeRequest(item: QueueItem) {
    const { config, resolve, reject } = item
    const requestId = this.getRequestId(config)
    
    this.activeRequests.add(requestId)

    try {
      const response = await this.performRequest(config)
      
      // 缓存响应
      if (config.cache !== false && response.success) {
        const cacheKey = this.getCacheKey(config)
        const ttl = config.cacheTTL || 5 * 60 * 1000 // 默认5分钟
        cacheManager.set(cacheKey, response, ttl)
      }
      
      resolve(response)
    } catch (error) {
      // 重试逻辑
      if (config.retry && config.retry > 0) {
        config.retry--
        this.requestQueue.unshift(item) // 重新加入队列头部
      } else {
        reject(error)
      }
    } finally {
      this.activeRequests.delete(requestId)
    }
  }

  // 执行实际的HTTP请求
  private async performRequest(config: RequestConfig): Promise<ApiResponse> {
    const controller = new AbortController()
    const timeout = config.timeout || 10000

    // 设置超时
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await performanceMonitor.measureApiCall(
        () => fetch(`${this.baseURL}${config.url}`, {
          method: config.method || 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: config.data ? JSON.stringify(config.data) : undefined,
          signal: controller.signal
        }),
        config.url
      )

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  // 批量请求处理
  private canBatch(config: RequestConfig): boolean {
    return config.method === 'GET' && config.url.includes('/market/')
  }

  private addToBatch(config: RequestConfig): Promise<ApiResponse> {
    const batchKey = this.getBatchKey(config)
    
    if (!this.batchRequests.has(batchKey)) {
      this.batchRequests.set(batchKey, [])
      
      // 延迟执行批量请求
      setTimeout(() => {
        this.executeBatch(batchKey)
      }, this.batchTimeout)
    }

    return new Promise((resolve, reject) => {
      this.batchRequests.get(batchKey)!.push({
        config,
        resolve,
        reject,
        priority: 0,
        timestamp: Date.now()
      })
    })
  }

  private async executeBatch(batchKey: string) {
    const items = this.batchRequests.get(batchKey)
    if (!items || items.length === 0) return

    this.batchRequests.delete(batchKey)

    try {
      // 合并请求参数
      const symbols = items.map(item => this.extractSymbol(item.config.url))
      const batchConfig: RequestConfig = {
        url: '/market/batch',
        method: 'POST',
        data: { symbols },
        cache: true
      }

      const response = await this.performRequest(batchConfig)
      
      // 分发响应给各个请求
      items.forEach((item, index) => {
        const symbol = symbols[index]
        const data = response.data[symbol]
        item.resolve({
          success: true,
          data,
          timestamp: response.timestamp
        })
      })
    } catch (error) {
      // 如果批量请求失败，回退到单个请求
      items.forEach(item => {
        this.requestQueue.push(item)
      })
    }
  }

  // 工具方法
  private getCacheKey(config: RequestConfig): string {
    const params = config.data ? JSON.stringify(config.data) : ''
    return `${config.method || 'GET'}:${config.url}:${params}`
  }

  private getRequestId(config: RequestConfig): string {
    return `${config.method || 'GET'}:${config.url}:${Date.now()}`
  }

  private getBatchKey(config: RequestConfig): string {
    return config.url.split('/')[1] // 按API分组
  }

  private extractSymbol(url: string): string {
    const match = url.match(/\/([^\/]+)$/)
    return match ? match[1] : ''
  }

  private getPriorityValue(priority: string): number {
    switch (priority) {
      case 'high': return 3
      case 'normal': return 2
      case 'low': return 1
      default: return 2
    }
  }

  // 便捷方法
  get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request({ ...config, url, method: 'GET' })
  }

  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request({ ...config, url, method: 'POST', data })
  }

  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request({ ...config, url, method: 'PUT', data })
  }

  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request({ ...config, url, method: 'DELETE' })
  }

  // 预加载数据
  preload(urls: string[]) {
    urls.forEach(url => {
      this.get(url, { priority: 'low', cache: true })
    })
  }

  // 清除缓存
  clearCache() {
    cacheManager.clear()
  }

  // 获取统计信息
  getStats() {
    return {
      queueLength: this.requestQueue.length,
      activeRequests: this.activeRequests.size,
      cacheSize: cacheManager.size(),
      batchGroups: this.batchRequests.size
    }
  }
}

// 创建默认实例
const apiClient = new OptimizedApiClient(import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1')

export default apiClient

// 导出类型
export type { RequestConfig, ApiResponse }
