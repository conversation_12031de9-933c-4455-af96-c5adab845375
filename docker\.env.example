# Environment Configuration Template
# Copy to .env and configure for your environment

# === Service Ports ===
API_PORT=8000
FRONTEND_PORT=5173
DB_PORT=5432
REDIS_PORT=6379
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# === Database Configuration ===
POSTGRES_PASSWORD=your-secure-postgres-password
POSTGRES_DB=quant_db
POSTGRES_USER=quant_user

# === Redis Configuration ===
REDIS_PASSWORD=your-secure-redis-password

# === Application Security ===
SECRET_KEY=your-very-secure-secret-key-minimum-32-characters
JWT_SECRET_KEY=your-very-secure-jwt-secret-key-minimum-32-characters
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# === Monitoring ===
GRAFANA_PASSWORD=your-secure-grafana-admin-password

# === Environment ===
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=false

# === CORS Settings (Development) ===
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# === Celery Configuration ===
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# === External Services ===
# MARKET_DATA_API_KEY=your-market-data-api-key
# NOTIFICATION_SERVICE_URL=your-notification-service-url

# === Performance Tuning ===
GUNICORN_WORKERS=4
CELERY_WORKER_CONCURRENCY=4
PROMETHEUS_RETENTION_TIME=200h

# === SSL/TLS (Production) ===
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem