"""
数据库Schema审计系统
提供数据库结构验证、版本检查和升级建议
"""

import asyncio
import hashlib
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import sqlalchemy as sa
from sqlalchemy import inspect, text
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from sqlalchemy.dialects import postgresql

from app.core.database import get_async_engine, get_async_session

logger = logging.getLogger(__name__)


class SchemaStatus(Enum):
    """Schema状态枚举"""
    HEALTHY = "healthy"
    OUTDATED = "outdated"
    CORRUPTED = "corrupted"
    MISSING = "missing"
    ERROR = "error"


class SeverityLevel(Enum):
    """问题严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class SchemaIssue:
    """Schema问题记录"""
    table_name: Optional[str]
    issue_type: str
    description: str
    severity: SeverityLevel
    recommendation: str
    detected_at: datetime = None
    
    def __post_init__(self):
        if self.detected_at is None:
            self.detected_at = datetime.now()


@dataclass
class TableMetrics:
    """表指标数据"""
    name: str
    row_count: int
    size_bytes: int
    index_count: int
    foreign_key_count: int
    last_analyzed: Optional[datetime] = None


@dataclass
class SchemaAuditReport:
    """Schema审计报告"""
    database_version: str
    alembic_version: Optional[str]
    status: SchemaStatus
    total_tables: int
    total_indexes: int
    total_foreign_keys: int
    database_size_mb: float
    issues: List[SchemaIssue]
    table_metrics: List[TableMetrics]
    recommendations: List[str]
    audit_timestamp: datetime = None
    
    def __post_init__(self):
        if self.audit_timestamp is None:
            self.audit_timestamp = datetime.now()


class DatabaseSchemaAuditor:
    """数据库Schema审计器"""
    
    def __init__(self, engine: AsyncEngine):
        self.engine = engine
        self.expected_tables = {
            'users', 'user_sessions', 'symbols', 'market_data', 'kline_data',
            'accounts', 'orders', 'trades', 'positions', 'strategies',
            'strategy_instances', 'backtest_tasks', 'backtest_results', 'watchlists'
        }
        self.critical_indexes = {
            'ix_users_username', 'ix_users_email',
            'ix_market_data_symbol_timestamp', 'ix_kline_data_symbol_type_timestamp',
            'ix_orders_user_id', 'ix_trades_user_id', 'ix_positions_account_symbol'
        }
    
    async def run_full_audit(self) -> SchemaAuditReport:
        """运行完整的Schema审计"""
        logger.info("开始数据库Schema审计")
        
        try:
            # 基础信息收集
            db_version = await self._get_database_version()
            alembic_version = await self._get_alembic_version()
            
            # 结构检查
            tables_info = await self._analyze_table_structure()
            indexes_info = await self._analyze_indexes()
            foreign_keys_info = await self._analyze_foreign_keys()
            
            # 性能指标
            table_metrics = await self._collect_table_metrics()
            db_size = await self._get_database_size()
            
            # 问题检测
            issues = []
            issues.extend(await self._check_missing_tables())
            issues.extend(await self._check_missing_indexes())
            issues.extend(await self._check_data_integrity())
            issues.extend(await self._check_performance_issues())
            
            # 生成状态和建议
            status = self._determine_overall_status(issues)
            recommendations = self._generate_recommendations(issues, table_metrics)
            
            report = SchemaAuditReport(
                database_version=db_version,
                alembic_version=alembic_version,
                status=status,
                total_tables=len(tables_info),
                total_indexes=len(indexes_info),
                total_foreign_keys=len(foreign_keys_info),
                database_size_mb=db_size,
                issues=issues,
                table_metrics=table_metrics,
                recommendations=recommendations
            )
            
            logger.info(f"Schema审计完成，状态: {status.value}, 发现 {len(issues)} 个问题")
            return report
            
        except Exception as e:
            logger.error(f"Schema审计失败: {e}")
            return SchemaAuditReport(
                database_version="unknown",
                alembic_version=None,
                status=SchemaStatus.ERROR,
                total_tables=0,
                total_indexes=0,
                total_foreign_keys=0,
                database_size_mb=0.0,
                issues=[SchemaIssue(
                    table_name=None,
                    issue_type="audit_error",
                    description=f"审计过程中发生错误: {str(e)}",
                    severity=SeverityLevel.CRITICAL,
                    recommendation="检查数据库连接和权限设置"
                )],
                table_metrics=[],
                recommendations=["修复审计错误后重新运行审计"]
            )
    
    async def _get_database_version(self) -> str:
        """获取数据库版本"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("SELECT version()"))
            version = result.scalar()
            return version.split()[1] if version else "unknown"
    
    async def _get_alembic_version(self) -> Optional[str]:
        """获取Alembic版本"""
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute(text("SELECT version_num FROM alembic_version"))
                return result.scalar()
        except Exception:
            return None
    
    async def _analyze_table_structure(self) -> List[Dict[str, Any]]:
        """分析表结构"""
        async with self.engine.begin() as conn:
            inspector = inspect(conn.sync_engine)
            tables = []
            
            for table_name in inspector.get_table_names():
                columns = inspector.get_columns(table_name)
                tables.append({
                    'name': table_name,
                    'column_count': len(columns),
                    'columns': [col['name'] for col in columns]
                })
            
            return tables
    
    async def _analyze_indexes(self) -> List[Dict[str, Any]]:
        """分析索引"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE schemaname = 'public'
                ORDER BY tablename, indexname
            """))
            
            return [dict(row) for row in result]
    
    async def _analyze_foreign_keys(self) -> List[Dict[str, Any]]:
        """分析外键约束"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT 
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name,
                    tc.constraint_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = 'public'
            """))
            
            return [dict(row) for row in result]
    
    async def _collect_table_metrics(self) -> List[TableMetrics]:
        """收集表指标"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins + n_tup_upd + n_tup_del AS total_operations,
                    n_live_tup AS row_count,
                    pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes,
                    last_analyze
                FROM pg_stat_user_tables
                WHERE schemaname = 'public'
            """))
            
            metrics = []
            for row in result:
                # 获取索引数量
                index_result = await conn.execute(text("""
                    SELECT COUNT(*) FROM pg_indexes 
                    WHERE schemaname = 'public' AND tablename = :table_name
                """), {"table_name": row.tablename})
                index_count = index_result.scalar()
                
                # 获取外键数量
                fk_result = await conn.execute(text("""
                    SELECT COUNT(*) FROM information_schema.table_constraints 
                    WHERE table_name = :table_name 
                    AND constraint_type = 'FOREIGN KEY'
                    AND table_schema = 'public'
                """), {"table_name": row.tablename})
                fk_count = fk_result.scalar()
                
                metrics.append(TableMetrics(
                    name=row.tablename,
                    row_count=row.row_count or 0,
                    size_bytes=row.size_bytes or 0,
                    index_count=index_count or 0,
                    foreign_key_count=fk_count or 0,
                    last_analyzed=row.last_analyze
                ))
            
            return metrics
    
    async def _get_database_size(self) -> float:
        """获取数据库大小（MB）"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT pg_size_pretty(pg_database_size(current_database()))
            """))
            
            size_str = result.scalar()
            # 解析大小字符串，转换为MB
            if 'kB' in size_str:
                return float(size_str.replace(' kB', '')) / 1024
            elif 'MB' in size_str:
                return float(size_str.replace(' MB', ''))
            elif 'GB' in size_str:
                return float(size_str.replace(' GB', '')) * 1024
            else:
                return 0.0
    
    async def _check_missing_tables(self) -> List[SchemaIssue]:
        """检查缺失的表"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            
            existing_tables = {row[0] for row in result}
            missing_tables = self.expected_tables - existing_tables
            
            issues = []
            for table in missing_tables:
                issues.append(SchemaIssue(
                    table_name=table,
                    issue_type="missing_table",
                    description=f"缺失关键表: {table}",
                    severity=SeverityLevel.CRITICAL,
                    recommendation=f"运行数据库迁移创建表 {table}"
                ))
            
            return issues
    
    async def _check_missing_indexes(self) -> List[SchemaIssue]:
        """检查缺失的关键索引"""
        async with self.engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT indexname FROM pg_indexes 
                WHERE schemaname = 'public'
            """))
            
            existing_indexes = {row[0] for row in result}
            missing_indexes = self.critical_indexes - existing_indexes
            
            issues = []
            for index in missing_indexes:
                issues.append(SchemaIssue(
                    table_name=None,
                    issue_type="missing_index",
                    description=f"缺失关键索引: {index}",
                    severity=SeverityLevel.WARNING,
                    recommendation=f"创建索引 {index} 以提升查询性能"
                ))
            
            return issues
    
    async def _check_data_integrity(self) -> List[SchemaIssue]:
        """检查数据完整性"""
        issues = []
        
        try:
            async with self.engine.begin() as conn:
                # 检查外键约束违规
                tables_to_check = ['orders', 'trades', 'positions', 'strategies']
                
                for table in tables_to_check:
                    if table == 'orders':
                        result = await conn.execute(text("""
                            SELECT COUNT(*) FROM orders o
                            LEFT JOIN users u ON o.user_id = u.id
                            WHERE u.id IS NULL
                        """))
                        orphaned_count = result.scalar()
                        
                        if orphaned_count > 0:
                            issues.append(SchemaIssue(
                                table_name='orders',
                                issue_type="data_integrity",
                                description=f"发现 {orphaned_count} 个孤立订单记录",
                                severity=SeverityLevel.ERROR,
                                recommendation="清理或修复孤立的订单记录"
                            ))
                    
        except Exception as e:
            logger.warning(f"数据完整性检查时出错: {e}")
        
        return issues
    
    async def _check_performance_issues(self) -> List[SchemaIssue]:
        """检查性能问题"""
        issues = []
        
        try:
            async with self.engine.begin() as conn:
                # 检查大表是否缺少统计信息
                result = await conn.execute(text("""
                    SELECT tablename, n_live_tup, last_analyze
                    FROM pg_stat_user_tables
                    WHERE schemaname = 'public'
                    AND n_live_tup > 10000
                    AND (last_analyze IS NULL OR last_analyze < NOW() - INTERVAL '7 days')
                """))
                
                for row in result:
                    issues.append(SchemaIssue(
                        table_name=row.tablename,
                        issue_type="performance",
                        description=f"表 {row.tablename} 统计信息过期或缺失",
                        severity=SeverityLevel.WARNING,
                        recommendation=f"对表 {row.tablename} 运行 ANALYZE 命令"
                    ))
                
        except Exception as e:
            logger.warning(f"性能问题检查时出错: {e}")
        
        return issues
    
    def _determine_overall_status(self, issues: List[SchemaIssue]) -> SchemaStatus:
        """确定整体状态"""
        if not issues:
            return SchemaStatus.HEALTHY
        
        has_critical = any(issue.severity == SeverityLevel.CRITICAL for issue in issues)
        has_error = any(issue.severity == SeverityLevel.ERROR for issue in issues)
        
        if has_critical:
            return SchemaStatus.CORRUPTED
        elif has_error:
            return SchemaStatus.OUTDATED
        else:
            return SchemaStatus.HEALTHY
    
    def _generate_recommendations(
        self, 
        issues: List[SchemaIssue], 
        table_metrics: List[TableMetrics]
    ) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于问题生成建议
        critical_issues = [i for i in issues if i.severity == SeverityLevel.CRITICAL]
        if critical_issues:
            recommendations.append("立即修复所有关键问题以确保系统稳定运行")
        
        error_issues = [i for i in issues if i.severity == SeverityLevel.ERROR]
        if error_issues:
            recommendations.append("尽快修复错误级别的问题以避免数据丢失")
        
        # 基于表指标生成建议
        large_tables = [m for m in table_metrics if m.row_count > 1000000]
        if large_tables:
            table_names = ', '.join([t.name for t in large_tables])
            recommendations.append(f"考虑对大表 ({table_names}) 进行分区或归档")
        
        # 索引建议
        under_indexed_tables = [m for m in table_metrics if m.row_count > 10000 and m.index_count < 3]
        if under_indexed_tables:
            table_names = ', '.join([t.name for t in under_indexed_tables])
            recommendations.append(f"考虑为高频查询表 ({table_names}) 添加更多索引")
        
        # 维护建议
        recommendations.append("定期运行 VACUUM ANALYZE 以优化数据库性能")
        recommendations.append("建议每月运行一次完整的Schema审计")
        
        return recommendations


class SchemaVersionManager:
    """Schema版本管理器"""
    
    def __init__(self, engine: AsyncEngine):
        self.engine = engine
    
    async def get_current_version(self) -> Optional[str]:
        """获取当前Schema版本"""
        auditor = DatabaseSchemaAuditor(self.engine)
        return await auditor._get_alembic_version()
    
    async def get_version_history(self) -> List[Dict[str, Any]]:
        """获取版本历史"""
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute(text("""
                    SELECT version_num, applied_at 
                    FROM alembic_version_history 
                    ORDER BY applied_at DESC
                """))
                
                return [dict(row) for row in result]
        except Exception:
            # 如果历史表不存在，返回当前版本
            current = await self.get_current_version()
            if current:
                return [{"version_num": current, "applied_at": datetime.now()}]
            return []
    
    async def create_schema_snapshot(self) -> Dict[str, Any]:
        """创建Schema快照"""
        auditor = DatabaseSchemaAuditor(self.engine)
        report = await auditor.run_full_audit()
        
        snapshot = {
            "timestamp": datetime.now().isoformat(),
            "database_version": report.database_version,
            "alembic_version": report.alembic_version,
            "total_tables": report.total_tables,
            "total_indexes": report.total_indexes,
            "database_size_mb": report.database_size_mb,
            "table_metrics": [
                {
                    "name": m.name,
                    "row_count": m.row_count,
                    "size_bytes": m.size_bytes,
                    "index_count": m.index_count
                }
                for m in report.table_metrics
            ]
        }
        
        # 生成快照哈希
        snapshot_str = json.dumps(snapshot, sort_keys=True)
        snapshot["hash"] = hashlib.sha256(snapshot_str.encode()).hexdigest()
        
        return snapshot


async def run_schema_audit() -> SchemaAuditReport:
    """运行Schema审计的便捷函数"""
    engine = get_async_engine()
    auditor = DatabaseSchemaAuditor(engine)
    return await auditor.run_full_audit()


async def get_schema_version_info() -> Dict[str, Any]:
    """获取Schema版本信息"""
    engine = get_async_engine()
    manager = SchemaVersionManager(engine)
    
    current_version = await manager.get_current_version()
    version_history = await manager.get_version_history()
    snapshot = await manager.create_schema_snapshot()
    
    return {
        "current_version": current_version,
        "version_history": version_history,
        "latest_snapshot": snapshot
    }


if __name__ == "__main__":
    async def main():
        """测试Schema审计"""
        report = await run_schema_audit()
        print(f"审计状态: {report.status.value}")
        print(f"发现问题: {len(report.issues)}")
        for issue in report.issues:
            print(f"  - {issue.severity.value}: {issue.description}")
        
        version_info = await get_schema_version_info()
        print(f"当前版本: {version_info['current_version']}")
    
    asyncio.run(main())