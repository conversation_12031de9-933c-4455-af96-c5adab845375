<template>
  <div class="strategy-view">
    <div class="page-header">
      <h1>🧠 策略研发</h1>
      <p>量化投资策略开发与管理</p>
    </div>

    <div class="strategy-content">
      <div class="strategy-card">
        <h2>📈 策略管理</h2>
        <p>策略开发工具正在完善中...</p>
        
        <div class="feature-list">
          <div class="feature-item">
            <span class="feature-icon">🔬</span>
            <span>策略研发工具</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📊</span>
            <span>回测分析系统</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🎯</span>
            <span>策略优化引擎</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📚</span>
            <span>策略库管理</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 策略视图组件
</script>

<style scoped>
.strategy-view {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.strategy-content {
  display: grid;
  gap: 2rem;
}

.strategy-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.strategy-card h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.feature-icon {
  font-size: 1.5rem;
}
</style>
