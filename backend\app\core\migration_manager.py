"""
数据库迁移管理器
提供迁移执行、回滚、状态检查等功能
"""

import asyncio
import logging
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from alembic import command
from alembic.config import Config
from alembic.runtime.migration import MigrationContext
from alembic.script import ScriptDirectory
from sqlalchemy.ext.asyncio import AsyncEngine

from app.core.database import get_async_engine
from app.core.schema_audit import DatabaseSchemaAuditor, SchemaAuditReport

logger = logging.getLogger(__name__)


class MigrationStatus(Enum):
    """迁移状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


@dataclass
class MigrationInfo:
    """迁移信息"""
    revision: str
    down_revision: Optional[str]
    description: str
    status: MigrationStatus
    created_at: Optional[datetime] = None
    executed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class DatabaseMigrationManager:
    """数据库迁移管理器"""
    
    def __init__(self, alembic_cfg_path: Optional[str] = None):
        self.project_root = Path(__file__).parent.parent.parent
        self.alembic_cfg_path = alembic_cfg_path or str(self.project_root / "alembic.ini")
        self.migrations_dir = self.project_root / "migrations"
        self.versions_dir = self.migrations_dir / "versions"
        
        # 确保配置文件存在
        if not Path(self.alembic_cfg_path).exists():
            logger.warning(f"Alembic配置文件不存在: {self.alembic_cfg_path}")
        
        self.alembic_cfg = None
        self._init_alembic_config()
    
    def _init_alembic_config(self):
        """初始化Alembic配置"""
        try:
            if Path(self.alembic_cfg_path).exists():
                self.alembic_cfg = Config(self.alembic_cfg_path)
                # 设置脚本位置
                self.alembic_cfg.set_main_option("script_location", str(self.migrations_dir))
                logger.info("Alembic配置初始化成功")
            else:
                logger.warning("Alembic配置文件不存在，某些功能可能不可用")
        except Exception as e:
            logger.error(f"初始化Alembic配置失败: {e}")
            self.alembic_cfg = None
    
    async def get_migration_status(self) -> Dict[str, Any]:
        """获取迁移状态概览"""
        try:
            engine = get_async_engine()
            
            # 获取数据库当前版本
            current_version = await self._get_current_database_version(engine)
            
            # 获取所有可用迁移
            available_migrations = self._get_available_migrations()
            
            # 获取已执行的迁移历史
            executed_migrations = await self._get_executed_migrations(engine)
            
            # 检查待执行的迁移
            pending_migrations = self._get_pending_migrations(
                available_migrations, 
                executed_migrations
            )
            
            status = {
                "current_version": current_version,
                "available_migrations_count": len(available_migrations),
                "executed_migrations_count": len(executed_migrations),
                "pending_migrations_count": len(pending_migrations),
                "available_migrations": available_migrations,
                "executed_migrations": executed_migrations,
                "pending_migrations": pending_migrations,
                "database_needs_upgrade": len(pending_migrations) > 0,
                "last_check": datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取迁移状态失败: {e}")
            return {
                "error": str(e),
                "current_version": None,
                "available_migrations_count": 0,
                "executed_migrations_count": 0,
                "pending_migrations_count": 0,
                "database_needs_upgrade": False,
                "last_check": datetime.now().isoformat()
            }
    
    async def _get_current_database_version(self, engine: AsyncEngine) -> Optional[str]:
        """获取数据库当前版本"""
        try:
            from sqlalchemy import text
            async with engine.begin() as conn:
                result = await conn.execute(text("SELECT version_num FROM alembic_version"))
                return result.scalar()
        except Exception as e:
            logger.warning(f"无法获取数据库版本: {e}")
            return None
    
    def _get_available_migrations(self) -> List[MigrationInfo]:
        """获取所有可用迁移"""
        migrations = []
        
        if not self.alembic_cfg:
            return migrations
        
        try:
            script_dir = ScriptDirectory.from_config(self.alembic_cfg)
            
            for revision in script_dir.walk_revisions():
                migrations.append(MigrationInfo(
                    revision=revision.revision,
                    down_revision=revision.down_revision,
                    description=revision.doc or "No description",
                    status=MigrationStatus.PENDING,
                    created_at=None  # Alembic doesn't provide creation time
                ))
            
        except Exception as e:
            logger.error(f"获取可用迁移失败: {e}")
        
        return migrations
    
    async def _get_executed_migrations(self, engine: AsyncEngine) -> List[str]:
        """获取已执行的迁移"""
        try:
            from sqlalchemy import text
            async with engine.begin() as conn:
                # 检查是否存在alembic_version表
                table_exists = await conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'alembic_version'
                    )
                """))
                
                if not table_exists.scalar():
                    return []
                
                # 获取版本历史（如果存在历史表）
                try:
                    result = await conn.execute(text("""
                        SELECT version_num FROM alembic_version_history 
                        ORDER BY applied_at
                    """))
                    return [row[0] for row in result]
                except:
                    # 如果没有历史表，只返回当前版本
                    result = await conn.execute(text("SELECT version_num FROM alembic_version"))
                    current = result.scalar()
                    return [current] if current else []
                    
        except Exception as e:
            logger.error(f"获取执行历史失败: {e}")
            return []
    
    def _get_pending_migrations(
        self, 
        available: List[MigrationInfo], 
        executed: List[str]
    ) -> List[MigrationInfo]:
        """获取待执行的迁移"""
        executed_set = set(executed)
        return [
            migration for migration in available 
            if migration.revision not in executed_set
        ]
    
    async def upgrade_database(self, target_revision: str = "head") -> Dict[str, Any]:
        """升级数据库"""
        if not self.alembic_cfg:
            return {
                "success": False,
                "error": "Alembic配置未初始化",
                "executed_at": datetime.now().isoformat()
            }
        
        try:
            logger.info(f"开始数据库升级到版本: {target_revision}")
            
            # 执行升级前的检查
            pre_check = await self._pre_migration_check()
            if not pre_check["success"]:
                return {
                    "success": False,
                    "error": f"升级前检查失败: {pre_check['error']}",
                    "executed_at": datetime.now().isoformat()
                }
            
            # 创建备份（如果需要）
            backup_info = await self._create_backup_if_needed()
            
            # 执行升级
            start_time = datetime.now()
            
            try:
                # 使用subprocess运行alembic命令以避免线程问题
                result = subprocess.run([
                    sys.executable, "-m", "alembic", 
                    "-c", self.alembic_cfg_path,
                    "upgrade", target_revision
                ], 
                cwd=str(self.project_root),
                capture_output=True, 
                text=True, 
                timeout=300  # 5分钟超时
                )
                
                if result.returncode == 0:
                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()
                    
                    logger.info(f"数据库升级成功，耗时: {duration:.2f}秒")
                    
                    # 执行升级后检查
                    post_check = await self._post_migration_check()
                    
                    return {
                        "success": True,
                        "target_revision": target_revision,
                        "duration_seconds": duration,
                        "backup_info": backup_info,
                        "post_check": post_check,
                        "executed_at": end_time.isoformat(),
                        "output": result.stdout
                    }
                else:
                    error_msg = result.stderr or result.stdout or "未知错误"
                    logger.error(f"数据库升级失败: {error_msg}")
                    
                    return {
                        "success": False,
                        "error": error_msg,
                        "target_revision": target_revision,
                        "backup_info": backup_info,
                        "executed_at": datetime.now().isoformat()
                    }
                    
            except subprocess.TimeoutExpired:
                return {
                    "success": False,
                    "error": "升级操作超时（5分钟）",
                    "target_revision": target_revision,
                    "executed_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"数据库升级异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "target_revision": target_revision,
                "executed_at": datetime.now().isoformat()
            }
    
    async def downgrade_database(self, target_revision: str) -> Dict[str, Any]:
        """降级数据库"""
        if not self.alembic_cfg:
            return {
                "success": False,
                "error": "Alembic配置未初始化",
                "executed_at": datetime.now().isoformat()
            }
        
        try:
            logger.warning(f"开始数据库降级到版本: {target_revision}")
            
            # 降级前强制创建备份
            backup_info = await self._create_backup_if_needed(force=True)
            
            start_time = datetime.now()
            
            result = subprocess.run([
                sys.executable, "-m", "alembic", 
                "-c", self.alembic_cfg_path,
                "downgrade", target_revision
            ], 
            cwd=str(self.project_root),
            capture_output=True, 
            text=True, 
            timeout=300
            )
            
            if result.returncode == 0:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                logger.warning(f"数据库降级成功，耗时: {duration:.2f}秒")
                
                return {
                    "success": True,
                    "target_revision": target_revision,
                    "duration_seconds": duration,
                    "backup_info": backup_info,
                    "executed_at": end_time.isoformat(),
                    "output": result.stdout,
                    "warning": "数据库已降级，请确保应用代码兼容"
                }
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                logger.error(f"数据库降级失败: {error_msg}")
                
                return {
                    "success": False,
                    "error": error_msg,
                    "target_revision": target_revision,
                    "backup_info": backup_info,
                    "executed_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"数据库降级异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "target_revision": target_revision,
                "executed_at": datetime.now().isoformat()
            }
    
    async def _pre_migration_check(self) -> Dict[str, Any]:
        """迁移前检查"""
        try:
            engine = get_async_engine()
            
            # 检查数据库连接
            async with engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            # 检查磁盘空间（简单检查）
            disk_usage = self._check_disk_space()
            
            if disk_usage < 1024:  # 少于1GB
                return {
                    "success": False,
                    "error": f"磁盘空间不足: {disk_usage:.2f}MB"
                }
            
            return {
                "success": True,
                "disk_space_mb": disk_usage,
                "checks_passed": ["database_connection", "disk_space"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _post_migration_check(self) -> Dict[str, Any]:
        """迁移后检查"""
        try:
            engine = get_async_engine()
            auditor = DatabaseSchemaAuditor(engine)
            
            # 运行快速审计
            report = await auditor.run_full_audit()
            
            return {
                "schema_status": report.status.value,
                "total_issues": len(report.issues),
                "critical_issues": len([
                    i for i in report.issues 
                    if i.severity.name == "CRITICAL"
                ]),
                "recommendations": report.recommendations[:3]  # 只返回前3个建议
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "schema_status": "unknown"
            }
    
    def _check_disk_space(self) -> float:
        """检查磁盘空间（MB）"""
        try:
            import shutil
            total, used, free = shutil.disk_usage("/")
            return free / (1024 * 1024)  # 转换为MB
        except:
            return 10240  # 默认返回10GB
    
    async def _create_backup_if_needed(self, force: bool = False) -> Optional[Dict[str, Any]]:
        """如果需要则创建备份"""
        # 这里可以实现数据库备份逻辑
        # 为了简化，现在只返回备份信息占位符
        if force:
            return {
                "backup_created": True,
                "backup_path": f"/tmp/db_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql",
                "backup_size_mb": 0.0,
                "created_at": datetime.now().isoformat()
            }
        return None
    
    def generate_migration(
        self, 
        message: str, 
        autogenerate: bool = True
    ) -> Dict[str, Any]:
        """生成新的迁移文件"""
        if not self.alembic_cfg:
            return {
                "success": False,
                "error": "Alembic配置未初始化"
            }
        
        try:
            logger.info(f"生成新迁移: {message}")
            
            cmd_args = [
                sys.executable, "-m", "alembic", 
                "-c", self.alembic_cfg_path,
                "revision"
            ]
            
            if autogenerate:
                cmd_args.append("--autogenerate")
            
            cmd_args.extend(["-m", message])
            
            result = subprocess.run(
                cmd_args,
                cwd=str(self.project_root),
                capture_output=True, 
                text=True, 
                timeout=60
            )
            
            if result.returncode == 0:
                # 解析输出获取生成的文件名
                output_lines = result.stdout.split('\n')
                migration_file = None
                for line in output_lines:
                    if 'Generating' in line and '.py' in line:
                        migration_file = line.split()[-1]
                        break
                
                return {
                    "success": True,
                    "message": message,
                    "migration_file": migration_file,
                    "output": result.stdout,
                    "generated_at": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr or result.stdout or "生成失败",
                    "message": message
                }
                
        except Exception as e:
            logger.error(f"生成迁移失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": message
            }
    
    def get_migration_history(self) -> List[Dict[str, Any]]:
        """获取迁移历史"""
        if not self.alembic_cfg:
            return []
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "alembic", 
                "-c", self.alembic_cfg_path,
                "history", "--verbose"
            ], 
            cwd=str(self.project_root),
            capture_output=True, 
            text=True
            )
            
            if result.returncode == 0:
                # 解析历史输出
                history = []
                lines = result.stdout.split('\n')
                
                for line in lines:
                    line = line.strip()
                    if line and '->' in line:
                        parts = line.split(' -> ')
                        if len(parts) >= 2:
                            history.append({
                                "from_revision": parts[0].strip(),
                                "to_revision": parts[1].split()[0],
                                "description": ' '.join(parts[1].split()[1:]) if len(parts[1].split()) > 1 else ""
                            })
                
                return history
            
        except Exception as e:
            logger.error(f"获取迁移历史失败: {e}")
        
        return []


# 便捷函数
async def get_migration_status() -> Dict[str, Any]:
    """获取迁移状态"""
    manager = DatabaseMigrationManager()
    return await manager.get_migration_status()


async def upgrade_database(target_revision: str = "head") -> Dict[str, Any]:
    """升级数据库"""
    manager = DatabaseMigrationManager()
    return await manager.upgrade_database(target_revision)


async def run_migration_check() -> Dict[str, Any]:
    """运行迁移检查"""
    manager = DatabaseMigrationManager()
    status = await manager.get_migration_status()
    
    return {
        "needs_upgrade": status.get("database_needs_upgrade", False),
        "current_version": status.get("current_version"),
        "pending_count": status.get("pending_migrations_count", 0),
        "status": status
    }


if __name__ == "__main__":
    async def main():
        """测试迁移管理器"""
        print("=== 数据库迁移状态检查 ===")
        status = await get_migration_status()
        print(f"当前版本: {status.get('current_version', 'None')}")
        print(f"待执行迁移: {status.get('pending_migrations_count', 0)}")
        print(f"需要升级: {status.get('database_needs_upgrade', False)}")
        
        if status.get("database_needs_upgrade"):
            print("\n=== 执行数据库升级 ===")
            result = await upgrade_database()
            print(f"升级结果: {'成功' if result.get('success') else '失败'}")
            if not result.get('success'):
                print(f"错误信息: {result.get('error')}")
    
    asyncio.run(main())