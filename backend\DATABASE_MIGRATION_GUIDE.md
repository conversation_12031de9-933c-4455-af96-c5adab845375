# 数据库迁移系统指南

## 概述

本文档描述了量化交易平台的完整数据库迁移和Schema管理系统。该系统提供了数据库版本控制、自动迁移、Schema审计和性能监控等功能。

## 系统架构

### 核心组件

1. **Alembic迁移系统** (`alembic.ini`, `migrations/`)
   - 标准的数据库版本控制
   - 自动生成和执行迁移脚本
   - 支持升级和回滚操作

2. **Schema审计系统** (`app/core/schema_audit.py`)
   - 数据库结构完整性检查
   - 性能问题检测
   - 优化建议生成

3. **迁移管理器** (`app/core/migration_manager.py`)
   - 迁移状态监控
   - 自动化迁移执行
   - 备份和回滚支持

4. **管理脚本** (`scripts/db_manage.py`)
   - 命令行工具
   - 批量操作支持
   - 交互式管理界面

## 迁移版本说明

### 001 - 初始数据库Schema
**文件**: `migrations/versions/001_initial_database_schema.py`

**功能**:
- 创建所有核心数据表
- 用户系统 (users, user_sessions)
- 市场数据 (symbols, market_data, kline_data)
- 交易系统 (accounts, orders, trades, positions)
- 策略系统 (strategies, strategy_instances)
- 回测系统 (backtest_tasks, backtest_results)
- 观察列表 (watchlists)

**关键特性**:
- 完整的外键约束
- 性能优化索引
- 数据类型验证
- 默认值设置

### 002 - CTP集成支持
**文件**: `migrations/versions/002_add_ctp_integration.py`

**功能**:
- CTP账户管理 (ctp_accounts)
- 市场数据订阅 (ctp_subscriptions)
- CTP订单系统 (ctp_orders)
- CTP持仓管理 (ctp_positions)
- Tick数据存储 (ctp_tick_data)
- 系统配置管理 (ctp_configurations)

**关键特性**:
- 期货交易支持
- 实时数据处理
- 多账户管理
- 配置灵活性

### 003 - 性能优化
**文件**: `migrations/versions/003_add_performance_optimization.py`

**功能**:
- 高性能索引优化
- 物化视图 (market_data_daily_summary)
- 查询性能统计 (query_performance_stats)
- 数据库健康监控 (database_health_metrics)
- Schema审计结果存储 (schema_audit_results)
- 连接池监控 (connection_pool_metrics)
- 慢查询日志 (slow_query_log)

**关键特性**:
- 查询性能提升
- 系统监控集成
- 自动统计收集
- 性能问题诊断

## 使用方法

### 命令行工具

基本语法:
```bash
python scripts/db_manage.py <command> [options]
```

#### 1. 检查数据库状态
```bash
python scripts/db_manage.py status
```

输出示例:
```
=== 数据库状态检查 ===
当前数据库版本: 003
可用迁移数量: 3
已执行迁移数量: 3
待执行迁移数量: 0
需要升级: 否

Schema快照哈希: a1b2c3d4e5f6...
```

#### 2. 初始化数据库
```bash
python scripts/db_manage.py init
```

这会执行以下操作:
- 检查当前数据库状态
- 创建Alembic版本表
- 执行所有待执行的迁移
- 运行初始化后审计

#### 3. 升级数据库
```bash
# 升级到最新版本
python scripts/db_manage.py upgrade

# 升级到指定版本
python scripts/db_manage.py upgrade --target 002
```

#### 4. 降级数据库 (危险操作)
```bash
python scripts/db_manage.py downgrade 001
```
⚠️ **警告**: 降级可能导致数据丢失，操作前会自动创建备份。

#### 5. 运行Schema审计
```bash
python scripts/db_manage.py audit
```

审计报告包括:
- 数据库完整性检查
- 性能问题检测
- 索引优化建议
- 表统计信息
- 优化建议

#### 6. 查看迁移历史
```bash
python scripts/db_manage.py history
```

#### 7. 生成新迁移
```bash
python scripts/db_manage.py generate "Add new feature tables"
```

### 编程接口

#### 迁移状态检查
```python
from app.core.migration_manager import get_migration_status

status = await get_migration_status()
print(f"需要升级: {status['database_needs_upgrade']}")
```

#### 执行数据库升级
```python
from app.core.migration_manager import upgrade_database

result = await upgrade_database("head")
if result["success"]:
    print("升级成功")
else:
    print(f"升级失败: {result['error']}")
```

#### Schema审计
```python
from app.core.schema_audit import run_schema_audit

report = await run_schema_audit()
print(f"审计状态: {report.status.value}")
print(f"发现问题: {len(report.issues)}")
```

## 配置说明

### Alembic配置 (`alembic.ini`)

关键配置项:
- `script_location`: 迁移脚本目录
- `sqlalchemy.url`: 数据库连接URL
- `file_template`: 迁移文件命名模板

### 数据库连接

支持的数据库:
- PostgreSQL (推荐)
- MySQL
- SQLite (开发环境)

连接字符串格式:
```
postgresql+asyncpg://user:password@localhost:5432/dbname
```

## 最佳实践

### 1. 迁移开发
- 使用描述性的迁移消息
- 每个迁移应该是原子性的
- 包含必要的数据验证
- 提供完整的回滚逻辑

### 2. 生产环境部署
- 部署前运行完整测试
- 创建数据库备份
- 在维护窗口执行迁移
- 监控迁移执行过程

### 3. 性能优化
- 定期运行Schema审计
- 监控慢查询日志
- 优化高频查询索引
- 定期更新表统计信息

### 4. 安全考虑
- 使用加密连接
- 限制数据库用户权限
- 定期轮换数据库密码
- 审计数据库访问日志

## 监控和维护

### 自动化监控

系统提供以下监控功能:
1. **连接池监控**: 跟踪连接池使用情况
2. **查询性能监控**: 记录慢查询和执行统计
3. **Schema健康检查**: 定期验证数据库结构
4. **存储空间监控**: 跟踪数据库大小增长

### 维护任务

建议的维护计划:
- **每日**: 检查慢查询日志
- **每周**: 运行Schema审计
- **每月**: 更新表统计信息
- **每季度**: 检查索引使用情况和优化

### 故障排除

#### 常见问题

1. **迁移失败**
   ```bash
   # 检查数据库连接
   python scripts/db_manage.py status
   
   # 查看详细错误信息
   python scripts/db_manage.py upgrade --json
   ```

2. **性能问题**
   ```bash
   # 运行性能审计
   python scripts/db_manage.py audit
   
   # 检查慢查询
   SELECT * FROM slow_query_log ORDER BY execution_time_ms DESC LIMIT 10;
   ```

3. **Schema不一致**
   ```bash
   # 生成修复迁移
   python scripts/db_manage.py generate "Fix schema inconsistency"
   ```

#### 紧急恢复

如果迁移导致严重问题:

1. **立即回滚**:
   ```bash
   python scripts/db_manage.py downgrade <last_known_good_version>
   ```

2. **从备份恢复**:
   - 停止应用服务
   - 恢复数据库备份
   - 验证数据完整性
   - 重启应用服务

## API集成

### FastAPI集成

可以将迁移管理集成到FastAPI应用中:

```python
from fastapi import APIRouter
from app.core.migration_manager import get_migration_status
from app.core.schema_audit import run_schema_audit

router = APIRouter(prefix="/admin/db")

@router.get("/status")
async def database_status():
    return await get_migration_status()

@router.post("/upgrade")
async def upgrade_database_api():
    # 实现权限检查
    return await upgrade_database()

@router.get("/audit")
async def database_audit():
    return await run_schema_audit()
```

### 健康检查

在应用启动时检查数据库状态:

```python
async def startup_database_check():
    status = await get_migration_status()
    if status.get("database_needs_upgrade"):
        logger.warning("数据库需要升级")
        # 可以选择自动升级或抛出错误
```

## 扩展和定制

### 自定义审计规则

可以扩展Schema审计器:

```python
from app.core.schema_audit import DatabaseSchemaAuditor

class CustomSchemaAuditor(DatabaseSchemaAuditor):
    async def _check_custom_rules(self):
        # 实现自定义检查逻辑
        pass
```

### 添加新的监控指标

扩展性能监控:

```python
# 在database_health_metrics表中添加自定义指标
await session.execute(text("""
    INSERT INTO database_health_metrics 
    (metric_name, metric_value, metric_unit, status)
    VALUES ('custom_metric', :value, 'count', 'HEALTHY')
"""), {"value": metric_value})
```

## 结论

本迁移系统提供了完整的数据库生命周期管理功能，支持:
- ✅ 版本控制和自动迁移
- ✅ Schema完整性审计
- ✅ 性能监控和优化
- ✅ 备份和恢复支持
- ✅ 命令行和API接口
- ✅ 生产环境安全部署

通过遵循本指南，可以确保数据库的稳定性、性能和可维护性。