#!/usr/bin/env python3
"""
数据库管理脚本
提供数据库初始化、迁移、审计、备份等功能
"""

import asyncio
import argparse
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.migration_manager import (
    DatabaseMigrationManager, 
    get_migration_status, 
    upgrade_database,
    run_migration_check
)
from app.core.schema_audit import (
    run_schema_audit,
    get_schema_version_info
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.migration_manager = DatabaseMigrationManager()
    
    async def status(self) -> Dict[str, Any]:
        """显示数据库状态"""
        print("=== 数据库状态检查 ===")
        
        try:
            # 获取迁移状态
            migration_status = await get_migration_status()
            
            print(f"当前数据库版本: {migration_status.get('current_version', 'None')}")
            print(f"可用迁移数量: {migration_status.get('available_migrations_count', 0)}")
            print(f"已执行迁移数量: {migration_status.get('executed_migrations_count', 0)}")
            print(f"待执行迁移数量: {migration_status.get('pending_migrations_count', 0)}")
            print(f"需要升级: {'是' if migration_status.get('database_needs_upgrade', False) else '否'}")
            
            # 如果有待执行的迁移，显示详情
            pending = migration_status.get('pending_migrations', [])
            if pending:
                print("\n待执行的迁移:")
                for migration in pending:
                    print(f"  - {migration.get('revision', 'unknown')}: {migration.get('description', 'No description')}")
            
            # 获取Schema版本信息
            try:
                version_info = await get_schema_version_info()
                print(f"\nSchema快照哈希: {version_info.get('latest_snapshot', {}).get('hash', 'None')[:12]}...")
            except Exception as e:
                print(f"\n获取Schema信息失败: {e}")
            
            return migration_status
            
        except Exception as e:
            print(f"获取数据库状态失败: {e}")
            return {"error": str(e)}
    
    async def upgrade(self, target_revision: str = "head") -> Dict[str, Any]:
        """升级数据库"""
        print(f"=== 升级数据库到版本: {target_revision} ===")
        
        try:
            # 先检查状态
            check_result = await run_migration_check()
            if not check_result.get("needs_upgrade", False):
                print("数据库已是最新版本，无需升级")
                return {"success": True, "message": "Database is already up to date"}
            
            print(f"准备升级数据库，当前版本: {check_result.get('current_version', 'None')}")
            print(f"待执行迁移数量: {check_result.get('pending_count', 0)}")
            
            # 确认升级
            if not self._confirm_action("是否继续升级？"):
                print("升级已取消")
                return {"success": False, "message": "Upgrade cancelled by user"}
            
            # 执行升级
            result = await upgrade_database(target_revision)
            
            if result.get("success"):
                print(f"✅ 数据库升级成功")
                print(f"耗时: {result.get('duration_seconds', 0):.2f}秒")
                
                if result.get("backup_info"):
                    backup = result["backup_info"]
                    print(f"备份文件: {backup.get('backup_path', 'None')}")
                
                # 显示升级后检查结果
                post_check = result.get("post_check", {})
                if post_check:
                    print(f"Schema状态: {post_check.get('schema_status', 'Unknown')}")
                    if post_check.get("total_issues", 0) > 0:
                        print(f"⚠️ 发现 {post_check['total_issues']} 个问题，建议运行审计检查")
            else:
                print(f"❌ 数据库升级失败: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            print(f"升级过程中发生异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def downgrade(self, target_revision: str) -> Dict[str, Any]:
        """降级数据库"""
        print(f"=== 降级数据库到版本: {target_revision} ===")
        
        print("⚠️  警告: 数据库降级是危险操作，可能导致数据丢失！")
        if not self._confirm_action("确定要继续降级吗？"):
            print("降级已取消")
            return {"success": False, "message": "Downgrade cancelled by user"}
        
        try:
            result = await self.migration_manager.downgrade_database(target_revision)
            
            if result.get("success"):
                print(f"✅ 数据库降级成功")
                print(f"耗时: {result.get('duration_seconds', 0):.2f}秒")
                
                if result.get("backup_info"):
                    backup = result["backup_info"]
                    print(f"备份文件: {backup.get('backup_path', 'None')}")
                
                if result.get("warning"):
                    print(f"⚠️ {result['warning']}")
            else:
                print(f"❌ 数据库降级失败: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            print(f"降级过程中发生异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def audit(self) -> Dict[str, Any]:
        """运行数据库审计"""
        print("=== 数据库Schema审计 ===")
        
        try:
            report = await run_schema_audit()
            
            print(f"审计状态: {report.status.value}")
            print(f"数据库版本: {report.database_version}")
            print(f"Alembic版本: {report.alembic_version or 'None'}")
            print(f"总表数: {report.total_tables}")
            print(f"总索引数: {report.total_indexes}")
            print(f"数据库大小: {report.database_size_mb:.2f}MB")
            print(f"发现问题数: {len(report.issues)}")
            
            # 显示问题详情
            if report.issues:
                print("\n发现的问题:")
                for issue in report.issues:
                    severity_icon = {
                        "INFO": "ℹ️",
                        "WARNING": "⚠️",
                        "ERROR": "❌",
                        "CRITICAL": "🚨"
                    }.get(issue.severity.name, "•")
                    
                    table_info = f" [{issue.table_name}]" if issue.table_name else ""
                    print(f"  {severity_icon} {issue.issue_type}{table_info}: {issue.description}")
                    print(f"    建议: {issue.recommendation}")
            
            # 显示优化建议
            if report.recommendations:
                print("\n优化建议:")
                for i, rec in enumerate(report.recommendations, 1):
                    print(f"  {i}. {rec}")
            
            # 显示表指标
            if report.table_metrics:
                print("\n表统计:")
                print("  表名                    行数       大小(MB)  索引数  外键数")
                print("  " + "-" * 60)
                
                for metric in sorted(report.table_metrics, key=lambda x: x.size_bytes, reverse=True)[:10]:
                    size_mb = metric.size_bytes / (1024 * 1024) if metric.size_bytes else 0
                    print(f"  {metric.name:<20} {metric.row_count:>8} {size_mb:>10.2f} {metric.index_count:>6} {metric.foreign_key_count:>6}")
            
            return {
                "status": report.status.value,
                "issues_count": len(report.issues),
                "critical_issues": len([i for i in report.issues if i.severity.name == "CRITICAL"]),
                "recommendations_count": len(report.recommendations),
                "database_size_mb": report.database_size_mb,
                "audit_timestamp": report.audit_timestamp.isoformat()
            }
            
        except Exception as e:
            print(f"审计过程中发生异常: {e}")
            return {"error": str(e)}
    
    async def init(self) -> Dict[str, Any]:
        """初始化数据库"""
        print("=== 初始化数据库 ===")
        
        try:
            # 检查当前状态
            status = await get_migration_status()
            
            if status.get("current_version"):
                print(f"数据库已初始化，当前版本: {status['current_version']}")
                
                if not self._confirm_action("是否要重新初始化？这将升级到最新版本"):
                    return {"success": False, "message": "Initialization cancelled"}
            
            # 执行初始化（升级到最新版本）
            print("正在初始化数据库...")
            result = await upgrade_database("head")
            
            if result.get("success"):
                print("✅ 数据库初始化成功")
                
                # 运行初始化后审计
                print("\n正在运行初始化后检查...")
                audit_result = await self.audit()
                
                if audit_result.get("critical_issues", 0) > 0:
                    print("⚠️ 发现关键问题，建议立即修复")
                
            return result
            
        except Exception as e:
            print(f"初始化过程中发生异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def history(self) -> Dict[str, Any]:
        """显示迁移历史"""
        print("=== 迁移历史 ===")
        
        try:
            history = self.migration_manager.get_migration_history()
            
            if not history:
                print("暂无迁移历史记录")
                return {"history": []}
            
            print("迁移历史记录:")
            print("  源版本     -> 目标版本   描述")
            print("  " + "-" * 50)
            
            for item in history:
                from_rev = item.get("from_revision", "None")[:8]
                to_rev = item.get("to_revision", "None")[:8]
                desc = item.get("description", "No description")
                print(f"  {from_rev:<10} -> {to_rev:<10} {desc}")
            
            return {"history": history}
            
        except Exception as e:
            print(f"获取迁移历史失败: {e}")
            return {"error": str(e)}
    
    async def generate_migration(self, message: str) -> Dict[str, Any]:
        """生成新的迁移文件"""
        print(f"=== 生成迁移: {message} ===")
        
        try:
            result = self.migration_manager.generate_migration(message, autogenerate=True)
            
            if result.get("success"):
                print(f"✅ 迁移文件生成成功")
                print(f"文件: {result.get('migration_file', 'Unknown')}")
                print("请检查生成的迁移文件并根据需要进行调整")
            else:
                print(f"❌ 生成迁移文件失败: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            print(f"生成迁移文件时发生异常: {e}")
            return {"success": False, "error": str(e)}
    
    def _confirm_action(self, message: str) -> bool:
        """确认操作"""
        try:
            response = input(f"{message} (y/N): ").strip().lower()
            return response in ['y', 'yes']
        except KeyboardInterrupt:
            print("\n操作已取消")
            return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 状态检查命令
    subparsers.add_parser("status", help="显示数据库状态")
    
    # 初始化命令
    subparsers.add_parser("init", help="初始化数据库")
    
    # 升级命令
    upgrade_parser = subparsers.add_parser("upgrade", help="升级数据库")
    upgrade_parser.add_argument("--target", default="head", help="目标版本 (默认: head)")
    
    # 降级命令
    downgrade_parser = subparsers.add_parser("downgrade", help="降级数据库")
    downgrade_parser.add_argument("target", help="目标版本")
    
    # 审计命令
    subparsers.add_parser("audit", help="运行数据库审计")
    
    # 历史命令
    subparsers.add_parser("history", help="显示迁移历史")
    
    # 生成迁移命令
    generate_parser = subparsers.add_parser("generate", help="生成新的迁移文件")
    generate_parser.add_argument("message", help="迁移描述")
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    
    try:
        # 执行命令
        if args.command == "status":
            result = await db_manager.status()
        elif args.command == "init":
            result = await db_manager.init()
        elif args.command == "upgrade":
            result = await db_manager.upgrade(args.target)
        elif args.command == "downgrade":
            result = await db_manager.downgrade(args.target)
        elif args.command == "audit":
            result = await db_manager.audit()
        elif args.command == "history":
            result = await db_manager.history()
        elif args.command == "generate":
            result = await db_manager.generate_migration(args.message)
        else:
            print(f"未知命令: {args.command}")
            sys.exit(1)
        
        # 输出JSON结果（可选）
        if "--json" in sys.argv:
            print("\n" + "="*50)
            print("JSON输出:")
            print(json.dumps(result, indent=2, default=str))
        
        # 根据结果设置退出码
        if isinstance(result, dict):
            if result.get("success", True) and not result.get("error"):
                sys.exit(0)
            else:
                sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(130)
    except Exception as e:
        logger.exception("执行命令时发生未处理异常")
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())