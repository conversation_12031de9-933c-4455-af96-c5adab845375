version: '3.8'

# 量化投资平台 - Linux 容器优化配置
# 适用于 Linux 生产环境，包含性能优化和安全加固

services:
  # 后端服务 - 优化版
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile.prod
      args:
        BUILD_ENV: production
        PYTHON_VERSION: 3.9-slim
    image: quant-platform/backend:latest
    container_name: quant-backend
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # 环境变量
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CORS_ORIGINS=https://yourdomain.com
      - LOG_LEVEL=INFO
      - WORKERS=4
      - MAX_CONNECTIONS=100
    
    # 端口映射
    ports:
      - "8000:8000"
    
    # 依赖服务
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    # 网络
    networks:
      - quant-internal
      - quant-external
    
    # 数据卷
    volumes:
      - ../logs:/app/logs:rw
      - ../data:/app/data:rw
      - /etc/localtime:/etc/localtime:ro
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false  # 需要写入日志
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    
    # 用户配置
    user: "${UID:-1000}:${GID:-1000}"
    
    # 标签
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.yourdomain.com`)"
      - "traefik.http.routers.backend.tls=true"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"

  # 前端服务 - Nginx 优化版
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile.prod
      args:
        BUILD_ENV: production
        NODE_VERSION: 18-alpine
    image: quant-platform/frontend:latest
    container_name: quant-frontend
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # 环境变量
    environment:
      - NODE_ENV=production
      - NGINX_HOST=yourdomain.com
      - NGINX_PORT=80
      - API_URL=https://api.yourdomain.com
    
    # 端口映射
    ports:
      - "80:80"
      - "443:443"
    
    # 依赖服务
    depends_on:
      - backend
    
    # 网络
    networks:
      - quant-external
    
    # 数据卷
    volumes:
      - ../config/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ../config/nginx/ssl:/etc/nginx/ssl:ro
      - ../logs/nginx:/var/log/nginx:rw
      - /etc/localtime:/etc/localtime:ro
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /var/cache/nginx:noexec,nosuid,size=50m
      - /var/run:noexec,nosuid,size=10m
    
    # 标签
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`yourdomain.com`)"
      - "traefik.http.routers.frontend.tls=true"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: quant-postgres
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    
    # 环境变量
    environment:
      - POSTGRES_DB=quant_db
      - POSTGRES_USER=quant_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256 --auth-local=scram-sha-256
      - PGDATA=/var/lib/postgresql/data/pgdata
    
    # 端口映射（仅内部访问）
    expose:
      - "5432"
    
    # 网络
    networks:
      - quant-internal
    
    # 数据卷
    volumes:
      - postgres_data:/var/lib/postgresql/data:rw
      - ../config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ../config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ../backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - /etc/localtime:/etc/localtime:ro
    
    # 启动命令
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c hba_file=/etc/postgresql/pg_hba.conf
      -c max_connections=200
      -c shared_buffers=1GB
      -c effective_cache_size=3GB
      -c maintenance_work_mem=256MB
      -c checkpoint_completion_target=0.7
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quant_user -d quant_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 标签
    labels:
      - "backup.enable=true"
      - "backup.schedule=0 2 * * *"

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: quant-redis
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    
    # 端口映射（仅内部访问）
    expose:
      - "6379"
    
    # 网络
    networks:
      - quant-internal
    
    # 数据卷
    volumes:
      - redis_data:/data:rw
      - ../config/redis/redis.conf:/etc/redis/redis.conf:ro
      - /etc/localtime:/etc/localtime:ro
    
    # 启动命令
    command: >
      redis-server /etc/redis/redis.conf
      --maxmemory 768mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    
    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=50m

  # Nginx 负载均衡器（可选）
  nginx-lb:
    image: nginx:alpine
    container_name: quant-nginx-lb
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # 端口映射
    ports:
      - "80:80"
      - "443:443"
    
    # 依赖服务
    depends_on:
      - frontend
      - backend
    
    # 网络
    networks:
      - quant-external
      - quant-internal
    
    # 数据卷
    volumes:
      - ../config/nginx/nginx.lb.conf:/etc/nginx/nginx.conf:ro
      - ../config/nginx/ssl:/etc/nginx/ssl:ro
      - ../logs/nginx-lb:/var/log/nginx:rw
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /var/cache/nginx:noexec,nosuid,size=100m
      - /var/run:noexec,nosuid,size=10m
    
    profiles:
      - load-balancer

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: quant-prometheus
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    
    # 端口映射
    ports:
      - "9090:9090"
    
    # 网络
    networks:
      - quant-internal
      - monitoring
    
    # 数据卷
    volumes:
      - prometheus_data:/prometheus:rw
      - ../config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ../config/prometheus/rules:/etc/prometheus/rules:ro
    
    # 启动命令
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    profiles:
      - monitoring

  # Grafana 仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: quant-grafana
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    
    # 端口映射
    ports:
      - "3001:3000"
    
    # 环境变量
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    
    # 依赖服务
    depends_on:
      - prometheus
    
    # 网络
    networks:
      - monitoring
      - quant-external
    
    # 数据卷
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - ../config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ../config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    profiles:
      - monitoring

# 网络定义
networks:
  quant-internal:
    driver: bridge
    internal: true  # 内部网络，不能访问外网
    ipam:
      config:
        - subnet: **********/24

  quant-external:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24

  monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24

# 数据卷定义
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres

  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/redis

  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/prometheus

  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/grafana

# 配置
x-common-variables: &common-variables
  TZ: Asia/Shanghai
  
x-logging: &default-logging
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"