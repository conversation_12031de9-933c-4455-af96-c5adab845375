<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 专业版</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .main-nav {
            background: white;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .nav-menu {
            display: flex;
            gap: 0;
            list-style: none;
        }
        
        .nav-item {
            position: relative;
        }
        
        .nav-link {
            display: block;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .content {
            flex: 1;
            padding: 30px 20px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-content {
            color: #666;
            line-height: 1.6;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #999;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #52c41a;
            box-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
        }
        
        .status-warning {
            background: #faad14;
            box-shadow: 0 0 8px rgba(250, 173, 20, 0.5);
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .nav-menu {
                flex-direction: column;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 头部 -->
            <header class="header">
                <h1>🚀 量化投资平台</h1>
                <p class="subtitle">专业级量化交易系统 • 实时数据分析 • 智能策略管理</p>
            </header>
            
            <!-- 主导航 -->
            <nav class="main-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active" @click="setActiveTab('dashboard')" data-testid="nav-dashboard">
                            📊 仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#market" class="nav-link" @click="setActiveTab('market')" data-testid="nav-market">
                            📈 市场行情
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#trading" class="nav-link" @click="setActiveTab('trading')" data-testid="nav-trading">
                            💹 交易中心
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#portfolio" class="nav-link" @click="setActiveTab('portfolio')" data-testid="nav-portfolio">
                            💼 投资组合
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#strategy" class="nav-link" @click="setActiveTab('strategy')" data-testid="nav-strategy">
                            🧠 策略中心
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#nav-test" class="nav-link" @click="setActiveTab('nav-test')" data-testid="nav-test">
                            🧪 导航测试
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- 主内容区 -->
            <main class="content">
                <!-- 仪表盘页面 -->
                <div id="dashboard" class="page-content active">
                    <div class="dashboard-grid">
                        <div class="card">
                            <div class="card-title">
                                <span class="status-indicator status-online"></span>
                                系统状态
                            </div>
                            <div class="card-content">
                                <div class="metric-value">99.9%</div>
                                <div class="metric-label">系统可用性</div>
                                <p>所有核心服务运行正常</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">
                                📊 今日交易
                            </div>
                            <div class="card-content">
                                <div class="metric-value">¥2,847,392</div>
                                <div class="metric-label">交易总额</div>
                                <p>较昨日增长 +12.5%</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">
                                🎯 策略表现
                            </div>
                            <div class="card-content">
                                <div class="metric-value">+8.7%</div>
                                <div class="metric-label">今日收益率</div>
                                <p>15个策略正在运行</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">
                                ⚡ API性能
                            </div>
                            <div class="card-content">
                                <div class="metric-value">145ms</div>
                                <div class="metric-label">平均响应时间</div>
                                <p>24小时内处理 847,392 次请求</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-title">🚀 快速操作</div>
                        <div class="card-content">
                            <button class="btn" @click="testAPI('market')">获取市场数据</button>
                            <button class="btn" @click="testAPI('trading')">模拟交易</button>
                            <button class="btn" @click="testAPI('strategy')">运行策略</button>
                            <button class="btn" @click="testAPI('portfolio')">查看组合</button>
                        </div>
                    </div>
                </div>
                
                <!-- 其他页面内容 -->
                <div id="market" class="page-content">
                    <div class="card">
                        <div class="card-title">📈 市场行情</div>
                        <div class="card-content">
                            <p>实时股票数据、K线图表、技术指标分析</p>
                            <button class="btn" @click="loadMarketData()">加载市场数据</button>
                        </div>
                    </div>
                </div>
                
                <div id="trading" class="page-content">
                    <div class="card">
                        <div class="card-title">💹 交易中心</div>
                        <div class="card-content">
                            <p>股票买卖、订单管理、风险控制</p>
                            <button class="btn" @click="openTradingTerminal()">打开交易终端</button>
                        </div>
                    </div>
                </div>
                
                <div id="portfolio" class="page-content">
                    <div class="card">
                        <div class="card-title">💼 投资组合</div>
                        <div class="card-content">
                            <p>持仓分析、收益统计、风险评估</p>
                            <button class="btn" @click="analyzePortfolio()">分析组合</button>
                        </div>
                    </div>
                </div>
                
                <div id="strategy" class="page-content">
                    <div class="card">
                        <div class="card-title">🧠 策略中心</div>
                        <div class="card-content">
                            <p>量化策略开发、回测分析、参数优化</p>
                            <button class="btn" @click="createStrategy()">创建策略</button>
                        </div>
                    </div>
                </div>
                
                <div id="nav-test" class="page-content">
                    <div class="card">
                        <div class="card-title">🧪 导航测试</div>
                        <div class="card-content">
                            <p>导航功能测试页面，验证所有链接正常工作</p>
                            <button class="btn" @click="runNavigationTest()">运行测试</button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    activeTab: 'dashboard'
                }
            },
            methods: {
                setActiveTab(tab) {
                    // 更新活动标签
                    this.activeTab = tab;
                    
                    // 更新导航样式
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    document.querySelector(`[data-testid="nav-${tab}"]`).classList.add('active');
                    
                    // 显示对应页面
                    document.querySelectorAll('.page-content').forEach(page => {
                        page.classList.remove('active');
                    });
                    document.getElementById(tab).classList.add('active');
                    
                    console.log(`🧭 导航到: ${tab}`);
                },
                
                async testAPI(type) {
                    console.log(`🔧 测试API: ${type}`);
                    
                    const endpoints = {
                        market: 'http://localhost:8000/api/v1/market/stocks',
                        trading: 'http://localhost:8000/api/v1/trading/orders',
                        strategy: 'http://localhost:8000/api/v1/strategy/list',
                        portfolio: 'http://localhost:8000/api/v1/portfolio/summary'
                    };
                    
                    try {
                        const response = await fetch(endpoints[type] || 'http://localhost:8000/health');
                        const data = await response.json();
                        console.log(`✅ ${type} API响应:`, data);
                        alert(`${type} API测试成功！查看控制台获取详细信息。`);
                    } catch (error) {
                        console.error(`❌ ${type} API错误:`, error);
                        alert(`${type} API测试失败: ${error.message}`);
                    }
                },
                
                loadMarketData() {
                    console.log('📊 加载市场数据...');
                    this.testAPI('market');
                },
                
                openTradingTerminal() {
                    console.log('💹 打开交易终端...');
                    this.testAPI('trading');
                },
                
                analyzePortfolio() {
                    console.log('💼 分析投资组合...');
                    this.testAPI('portfolio');
                },
                
                createStrategy() {
                    console.log('🧠 创建量化策略...');
                    this.testAPI('strategy');
                },
                
                runNavigationTest() {
                    console.log('🧪 运行导航测试...');
                    const tabs = ['dashboard', 'market', 'trading', 'portfolio', 'strategy'];
                    let index = 0;
                    
                    const testInterval = setInterval(() => {
                        if (index < tabs.length) {
                            this.setActiveTab(tabs[index]);
                            index++;
                        } else {
                            clearInterval(testInterval);
                            this.setActiveTab('nav-test');
                            alert('导航测试完成！所有页面都可以正常访问。');
                        }
                    }, 1000);
                }
            },
            
            mounted() {
                console.log('🚀 量化投资平台已加载');
                console.log('📍 当前页面: 专业Vue应用');
                console.log('🔧 API地址: http://localhost:8000');
            }
        }).mount('#app');
    </script>
</body>
</html>
