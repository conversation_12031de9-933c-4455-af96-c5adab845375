# Consolidated Secrets Configuration
# All sensitive configuration in one place with proper labeling

apiVersion: v1
kind: Secret
metadata:
  name: quant-platform-secrets
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform
    app.kubernetes.io/component: secrets
type: Opaque
stringData:
  # === Database Credentials ===
  DATABASE_PASSWORD: "REPLACE-WITH-SECURE-PASSWORD"
  DATABASE_URL: "*********************************************************************************/quant_db"
  
  # === Redis Credentials ===
  REDIS_PASSWORD: "REPLACE-WITH-SECURE-PASSWORD"
  REDIS_URL: "redis://:REPLACE-WITH-SECURE-PASSWORD@quant-platform-redis:6379/0"
  
  # === Application Security Keys ===
  SECRET_KEY: "REPLACE-WITH-SECURE-SECRET-KEY-MINIMUM-32-CHARACTERS"
  JWT_SECRET_KEY: "REPLACE-WITH-SECURE-JWT-SECRET-KEY-MINIMUM-32-CHARACTERS"
  
  # === Celery URLs with Authentication ===
  CELERY_BROKER_URL: "redis://:REPLACE-WITH-SECURE-PASSWORD@quant-platform-redis:6379/1"
  CELERY_RESULT_BACKEND: "redis://:REPLACE-WITH-SECURE-PASSWORD@quant-platform-redis:6379/2"
  
  # === External API Keys ===
  MARKET_DATA_API_KEY: "REPLACE-WITH-YOUR-MARKET-DATA-API-KEY"
  TUSHARE_TOKEN: "REPLACE-WITH-YOUR-TUSHARE-TOKEN"
  ALPHA_VANTAGE_API_KEY: "REPLACE-WITH-YOUR-ALPHA-VANTAGE-KEY"
  
  # === Email/SMTP Credentials ===
  SMTP_USERNAME: "REPLACE-WITH-SMTP-USERNAME"
  SMTP_PASSWORD: "REPLACE-WITH-SMTP-PASSWORD"
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  
  # === Monitoring Credentials ===
  GRAFANA_ADMIN_PASSWORD: "REPLACE-WITH-SECURE-GRAFANA-PASSWORD"
  PROMETHEUS_AUTH_TOKEN: "REPLACE-WITH-PROMETHEUS-TOKEN"
  
  # === Cloud Storage Credentials (if used) ===
  AWS_ACCESS_KEY_ID: "REPLACE-WITH-AWS-ACCESS-KEY"
  AWS_SECRET_ACCESS_KEY: "REPLACE-WITH-AWS-SECRET-KEY"
  AWS_REGION: "us-east-1"
  S3_BUCKET_NAME: "quant-platform-data"
  
  # === Third-party Service Keys ===
  SLACK_WEBHOOK_URL: "REPLACE-WITH-SLACK-WEBHOOK-URL"
  TELEGRAM_BOT_TOKEN: "REPLACE-WITH-TELEGRAM-BOT-TOKEN"
  DISCORD_WEBHOOK_URL: "REPLACE-WITH-DISCORD-WEBHOOK-URL"
---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: quant-platform-tls
  namespace: quant-platform
  labels:
    app.kubernetes.io/name: quant-platform
    app.kubernetes.io/component: tls
type: kubernetes.io/tls
data:
  # Base64 encoded certificate and key
  # Generate with: kubectl create secret tls quant-platform-tls --cert=path/to/cert.pem --key=path/to/key.pem --dry-run=client -o yaml
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t  # REPLACE WITH ACTUAL CERT
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t  # REPLACE WITH ACTUAL KEY