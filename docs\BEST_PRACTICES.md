# 最佳实践指南

## 📋 目录

- [代码质量](#代码质量)
- [性能优化](#性能优化)
- [安全实践](#安全实践)
- [测试策略](#测试策略)
- [部署运维](#部署运维)
- [团队协作](#团队协作)

## 💎 代码质量

### 前端最佳实践

#### 1. 组件设计原则

**单一职责原则**
```vue
<!-- ✅ 正确：专注于用户信息显示 -->
<template>
  <div class="user-info">
    <img :src="user.avatar" :alt="user.name" />
    <h3>{{ user.name }}</h3>
    <p>{{ user.email }}</p>
  </div>
</template>

<!-- ❌ 错误：混合了多个职责 -->
<template>
  <div class="user-dashboard">
    <!-- 用户信息 -->
    <div class="user-info">...</div>
    <!-- 交易面板 -->
    <div class="trading-panel">...</div>
    <!-- 图表显示 -->
    <div class="charts">...</div>
  </div>
</template>
```

**Props验证**
```typescript
interface Props {
  userId: string
  showActions?: boolean
  maxItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  maxItems: 10
})

// 运行时验证
const props = defineProps({
  userId: {
    type: String,
    required: true,
    validator: (value: string) => value.length > 0
  },
  maxItems: {
    type: Number,
    default: 10,
    validator: (value: number) => value > 0 && value <= 100
  }
})
```

#### 2. 状态管理最佳实践

**Pinia Store设计**
```typescript
// ✅ 正确：模块化的store设计
export const useUserStore = defineStore('user', () => {
  // State
  const currentUser = ref<User | null>(null)
  const isLoading = ref(false)
  
  // Getters
  const isAuthenticated = computed(() => !!currentUser.value)
  const userDisplayName = computed(() => 
    currentUser.value?.name || '未登录用户'
  )
  
  // Actions
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    try {
      const user = await authApi.login(credentials)
      currentUser.value = user
      return user
    } catch (error) {
      throw new Error('登录失败')
    } finally {
      isLoading.value = false
    }
  }
  
  const logout = () => {
    currentUser.value = null
    // 清理相关状态
  }
  
  return {
    // State
    currentUser: readonly(currentUser),
    isLoading: readonly(isLoading),
    // Getters
    isAuthenticated,
    userDisplayName,
    // Actions
    login,
    logout
  }
})
```

#### 3. API调用最佳实践

**统一的API客户端**
```typescript
// api/client.ts
class ApiClient {
  private baseURL: string
  private timeout: number
  
  constructor(config: ApiConfig) {
    this.baseURL = config.baseURL
    this.timeout = config.timeout || 10000
  }
  
  async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    
    try {
      const response = await fetch(`${this.baseURL}${config.url}`, {
        method: config.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        body: config.data ? JSON.stringify(config.data) : undefined,
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new ApiError(response.status, response.statusText)
      }
      
      return await response.json()
    } catch (error) {
      clearTimeout(timeoutId)
      throw this.handleError(error)
    }
  }
  
  private handleError(error: unknown): ApiError {
    if (error instanceof ApiError) return error
    if (error instanceof DOMException && error.name === 'AbortError') {
      return new ApiError(408, '请求超时')
    }
    return new ApiError(500, '网络错误')
  }
}
```

### 后端最佳实践

#### 1. API设计原则

**RESTful API设计**
```python
# ✅ 正确：符合REST规范的API设计
@router.get("/users", response_model=List[UserResponse])
async def get_users(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表"""
    return await user_service.get_users(db, page, size, search)

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str = Path(..., description="用户ID"),
    db: AsyncSession = Depends(get_db)
):
    """获取单个用户信息"""
    user = await user_service.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(404, "用户不存在")
    return user
```

**数据验证和序列化**
```python
from pydantic import BaseModel, Field, validator

class UserCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=8, description="密码")
    
    @validator('password')
    def validate_password(cls, v):
        if not re.match(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)', v):
            raise ValueError('密码必须包含大小写字母和数字')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "name": "张三",
                "email": "<EMAIL>",
                "password": "Password123"
            }
        }
```

#### 2. 数据库操作最佳实践

**Repository模式**
```python
class UserRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, user_data: UserCreate) -> User:
        db_user = User(**user_data.dict())
        self.db.add(db_user)
        await self.db.commit()
        await self.db.refresh(db_user)
        return db_user
    
    async def get_by_id(self, user_id: str) -> Optional[User]:
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_email(self, email: str) -> Optional[User]:
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def update(self, user_id: str, update_data: dict) -> Optional[User]:
        result = await self.db.execute(
            update(User)
            .where(User.id == user_id)
            .values(**update_data)
            .returning(User)
        )
        await self.db.commit()
        return result.scalar_one_or_none()
```

## ⚡ 性能优化

### 前端性能优化

#### 1. 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  {
    path: '/trading',
    component: () => import('@/views/Trading.vue')
  }
]

// 组件懒加载
const LazyChart = defineAsyncComponent({
  loader: () => import('@/components/charts/TradingChart.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

#### 2. 虚拟滚动
```vue
<template>
  <VirtualList
    :items="largeDataSet"
    :item-height="50"
    :container-height="400"
    v-slot="{ item, index }"
  >
    <div class="list-item">
      {{ item.name }} - {{ item.value }}
    </div>
  </VirtualList>
</template>
```

#### 3. 防抖和节流
```typescript
import { debounce, throttle } from 'lodash-es'

// 搜索防抖
const debouncedSearch = debounce(async (query: string) => {
  const results = await searchApi.search(query)
  searchResults.value = results
}, 300)

// 滚动节流
const throttledScroll = throttle((event: Event) => {
  const scrollTop = (event.target as Element).scrollTop
  handleScroll(scrollTop)
}, 100)
```

### 后端性能优化

#### 1. 数据库查询优化
```python
# ✅ 正确：使用索引和预加载
async def get_user_with_orders(db: AsyncSession, user_id: str):
    result = await db.execute(
        select(User)
        .options(selectinload(User.orders))  # 预加载关联数据
        .where(User.id == user_id)
    )
    return result.scalar_one_or_none()

# ✅ 正确：分页查询
async def get_orders_paginated(
    db: AsyncSession, 
    page: int, 
    size: int
) -> Tuple[List[Order], int]:
    # 获取总数
    count_result = await db.execute(
        select(func.count(Order.id))
    )
    total = count_result.scalar()
    
    # 获取分页数据
    result = await db.execute(
        select(Order)
        .order_by(Order.created_at.desc())
        .offset((page - 1) * size)
        .limit(size)
    )
    orders = result.scalars().all()
    
    return orders, total
```

#### 2. 缓存策略
```python
from functools import lru_cache
import redis

# 内存缓存
@lru_cache(maxsize=128)
def get_market_config(market: str) -> MarketConfig:
    return MarketConfig.load_from_db(market)

# Redis缓存
class CacheService:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    async def get_or_set(
        self, 
        key: str, 
        factory: Callable, 
        ttl: int = 3600
    ):
        # 尝试从缓存获取
        cached = await self.redis.get(key)
        if cached:
            return json.loads(cached)
        
        # 缓存未命中，调用工厂函数
        data = await factory()
        await self.redis.setex(
            key, 
            ttl, 
            json.dumps(data, default=str)
        )
        return data
```

## 🔒 安全实践

### 1. 输入验证
```python
from pydantic import validator
import re

class OrderCreate(BaseModel):
    symbol: str = Field(..., regex=r'^[A-Z0-9]{6,10}$')
    quantity: int = Field(..., gt=0, le=10000)
    price: Decimal = Field(..., gt=0, decimal_places=2)
    
    @validator('symbol')
    def validate_symbol(cls, v):
        # 额外的业务验证
        if not is_valid_stock_symbol(v):
            raise ValueError('无效的股票代码')
        return v.upper()
```

### 2. 认证和授权
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def get_current_user(
    token: str = Depends(security)
) -> User:
    try:
        payload = jwt.decode(token.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据"
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据"
        )
    
    user = await get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )
    return user

def require_permission(permission: str):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user.has_permission(permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

### 3. 数据加密
```python
from cryptography.fernet import Fernet
import bcrypt

class SecurityService:
    def __init__(self, encryption_key: bytes):
        self.cipher = Fernet(encryption_key)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
    
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode(), salt).decode()
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode(), hashed.encode())
```

## 🧪 测试策略

### 1. 单元测试
```typescript
// 前端单元测试
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('should emit update event when user data changes', async () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: { id: '1', name: '张三' }
      }
    })
    
    await wrapper.find('input[name="name"]').setValue('李四')
    await wrapper.find('form').trigger('submit')
    
    expect(wrapper.emitted('update')).toBeTruthy()
    expect(wrapper.emitted('update')[0]).toEqual([
      { id: '1', name: '李四' }
    ])
  })
})
```

```python
# 后端单元测试
import pytest
from unittest.mock import AsyncMock
from app.services.user_service import UserService

@pytest.mark.asyncio
async def test_create_user_success():
    # Arrange
    mock_repo = AsyncMock()
    mock_repo.get_by_email.return_value = None
    mock_repo.create.return_value = User(
        id="123",
        name="张三",
        email="<EMAIL>"
    )
    
    service = UserService(mock_repo)
    user_data = UserCreate(
        name="张三",
        email="<EMAIL>",
        password="password123"
    )
    
    # Act
    result = await service.create_user(user_data)
    
    # Assert
    assert result.name == "张三"
    assert result.email == "<EMAIL>"
    mock_repo.create.assert_called_once()
```

### 2. 集成测试
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_user_registration_flow():
    # 注册用户
    response = client.post("/api/v1/auth/register", json={
        "name": "张三",
        "email": "<EMAIL>",
        "password": "Password123"
    })
    assert response.status_code == 201
    
    # 登录
    response = client.post("/api/v1/auth/login", json={
        "username": "<EMAIL>",
        "password": "Password123"
    })
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    # 访问受保护资源
    response = client.get(
        "/api/v1/users/me",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    assert response.json()["email"] == "<EMAIL>"
```

## 🚀 部署运维

### 1. 环境配置
```python
# config/settings.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    # 数据库配置
    database_url: str
    database_pool_size: int = 10
    
    # Redis配置
    redis_url: str
    redis_max_connections: int = 20
    
    # 安全配置
    secret_key: str
    access_token_expire_minutes: int = 30
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "json"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

settings = Settings()
```

### 2. 健康检查
```python
@app.get("/health")
async def health_check():
    """健康检查端点"""
    checks = {
        "database": await check_database_connection(),
        "redis": await check_redis_connection(),
        "external_api": await check_external_api()
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return JSONResponse(
        status_code=status_code,
        content={
            "status": "healthy" if all_healthy else "unhealthy",
            "checks": checks,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### 3. 监控和日志
```python
import structlog
from prometheus_client import Counter, Histogram

# 指标收集
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

# 结构化日志
logger = structlog.get_logger()

@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    # 记录指标
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path
    ).inc()
    REQUEST_DURATION.observe(duration)
    
    # 记录日志
    logger.info(
        "HTTP request completed",
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        duration=duration
    )
    
    return response
```

---

这些最佳实践应该根据项目的具体需求和团队情况进行调整和扩展。
