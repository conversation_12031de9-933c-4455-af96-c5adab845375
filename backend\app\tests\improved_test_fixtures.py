"""
改进的测试夹具
解决单元测试中的假阳性问题，提供更严格和现实的测试数据
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Any, Optional
import asyncio
from datetime import datetime, timedelta
import random
import uuid

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.core.database import Base, get_db
from app.main import app
from app.db.models.user import User
from app.db.models.strategy import Strategy, StrategyStatus, StrategyType
from app.db.models.trading import Order, OrderStatus, OrderType
from app.core.test_helpers import StrictTestValidator, MockDataValidator


class DatabaseTestHelper:
    """数据库测试辅助类"""
    
    @staticmethod
    async def create_test_database():
        """创建测试数据库"""
        # 使用内存SQLite进行测试
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            echo=False
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        return async_session, engine
    
    @staticmethod
    async def populate_test_data(session: AsyncSession):
        """填充测试数据"""
        # 创建测试用户
        test_user = User(
            id=1,
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password_123",
            full_name="Test User",
            is_active=True,
            is_verified=True,
            created_at=datetime.utcnow()
        )
        session.add(test_user)
        
        # 创建测试策略
        test_strategy = Strategy(
            id=uuid.uuid4(),
            user_id=1,
            name="测试双均线策略",
            description="用于测试的双均线策略",
            strategy_type=StrategyType.TECHNICAL,
            code="""
def initialize(context):
    context.short_ma = 5
    context.long_ma = 20

def handle_data(context, data):
    if context.short_ma > context.long_ma:
        context.order('buy', 1000)
    else:
        context.order('sell', 1000)
""",
            parameters={"short_period": 5, "long_period": 20},
            status=StrategyStatus.ACTIVE,
            is_active=True,
            created_at=datetime.utcnow()
        )
        session.add(test_strategy)
        
        # 创建测试订单
        test_order = Order(
            id=uuid.uuid4(),
            user_id=1,
            strategy_id=test_strategy.id,
            symbol="000001.SZ",
            order_type=OrderType.MARKET,
            side="buy",
            quantity=1000,
            price=12.50,
            status=OrderStatus.FILLED,
            created_at=datetime.utcnow()
        )
        session.add(test_order)
        
        await session.commit()
        return test_user, test_strategy, test_order


@pytest_asyncio.fixture
async def test_db():
    """测试数据库夹具"""
    async_session, engine = await DatabaseTestHelper.create_test_database()
    
    # 创建会话
    async with async_session() as session:
        # 填充测试数据
        test_user, test_strategy, test_order = await DatabaseTestHelper.populate_test_data(session)
        
        yield session
    
    await engine.dispose()


@pytest_asyncio.fixture
async def test_client(test_db):
    """测试客户端夹具"""
    def get_test_db():
        return test_db
    
    app.dependency_overrides[get_db] = get_test_db
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
def mock_market_data():
    """模拟市场数据夹具"""
    return MockDataValidator.create_realistic_market_data()


@pytest.fixture
def mock_user_data():
    """模拟用户数据夹具"""
    return MockDataValidator.create_realistic_user_data()


@pytest.fixture
def mock_strategy_data():
    """模拟策略数据夹具"""
    return MockDataValidator.create_realistic_strategy_data()


@pytest.fixture
def time_series_data():
    """时间序列数据夹具"""
    from app.core.test_helpers import TestDataGenerator
    return TestDataGenerator.generate_time_series_data(length=100)


@pytest.fixture
def market_portfolio():
    """市场组合数据夹具"""
    from app.core.test_helpers import TestDataGenerator
    return TestDataGenerator.generate_market_portfolio(num_stocks=20)


class MockServiceProvider:
    """模拟服务提供者"""
    
    @staticmethod
    def create_mock_market_service():
        """创建模拟市场服务"""
        mock_service = AsyncMock()
        
        # 配置真实的返回值
        mock_service.get_real_time_quote.return_value = {
            "symbol": "000001.SZ",
            "price": 12.35,
            "change": 0.15,
            "change_percent": 1.23,
            "volume": 12345678,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        mock_service.get_historical_data.return_value = [
            {
                "date": "2025-01-01",
                "open": 12.00,
                "high": 12.50,
                "low": 11.90,
                "close": 12.35,
                "volume": 10000000
            },
            {
                "date": "2025-01-02", 
                "open": 12.35,
                "high": 12.60,
                "low": 12.20,
                "close": 12.45,
                "volume": 11000000
            }
        ]
        
        mock_service.search_stocks.return_value = [
            {"symbol": "000001.SZ", "name": "平安银行", "market": "深圳"},
            {"symbol": "000002.SZ", "name": "万科A", "market": "深圳"}
        ]
        
        return mock_service
    
    @staticmethod
    def create_mock_strategy_service():
        """创建模拟策略服务"""
        mock_service = AsyncMock()
        
        # 创建真实的策略对象
        test_strategy = Strategy(
            id=uuid.uuid4(),
            user_id=1,
            name="测试策略",
            description="用于测试的策略",
            strategy_type=StrategyType.TECHNICAL,
            status=StrategyStatus.ACTIVE,
            code="# 测试策略代码",
            parameters={"param1": "value1"},
            created_at=datetime.utcnow()
        )
        
        mock_service.create_strategy.return_value = test_strategy
        mock_service.get_strategy_by_id.return_value = test_strategy
        mock_service.get_user_strategies.return_value = ([test_strategy], 1)
        mock_service.update_strategy.return_value = test_strategy
        mock_service.delete_strategy.return_value = True
        mock_service.start_strategy.return_value = True
        mock_service.stop_strategy.return_value = True
        
        return mock_service
    
    @staticmethod
    def create_mock_trading_service():
        """创建模拟交易服务"""
        mock_service = AsyncMock()
        
        # 创建真实的订单对象
        test_order = Order(
            id=uuid.uuid4(),
            user_id=1,
            symbol="000001.SZ",
            order_type=OrderType.MARKET,
            side="buy",
            quantity=1000,
            price=12.50,
            status=OrderStatus.SUBMITTED,
            created_at=datetime.utcnow()
        )
        
        mock_service.submit_order.return_value = test_order
        mock_service.get_order_by_id.return_value = test_order
        mock_service.get_user_orders.return_value = ([test_order], 1)
        mock_service.cancel_order.return_value = True
        
        # 模拟账户信息
        mock_service.get_account_info.return_value = {
            "user_id": 1,
            "total_assets": 1000000.00,
            "available_cash": 500000.00,
            "market_value": 500000.00,
            "total_profit": 50000.00,
            "profit_rate": 0.05
        }
        
        return mock_service


@pytest.fixture
def mock_market_service():
    """模拟市场服务夹具"""
    return MockServiceProvider.create_mock_market_service()


@pytest.fixture
def mock_strategy_service():
    """模拟策略服务夹具"""
    return MockServiceProvider.create_mock_strategy_service()


@pytest.fixture
def mock_trading_service():
    """模拟交易服务夹具"""
    return MockServiceProvider.create_mock_trading_service()


class AssertionHelpers:
    """断言辅助工具"""
    
    @staticmethod
    def assert_response_structure(response_data: Dict, expected_keys: List[str]):
        """验证响应结构"""
        assert isinstance(response_data, dict), f"Response should be dict, got {type(response_data)}"
        
        for key in expected_keys:
            assert key in response_data, f"Expected key '{key}' not found in response"
            
        # 验证没有占位符值
        placeholder_values = [None, "", "TODO", "NOT_IMPLEMENTED"]
        for key, value in response_data.items():
            if key in expected_keys:
                assert value not in placeholder_values, f"Key '{key}' has placeholder value: {value}"
    
    @staticmethod
    def assert_database_record(record: Any, expected_fields: Dict[str, Any]):
        """验证数据库记录"""
        assert record is not None, "Database record should not be None"
        
        for field, expected_value in expected_fields.items():
            actual_value = getattr(record, field, None)
            assert actual_value == expected_value, f"Field {field}: expected {expected_value}, got {actual_value}"
    
    @staticmethod
    def assert_list_response(response_data: Dict, min_items: int = 0):
        """验证列表响应"""
        assert "items" in response_data or "data" in response_data, "List response should have 'items' or 'data' key"
        
        items = response_data.get("items", response_data.get("data", []))
        assert isinstance(items, list), f"Items should be list, got {type(items)}"
        assert len(items) >= min_items, f"Expected at least {min_items} items, got {len(items)}"
        
        # 验证分页信息
        if "total" in response_data:
            assert isinstance(response_data["total"], int), "Total should be integer"
            assert response_data["total"] >= len(items), "Total should be >= items count"


@pytest.fixture
def assertion_helpers():
    """断言辅助工具夹具"""
    return AssertionHelpers()


class PerformanceTestMixin:
    """性能测试混入类"""
    
    @staticmethod
    async def measure_response_time(client: AsyncClient, endpoint: str, method: str = "GET", max_time: float = 1.0):
        """测量响应时间"""
        start_time = datetime.utcnow()
        
        if method.upper() == "GET":
            response = await client.get(endpoint)
        elif method.upper() == "POST":
            response = await client.post(endpoint, json={})
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds()
        
        assert response_time <= max_time, f"Response time {response_time:.3f}s exceeded limit {max_time}s"
        return response, response_time


@pytest.fixture
def performance_tester():
    """性能测试器夹具"""
    return PerformanceTestMixin()


# 自定义测试装饰器
def strict_api_test(expected_status: int = 200, expected_keys: List[str] = None, max_time: float = 2.0):
    """严格API测试装饰器"""
    def decorator(test_func):
        async def wrapper(*args, **kwargs):
            # 执行测试并测量时间
            start_time = datetime.utcnow()
            result = await test_func(*args, **kwargs)
            end_time = datetime.utcnow()
            
            # 验证执行时间
            execution_time = (end_time - start_time).total_seconds()
            assert execution_time <= max_time, f"Test execution time {execution_time:.3f}s exceeded limit {max_time}s"
            
            # 验证结果结构（如果result是HTTP响应）
            if hasattr(result, 'status_code'):
                assert result.status_code == expected_status, f"Expected status {expected_status}, got {result.status_code}"
                
                if expected_keys and result.status_code == 200:
                    data = result.json()
                    AssertionHelpers.assert_response_structure(data, expected_keys)
            
            return result
        
        return wrapper
    return decorator


# 数据完整性验证器
class DataIntegrityValidator:
    """数据完整性验证器"""
    
    @staticmethod
    async def validate_user_data_integrity(db: AsyncSession, user_id: int):
        """验证用户数据完整性"""
        from sqlalchemy import select
        
        # 检查用户存在
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        assert user is not None, f"User {user_id} not found"
        assert user.is_active is not None, "User active status should not be None"
        assert user.email is not None, "User email should not be None"
        
        # 检查相关数据一致性
        strategy_result = await db.execute(select(Strategy).where(Strategy.user_id == user_id))
        strategies = strategy_result.scalars().all()
        
        for strategy in strategies:
            assert strategy.user_id == user_id, "Strategy user_id mismatch"
            assert strategy.status is not None, "Strategy status should not be None"


@pytest.fixture
def data_integrity_validator():
    """数据完整性验证器夹具"""
    return DataIntegrityValidator()


# 环境配置
@pytest.fixture(scope="session")
def test_settings():
    """测试设置"""
    return {
        "TESTING": True,
        "DATABASE_URL": "sqlite+aiosqlite:///:memory:",
        "SECRET_KEY": "test-secret-key-for-testing-only",
        "ALGORITHM": "HS256",
        "ACCESS_TOKEN_EXPIRE_MINUTES": 30,
        "STRICT_TESTING": True
    }