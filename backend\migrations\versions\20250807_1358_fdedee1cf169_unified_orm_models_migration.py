"""Unified ORM models migration

Revision ID: fdedee1cf169
Revises: 003
Create Date: 2025-08-07 13:58:10.745369

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fdedee1cf169'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('instruments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='品种代码'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='品种名称'),
    sa.Column('name_en', sa.String(length=100), nullable=True, comment='英文名称'),
    sa.Column('instrument_type', sa.Enum('STOCK', 'FUTURE', 'OPTION', 'FOREX', 'CRYPTO', 'INDEX', 'FUND', name='instrumenttype'), nullable=False, comment='品种类型'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('sector', sa.String(length=50), nullable=True, comment='行业板块'),
    sa.Column('industry', sa.String(length=50), nullable=True, comment='细分行业'),
    sa.Column('currency', sa.String(length=3), nullable=True, comment='计价货币'),
    sa.Column('lot_size', sa.Integer(), nullable=True, comment='每手数量'),
    sa.Column('tick_size', sa.Numeric(precision=15, scale=8), nullable=True, comment='最小价格变动'),
    sa.Column('multiplier', sa.Numeric(precision=15, scale=4), nullable=True, comment='合约乘数'),
    sa.Column('current_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='当前价格'),
    sa.Column('pre_close', sa.Numeric(precision=15, scale=4), nullable=True, comment='昨收价'),
    sa.Column('upper_limit', sa.Numeric(precision=15, scale=4), nullable=True, comment='涨停价'),
    sa.Column('lower_limit', sa.Numeric(precision=15, scale=4), nullable=True, comment='跌停价'),
    sa.Column('market_cap', sa.Numeric(precision=15, scale=2), nullable=True, comment='总市值'),
    sa.Column('circulating_cap', sa.Numeric(precision=15, scale=2), nullable=True, comment='流通市值'),
    sa.Column('pe_ratio', sa.Numeric(precision=8, scale=2), nullable=True, comment='市盈率'),
    sa.Column('pb_ratio', sa.Numeric(precision=8, scale=2), nullable=True, comment='市净率'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否活跃'),
    sa.Column('is_suspended', sa.Boolean(), nullable=True, comment='是否停牌'),
    sa.Column('list_date', sa.DateTime(), nullable=True, comment='上市日期'),
    sa.Column('delist_date', sa.DateTime(), nullable=True, comment='退市日期'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_instruments_active', 'instruments', ['is_active'], unique=False)
    op.create_index('idx_instruments_exchange', 'instruments', ['exchange'], unique=False)
    op.create_index('idx_instruments_symbol', 'instruments', ['symbol'], unique=False)
    op.create_index('idx_instruments_type', 'instruments', ['instrument_type'], unique=False)
    op.create_index(op.f('ix_instruments_exchange'), 'instruments', ['exchange'], unique=False)
    op.create_index(op.f('ix_instruments_instrument_type'), 'instruments', ['instrument_type'], unique=False)
    op.create_index(op.f('ix_instruments_symbol'), 'instruments', ['symbol'], unique=True)
    op.create_table('market_depth',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='品种代码'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('timestamp', sa.DateTime(), nullable=False, comment='时间戳'),
    sa.Column('bids', sa.Text(), nullable=True, comment='买盘深度数据(JSON)'),
    sa.Column('asks', sa.Text(), nullable=True, comment='卖盘深度数据(JSON)'),
    sa.Column('bid_count', sa.Integer(), nullable=True, comment='买盘档数'),
    sa.Column('ask_count', sa.Integer(), nullable=True, comment='卖盘档数'),
    sa.Column('total_bid_volume', sa.Numeric(precision=20, scale=2), nullable=True, comment='总买量'),
    sa.Column('total_ask_volume', sa.Numeric(precision=20, scale=2), nullable=True, comment='总卖量'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_market_depth_symbol', 'market_depth', ['symbol'], unique=False)
    op.create_index('idx_market_depth_symbol_time', 'market_depth', ['symbol', 'timestamp'], unique=False)
    op.create_index('idx_market_depth_timestamp', 'market_depth', ['timestamp'], unique=False)
    op.create_index(op.f('ix_market_depth_symbol'), 'market_depth', ['symbol'], unique=False)
    op.create_index(op.f('ix_market_depth_timestamp'), 'market_depth', ['timestamp'], unique=False)
    op.create_table('permissions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='权限名称'),
    sa.Column('display_name', sa.String(length=100), nullable=False, comment='显示名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='权限描述'),
    sa.Column('category', sa.String(length=50), nullable=False, comment='权限分类'),
    sa.Column('resource', sa.String(length=100), nullable=False, comment='资源'),
    sa.Column('action', sa.String(length=50), nullable=False, comment='操作'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index('idx_permissions_category', 'permissions', ['category'], unique=False)
    op.create_index('idx_permissions_name', 'permissions', ['name'], unique=False)
    op.create_index('idx_permissions_resource_action', 'permissions', ['resource', 'action'], unique=False)
    op.create_table('roles',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='角色名称'),
    sa.Column('display_name', sa.String(length=100), nullable=False, comment='显示名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='角色描述'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('is_system', sa.Boolean(), nullable=True, comment='是否系统角色'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index('idx_roles_is_active', 'roles', ['is_active'], unique=False)
    op.create_index('idx_roles_name', 'roles', ['name'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('email', sa.String(length=255), nullable=False, comment='邮箱'),
    sa.Column('phone', sa.String(length=20), nullable=True, comment='手机号'),
    sa.Column('password_hash', sa.String(length=255), nullable=False, comment='密码哈希'),
    sa.Column('salt', sa.String(length=32), nullable=False, comment='密码盐值'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED', name='userstatusenum'), nullable=True, comment='用户状态'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('is_verified', sa.Boolean(), nullable=True, comment='是否验证'),
    sa.Column('last_login_at', sa.DateTime(), nullable=True, comment='最后登录时间'),
    sa.Column('last_login_ip', sa.String(length=45), nullable=True, comment='最后登录IP'),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=True, comment='失败登录次数'),
    sa.Column('locked_until', sa.DateTime(), nullable=True, comment='锁定截止时间'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_users_created_at', 'users', ['created_at'], unique=False)
    op.create_index('idx_users_email', 'users', ['email'], unique=False)
    op.create_index('idx_users_status', 'users', ['status'], unique=False)
    op.create_index('idx_users_username', 'users', ['username'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_phone'), 'users', ['phone'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('accounts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.String(length=50), nullable=False, comment='账户ID'),
    sa.Column('account_name', sa.String(length=100), nullable=False, comment='账户名称'),
    sa.Column('account_type', sa.String(length=20), nullable=True, comment='账户类型'),
    sa.Column('broker', sa.String(length=50), nullable=True, comment='券商'),
    sa.Column('total_assets', sa.Numeric(precision=15, scale=2), nullable=True, comment='总资产'),
    sa.Column('available_cash', sa.Numeric(precision=15, scale=2), nullable=True, comment='可用现金'),
    sa.Column('frozen_cash', sa.Numeric(precision=15, scale=2), nullable=True, comment='冻结现金'),
    sa.Column('market_value', sa.Numeric(precision=15, scale=2), nullable=True, comment='持仓市值'),
    sa.Column('total_profit', sa.Numeric(precision=15, scale=2), nullable=True, comment='总盈亏'),
    sa.Column('day_profit', sa.Numeric(precision=15, scale=2), nullable=True, comment='当日盈亏'),
    sa.Column('realized_profit', sa.Numeric(precision=15, scale=2), nullable=True, comment='已实现盈亏'),
    sa.Column('unrealized_profit', sa.Numeric(precision=15, scale=2), nullable=True, comment='未实现盈亏'),
    sa.Column('max_order_value', sa.Numeric(precision=15, scale=2), nullable=True, comment='单笔最大订单金额'),
    sa.Column('daily_loss_limit', sa.Numeric(precision=15, scale=2), nullable=True, comment='日损失限制'),
    sa.Column('position_limit', sa.Numeric(precision=8, scale=4), nullable=True, comment='持仓限制比例'),
    sa.Column('status', sa.Enum('ACTIVE', 'SUSPENDED', 'CLOSED', name='accountstatus'), nullable=False, comment='账户状态'),
    sa.Column('is_paper_trading', sa.Boolean(), nullable=True, comment='是否模拟交易'),
    sa.Column('last_update_time', sa.DateTime(), nullable=True, comment='最后更新时间'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('account_id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index('idx_accounts_account_id', 'accounts', ['account_id'], unique=False)
    op.create_index('idx_accounts_status', 'accounts', ['status'], unique=False)
    op.create_index('idx_accounts_user_id', 'accounts', ['user_id'], unique=False)
    op.create_table('market_klines',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('instrument_id', sa.UUID(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='品种代码'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('frequency', sa.Enum('TICK', 'MINUTE_1', 'MINUTE_5', 'MINUTE_15', 'MINUTE_30', 'HOUR_1', 'HOUR_4', 'DAILY', 'WEEKLY', 'MONTHLY', name='datafrequency'), nullable=False, comment='数据频率'),
    sa.Column('timestamp', sa.DateTime(), nullable=False, comment='时间戳'),
    sa.Column('trading_date', sa.DateTime(), nullable=False, comment='交易日期'),
    sa.Column('open_price', sa.Numeric(precision=15, scale=4), nullable=False, comment='开盘价'),
    sa.Column('high_price', sa.Numeric(precision=15, scale=4), nullable=False, comment='最高价'),
    sa.Column('low_price', sa.Numeric(precision=15, scale=4), nullable=False, comment='最低价'),
    sa.Column('close_price', sa.Numeric(precision=15, scale=4), nullable=False, comment='收盘价'),
    sa.Column('volume', sa.Numeric(precision=20, scale=2), nullable=True, comment='成交量'),
    sa.Column('turnover', sa.Numeric(precision=20, scale=2), nullable=True, comment='成交额'),
    sa.Column('vwap', sa.Numeric(precision=15, scale=4), nullable=True, comment='成交量加权平均价'),
    sa.Column('change', sa.Numeric(precision=15, scale=4), nullable=True, comment='价格变动'),
    sa.Column('change_pct', sa.Numeric(precision=8, scale=4), nullable=True, comment='变动百分比'),
    sa.Column('ma5', sa.Numeric(precision=15, scale=4), nullable=True, comment='5日均线'),
    sa.Column('ma10', sa.Numeric(precision=15, scale=4), nullable=True, comment='10日均线'),
    sa.Column('ma20', sa.Numeric(precision=15, scale=4), nullable=True, comment='20日均线'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['instrument_id'], ['instruments.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_market_klines_frequency', 'market_klines', ['frequency'], unique=False)
    op.create_index('idx_market_klines_instrument_freq_time', 'market_klines', ['instrument_id', 'frequency', 'timestamp'], unique=False)
    op.create_index('idx_market_klines_instrument_id', 'market_klines', ['instrument_id'], unique=False)
    op.create_index('idx_market_klines_symbol', 'market_klines', ['symbol'], unique=False)
    op.create_index('idx_market_klines_symbol_freq_time', 'market_klines', ['symbol', 'frequency', 'timestamp'], unique=False)
    op.create_index('idx_market_klines_timestamp', 'market_klines', ['timestamp'], unique=False)
    op.create_index('idx_market_klines_trading_date', 'market_klines', ['trading_date'], unique=False)
    op.create_index('idx_market_klines_unique', 'market_klines', ['symbol', 'frequency', 'timestamp'], unique=True)
    op.create_index(op.f('ix_market_klines_frequency'), 'market_klines', ['frequency'], unique=False)
    op.create_index(op.f('ix_market_klines_instrument_id'), 'market_klines', ['instrument_id'], unique=False)
    op.create_index(op.f('ix_market_klines_symbol'), 'market_klines', ['symbol'], unique=False)
    op.create_index(op.f('ix_market_klines_timestamp'), 'market_klines', ['timestamp'], unique=False)
    op.create_index(op.f('ix_market_klines_trading_date'), 'market_klines', ['trading_date'], unique=False)
    op.create_table('market_ticks',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('instrument_id', sa.UUID(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='品种代码'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('timestamp', sa.DateTime(), nullable=False, comment='时间戳'),
    sa.Column('trading_date', sa.DateTime(), nullable=False, comment='交易日期'),
    sa.Column('last_price', sa.Numeric(precision=15, scale=4), nullable=False, comment='最新价'),
    sa.Column('pre_close', sa.Numeric(precision=15, scale=4), nullable=True, comment='昨收价'),
    sa.Column('open_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='开盘价'),
    sa.Column('high_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='最高价'),
    sa.Column('low_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='最低价'),
    sa.Column('volume', sa.Numeric(precision=20, scale=2), nullable=True, comment='成交量'),
    sa.Column('turnover', sa.Numeric(precision=20, scale=2), nullable=True, comment='成交额'),
    sa.Column('bid_price_1', sa.Numeric(precision=15, scale=4), nullable=True, comment='买1价'),
    sa.Column('bid_volume_1', sa.Numeric(precision=15, scale=2), nullable=True, comment='买1量'),
    sa.Column('ask_price_1', sa.Numeric(precision=15, scale=4), nullable=True, comment='卖1价'),
    sa.Column('ask_volume_1', sa.Numeric(precision=15, scale=2), nullable=True, comment='卖1量'),
    sa.Column('bid_price_2', sa.Numeric(precision=15, scale=4), nullable=True, comment='买2价'),
    sa.Column('bid_volume_2', sa.Numeric(precision=15, scale=2), nullable=True, comment='买2量'),
    sa.Column('ask_price_2', sa.Numeric(precision=15, scale=4), nullable=True, comment='卖2价'),
    sa.Column('ask_volume_2', sa.Numeric(precision=15, scale=2), nullable=True, comment='卖2量'),
    sa.Column('bid_price_3', sa.Numeric(precision=15, scale=4), nullable=True, comment='买3价'),
    sa.Column('bid_volume_3', sa.Numeric(precision=15, scale=2), nullable=True, comment='买3量'),
    sa.Column('ask_price_3', sa.Numeric(precision=15, scale=4), nullable=True, comment='卖3价'),
    sa.Column('ask_volume_3', sa.Numeric(precision=15, scale=2), nullable=True, comment='卖3量'),
    sa.Column('bid_price_4', sa.Numeric(precision=15, scale=4), nullable=True, comment='买4价'),
    sa.Column('bid_volume_4', sa.Numeric(precision=15, scale=2), nullable=True, comment='买4量'),
    sa.Column('ask_price_4', sa.Numeric(precision=15, scale=4), nullable=True, comment='卖4价'),
    sa.Column('ask_volume_4', sa.Numeric(precision=15, scale=2), nullable=True, comment='卖4量'),
    sa.Column('bid_price_5', sa.Numeric(precision=15, scale=4), nullable=True, comment='买5价'),
    sa.Column('bid_volume_5', sa.Numeric(precision=15, scale=2), nullable=True, comment='买5量'),
    sa.Column('ask_price_5', sa.Numeric(precision=15, scale=4), nullable=True, comment='卖5价'),
    sa.Column('ask_volume_5', sa.Numeric(precision=15, scale=2), nullable=True, comment='卖5量'),
    sa.Column('upper_limit', sa.Numeric(precision=15, scale=4), nullable=True, comment='涨停价'),
    sa.Column('lower_limit', sa.Numeric(precision=15, scale=4), nullable=True, comment='跌停价'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.ForeignKeyConstraint(['instrument_id'], ['instruments.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_market_ticks_instrument_id', 'market_ticks', ['instrument_id'], unique=False)
    op.create_index('idx_market_ticks_instrument_time', 'market_ticks', ['instrument_id', 'timestamp'], unique=False)
    op.create_index('idx_market_ticks_symbol', 'market_ticks', ['symbol'], unique=False)
    op.create_index('idx_market_ticks_symbol_time', 'market_ticks', ['symbol', 'timestamp'], unique=False)
    op.create_index('idx_market_ticks_timestamp', 'market_ticks', ['timestamp'], unique=False)
    op.create_index('idx_market_ticks_trading_date', 'market_ticks', ['trading_date'], unique=False)
    op.create_index(op.f('ix_market_ticks_instrument_id'), 'market_ticks', ['instrument_id'], unique=False)
    op.create_index(op.f('ix_market_ticks_symbol'), 'market_ticks', ['symbol'], unique=False)
    op.create_index(op.f('ix_market_ticks_timestamp'), 'market_ticks', ['timestamp'], unique=False)
    op.create_index(op.f('ix_market_ticks_trading_date'), 'market_ticks', ['trading_date'], unique=False)
    op.create_table('role_permissions',
    sa.Column('role_id', sa.UUID(), nullable=False),
    sa.Column('permission_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('role_id', 'permission_id')
    )
    op.create_table('strategies',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='策略名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='策略描述'),
    sa.Column('strategy_type', sa.Enum('TREND_FOLLOWING', 'MEAN_REVERSION', 'ARBITRAGE', 'MARKET_MAKING', 'HIGH_FREQUENCY', 'QUANTITATIVE', 'CUSTOM', name='strategytype'), nullable=False, comment='策略类型'),
    sa.Column('code', sa.Text(), nullable=True, comment='策略代码'),
    sa.Column('config', sa.Text(), nullable=True, comment='策略配置(JSON)'),
    sa.Column('parameters', sa.Text(), nullable=True, comment='策略参数(JSON)'),
    sa.Column('status', sa.Enum('DRAFT', 'TESTING', 'ACTIVE', 'PAUSED', 'STOPPED', 'ARCHIVED', name='strategystatus'), nullable=False, comment='策略状态'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('is_public', sa.Boolean(), nullable=True, comment='是否公开'),
    sa.Column('max_position_size', sa.Numeric(precision=15, scale=2), nullable=True, comment='最大持仓规模'),
    sa.Column('max_daily_loss', sa.Numeric(precision=15, scale=2), nullable=True, comment='最大日损失'),
    sa.Column('max_drawdown', sa.Numeric(precision=8, scale=4), nullable=True, comment='最大回撤'),
    sa.Column('total_trades', sa.Integer(), nullable=True, comment='总交易次数'),
    sa.Column('winning_trades', sa.Integer(), nullable=True, comment='盈利交易次数'),
    sa.Column('losing_trades', sa.Integer(), nullable=True, comment='亏损交易次数'),
    sa.Column('total_pnl', sa.Numeric(precision=15, scale=2), nullable=True, comment='总盈亏'),
    sa.Column('win_rate', sa.Numeric(precision=5, scale=4), nullable=True, comment='胜率'),
    sa.Column('sharpe_ratio', sa.Numeric(precision=8, scale=4), nullable=True, comment='夏普比率'),
    sa.Column('max_drawdown_pct', sa.Numeric(precision=8, scale=4), nullable=True, comment='最大回撤百分比'),
    sa.Column('start_date', sa.DateTime(), nullable=True, comment='策略开始时间'),
    sa.Column('end_date', sa.DateTime(), nullable=True, comment='策略结束时间'),
    sa.Column('last_run_time', sa.DateTime(), nullable=True, comment='最后运行时间'),
    sa.Column('version', sa.String(length=20), nullable=True, comment='策略版本'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_strategies_active', 'strategies', ['is_active'], unique=False)
    op.create_index('idx_strategies_created_at', 'strategies', ['created_at'], unique=False)
    op.create_index('idx_strategies_status', 'strategies', ['status'], unique=False)
    op.create_index('idx_strategies_type', 'strategies', ['strategy_type'], unique=False)
    op.create_index('idx_strategies_user_id', 'strategies', ['user_id'], unique=False)
    op.create_index(op.f('ix_strategies_status'), 'strategies', ['status'], unique=False)
    op.create_index(op.f('ix_strategies_user_id'), 'strategies', ['user_id'], unique=False)
    op.create_table('user_profiles',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('real_name', sa.String(length=50), nullable=True, comment='真实姓名'),
    sa.Column('nickname', sa.String(length=50), nullable=True, comment='昵称'),
    sa.Column('avatar_url', sa.String(length=500), nullable=True, comment='头像URL'),
    sa.Column('bio', sa.Text(), nullable=True, comment='个人简介'),
    sa.Column('address', sa.String(length=500), nullable=True, comment='地址'),
    sa.Column('city', sa.String(length=50), nullable=True, comment='城市'),
    sa.Column('country', sa.String(length=50), nullable=True, comment='国家'),
    sa.Column('timezone', sa.String(length=50), nullable=True, comment='时区'),
    sa.Column('language', sa.String(length=10), nullable=True, comment='语言偏好'),
    sa.Column('trading_experience', sa.String(length=20), nullable=True, comment='交易经验'),
    sa.Column('risk_tolerance', sa.String(length=20), nullable=True, comment='风险承受能力'),
    sa.Column('investment_goal', sa.String(length=500), nullable=True, comment='投资目标'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('user_role_assignments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('role_id', sa.UUID(), nullable=False),
    sa.Column('assigned_by', sa.UUID(), nullable=True, comment='分配者'),
    sa.Column('assigned_at', sa.DateTime(), nullable=False, comment='分配时间'),
    sa.Column('expires_at', sa.DateTime(), nullable=True, comment='过期时间'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_role_assignments_active', 'user_role_assignments', ['is_active'], unique=False)
    op.create_index('idx_user_role_assignments_role_id', 'user_role_assignments', ['role_id'], unique=False)
    op.create_index('idx_user_role_assignments_user_id', 'user_role_assignments', ['user_id'], unique=False)
    op.create_table('user_roles',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('role_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'role_id')
    )
    op.create_table('user_settings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('theme', sa.String(length=20), nullable=True, comment='主题'),
    sa.Column('layout_config', sa.Text(), nullable=True, comment='布局配置(JSON)'),
    sa.Column('dashboard_config', sa.Text(), nullable=True, comment='仪表板配置(JSON)'),
    sa.Column('email_notifications', sa.Boolean(), nullable=True, comment='邮件通知'),
    sa.Column('push_notifications', sa.Boolean(), nullable=True, comment='推送通知'),
    sa.Column('sms_notifications', sa.Boolean(), nullable=True, comment='短信通知'),
    sa.Column('trade_notifications', sa.Boolean(), nullable=True, comment='交易通知'),
    sa.Column('alert_notifications', sa.Boolean(), nullable=True, comment='报警通知'),
    sa.Column('default_order_type', sa.String(length=20), nullable=True, comment='默认订单类型'),
    sa.Column('auto_refresh_interval', sa.Integer(), nullable=True, comment='自动刷新间隔(秒)'),
    sa.Column('price_decimal_places', sa.Integer(), nullable=True, comment='价格小数位数'),
    sa.Column('two_factor_enabled', sa.Boolean(), nullable=True, comment='双因子认证'),
    sa.Column('login_ip_whitelist', sa.Text(), nullable=True, comment='登录IP白名单'),
    sa.Column('session_timeout', sa.Integer(), nullable=True, comment='会话超时时间(秒)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('account_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.UUID(), nullable=False),
    sa.Column('record_date', sa.DateTime(), nullable=False, comment='记录日期'),
    sa.Column('total_assets', sa.Numeric(precision=15, scale=2), nullable=False, comment='总资产'),
    sa.Column('available_cash', sa.Numeric(precision=15, scale=2), nullable=False, comment='可用现金'),
    sa.Column('market_value', sa.Numeric(precision=15, scale=2), nullable=False, comment='持仓市值'),
    sa.Column('day_profit', sa.Numeric(precision=15, scale=2), nullable=True, comment='当日盈亏'),
    sa.Column('total_profit', sa.Numeric(precision=15, scale=2), nullable=True, comment='总盈亏'),
    sa.Column('profit_rate', sa.Numeric(precision=8, scale=4), nullable=True, comment='收益率'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_account_history_account_date', 'account_history', ['account_id', 'record_date'], unique=False)
    op.create_index('idx_account_history_account_id', 'account_history', ['account_id'], unique=False)
    op.create_index('idx_account_history_record_date', 'account_history', ['record_date'], unique=False)
    op.create_index(op.f('ix_account_history_account_id'), 'account_history', ['account_id'], unique=False)
    op.create_index(op.f('ix_account_history_record_date'), 'account_history', ['record_date'], unique=False)
    op.create_table('backtests',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('strategy_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='回测名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='回测描述'),
    sa.Column('start_date', sa.DateTime(), nullable=False, comment='回测开始时间'),
    sa.Column('end_date', sa.DateTime(), nullable=False, comment='回测结束时间'),
    sa.Column('initial_capital', sa.Numeric(precision=15, scale=2), nullable=False, comment='初始资金'),
    sa.Column('benchmark', sa.String(length=20), nullable=True, comment='基准标的'),
    sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='backteststatus'), nullable=False, comment='回测状态'),
    sa.Column('final_value', sa.Numeric(precision=15, scale=2), nullable=True, comment='最终价值'),
    sa.Column('total_return', sa.Numeric(precision=8, scale=4), nullable=True, comment='总收益率'),
    sa.Column('annual_return', sa.Numeric(precision=8, scale=4), nullable=True, comment='年化收益率'),
    sa.Column('max_drawdown', sa.Numeric(precision=8, scale=4), nullable=True, comment='最大回撤'),
    sa.Column('sharpe_ratio', sa.Numeric(precision=8, scale=4), nullable=True, comment='夏普比率'),
    sa.Column('sortino_ratio', sa.Numeric(precision=8, scale=4), nullable=True, comment='索提诺比率'),
    sa.Column('volatility', sa.Numeric(precision=8, scale=4), nullable=True, comment='波动率'),
    sa.Column('beta', sa.Numeric(precision=8, scale=4), nullable=True, comment='贝塔值'),
    sa.Column('alpha', sa.Numeric(precision=8, scale=4), nullable=True, comment='阿尔法值'),
    sa.Column('total_trades', sa.Integer(), nullable=True, comment='总交易次数'),
    sa.Column('winning_trades', sa.Integer(), nullable=True, comment='盈利交易次数'),
    sa.Column('losing_trades', sa.Integer(), nullable=True, comment='亏损交易次数'),
    sa.Column('win_rate', sa.Numeric(precision=5, scale=4), nullable=True, comment='胜率'),
    sa.Column('avg_win', sa.Numeric(precision=15, scale=2), nullable=True, comment='平均盈利'),
    sa.Column('avg_loss', sa.Numeric(precision=15, scale=2), nullable=True, comment='平均亏损'),
    sa.Column('profit_factor', sa.Numeric(precision=8, scale=4), nullable=True, comment='盈利因子'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='实际开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='实际结束时间'),
    sa.Column('duration', sa.Integer(), nullable=True, comment='运行时长(秒)'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('result_data', sa.Text(), nullable=True, comment='回测结果数据(JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['strategy_id'], ['strategies.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_backtests_created_at', 'backtests', ['created_at'], unique=False)
    op.create_index('idx_backtests_start_date', 'backtests', ['start_date'], unique=False)
    op.create_index('idx_backtests_status', 'backtests', ['status'], unique=False)
    op.create_index('idx_backtests_strategy_id', 'backtests', ['strategy_id'], unique=False)
    op.create_index('idx_backtests_user_id', 'backtests', ['user_id'], unique=False)
    op.create_index(op.f('ix_backtests_status'), 'backtests', ['status'], unique=False)
    op.create_index(op.f('ix_backtests_strategy_id'), 'backtests', ['strategy_id'], unique=False)
    op.create_index(op.f('ix_backtests_user_id'), 'backtests', ['user_id'], unique=False)
    op.create_table('ctp_accounts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.UUID(), nullable=True, comment='关联的统一账户ID'),
    sa.Column('broker_id', sa.String(length=11), nullable=False, comment='经纪公司代码'),
    sa.Column('investor_id', sa.String(length=13), nullable=False, comment='投资者帐号'),
    sa.Column('pre_balance', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次结算准备金'),
    sa.Column('pre_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次占用的保证金'),
    sa.Column('deposit', sa.Numeric(precision=15, scale=4), nullable=True, comment='入金金额'),
    sa.Column('withdraw', sa.Numeric(precision=15, scale=4), nullable=True, comment='出金金额'),
    sa.Column('frozen_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='冻结的保证金'),
    sa.Column('frozen_cash', sa.Numeric(precision=15, scale=4), nullable=True, comment='冻结的资金'),
    sa.Column('frozen_commission', sa.Numeric(precision=15, scale=4), nullable=True, comment='冻结的手续费'),
    sa.Column('curr_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='当前保证金总额'),
    sa.Column('commission', sa.Numeric(precision=15, scale=4), nullable=True, comment='手续费'),
    sa.Column('close_profit', sa.Numeric(precision=15, scale=4), nullable=True, comment='平仓盈亏'),
    sa.Column('position_profit', sa.Numeric(precision=15, scale=4), nullable=True, comment='持仓盈亏'),
    sa.Column('balance', sa.Numeric(precision=15, scale=4), nullable=True, comment='期货结算准备金'),
    sa.Column('available', sa.Numeric(precision=15, scale=4), nullable=True, comment='可用资金'),
    sa.Column('withdraw_quota', sa.Numeric(precision=15, scale=4), nullable=True, comment='可取资金'),
    sa.Column('pre_mortgage', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次质押金额'),
    sa.Column('pre_credit', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次信用额度'),
    sa.Column('pre_deposit', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次存款额'),
    sa.Column('interest_base', sa.Numeric(precision=15, scale=4), nullable=True, comment='利息基数'),
    sa.Column('interest', sa.Numeric(precision=15, scale=4), nullable=True, comment='利息收入'),
    sa.Column('cash_in', sa.Numeric(precision=15, scale=4), nullable=True, comment='资金差额'),
    sa.Column('reserve', sa.Numeric(precision=15, scale=4), nullable=True, comment='基本准备金'),
    sa.Column('trading_day', sa.String(length=9), nullable=True, comment='交易日'),
    sa.Column('settlement_id', sa.Integer(), nullable=True, comment='结算编号'),
    sa.Column('credit', sa.Numeric(precision=15, scale=4), nullable=True, comment='信用额度'),
    sa.Column('mortgage', sa.Numeric(precision=15, scale=4), nullable=True, comment='质押金额'),
    sa.Column('exchange_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='交易所保证金'),
    sa.Column('delivery_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='投资者交割保证金'),
    sa.Column('exchange_delivery_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='交易所交割保证金'),
    sa.Column('reserve_balance', sa.Numeric(precision=15, scale=4), nullable=True, comment='保底期货结算准备金'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_ctp_accounts_broker_id', 'ctp_accounts', ['broker_id'], unique=False)
    op.create_index('idx_ctp_accounts_investor_id', 'ctp_accounts', ['investor_id'], unique=False)
    op.create_index('idx_ctp_accounts_user_id', 'ctp_accounts', ['user_id'], unique=False)
    op.create_index(op.f('ix_ctp_accounts_account_id'), 'ctp_accounts', ['account_id'], unique=False)
    op.create_index(op.f('ix_ctp_accounts_user_id'), 'ctp_accounts', ['user_id'], unique=False)
    op.create_table('orders',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.UUID(), nullable=False),
    sa.Column('strategy_id', sa.UUID(), nullable=True),
    sa.Column('order_id', sa.String(length=50), nullable=False, comment='订单ID'),
    sa.Column('client_order_id', sa.String(length=50), nullable=True, comment='客户端订单ID'),
    sa.Column('parent_order_id', sa.UUID(), nullable=True, comment='父订单ID'),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='交易标的'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('security_type', sa.String(length=20), nullable=True, comment='证券类型'),
    sa.Column('order_type', sa.Enum('MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT', name='ordertype'), nullable=False, comment='订单类型'),
    sa.Column('side', sa.Enum('BUY', 'SELL', name='orderside'), nullable=False, comment='买卖方向'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='订单数量'),
    sa.Column('price', sa.Numeric(precision=15, scale=4), nullable=True, comment='订单价格'),
    sa.Column('stop_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='止损价格'),
    sa.Column('filled_quantity', sa.Integer(), nullable=True, comment='已成交数量'),
    sa.Column('avg_fill_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='平均成交价格'),
    sa.Column('remaining_quantity', sa.Integer(), nullable=False, comment='剩余数量'),
    sa.Column('status', sa.Enum('PENDING', 'SUBMITTED', 'PARTIAL_FILLED', 'FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED', name='orderstatus'), nullable=False, comment='订单状态'),
    sa.Column('submit_time', sa.DateTime(), nullable=False, comment='提交时间'),
    sa.Column('fill_time', sa.DateTime(), nullable=True, comment='成交时间'),
    sa.Column('cancel_time', sa.DateTime(), nullable=True, comment='撤销时间'),
    sa.Column('commission', sa.Numeric(precision=15, scale=4), nullable=True, comment='手续费'),
    sa.Column('total_cost', sa.Numeric(precision=15, scale=4), nullable=True, comment='总成本'),
    sa.Column('time_in_force', sa.String(length=10), nullable=True, comment='有效期'),
    sa.Column('reject_reason', sa.String(length=500), nullable=True, comment='拒绝原因'),
    sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['parent_order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['strategy_id'], ['strategies.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_orders_account_id', 'orders', ['account_id'], unique=False)
    op.create_index('idx_orders_status', 'orders', ['status'], unique=False)
    op.create_index('idx_orders_strategy_symbol', 'orders', ['strategy_id', 'symbol'], unique=False)
    op.create_index('idx_orders_submit_time', 'orders', ['submit_time'], unique=False)
    op.create_index('idx_orders_symbol', 'orders', ['symbol'], unique=False)
    op.create_index('idx_orders_user_id', 'orders', ['user_id'], unique=False)
    op.create_index(op.f('ix_orders_account_id'), 'orders', ['account_id'], unique=False)
    op.create_index(op.f('ix_orders_order_id'), 'orders', ['order_id'], unique=True)
    op.create_index(op.f('ix_orders_status'), 'orders', ['status'], unique=False)
    op.create_index(op.f('ix_orders_strategy_id'), 'orders', ['strategy_id'], unique=False)
    op.create_index(op.f('ix_orders_symbol'), 'orders', ['symbol'], unique=False)
    op.create_index(op.f('ix_orders_user_id'), 'orders', ['user_id'], unique=False)
    op.create_table('positions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.UUID(), nullable=False),
    sa.Column('strategy_id', sa.UUID(), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='交易标的'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('security_type', sa.String(length=20), nullable=True, comment='证券类型'),
    sa.Column('side', sa.Enum('LONG', 'SHORT', name='positionside'), nullable=False, comment='持仓方向'),
    sa.Column('total_quantity', sa.Integer(), nullable=True, comment='总持仓数量'),
    sa.Column('available_quantity', sa.Integer(), nullable=True, comment='可用数量'),
    sa.Column('frozen_quantity', sa.Integer(), nullable=True, comment='冻结数量'),
    sa.Column('avg_cost_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='平均成本价'),
    sa.Column('total_cost', sa.Numeric(precision=15, scale=4), nullable=True, comment='总成本'),
    sa.Column('current_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='当前价格'),
    sa.Column('market_value', sa.Numeric(precision=15, scale=4), nullable=True, comment='市值'),
    sa.Column('unrealized_pnl', sa.Numeric(precision=15, scale=4), nullable=True, comment='未实现盈亏'),
    sa.Column('realized_pnl', sa.Numeric(precision=15, scale=4), nullable=True, comment='已实现盈亏'),
    sa.Column('total_pnl', sa.Numeric(precision=15, scale=4), nullable=True, comment='总盈亏'),
    sa.Column('pnl_percentage', sa.Numeric(precision=8, scale=4), nullable=True, comment='盈亏比例'),
    sa.Column('stop_loss_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='止损价格'),
    sa.Column('take_profit_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='止盈价格'),
    sa.Column('open_date', sa.DateTime(), nullable=True, comment='建仓日期'),
    sa.Column('last_trade_date', sa.DateTime(), nullable=True, comment='最后交易日期'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['strategy_id'], ['strategies.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_positions_account_id', 'positions', ['account_id'], unique=False)
    op.create_index('idx_positions_account_symbol_unique', 'positions', ['account_id', 'symbol'], unique=True)
    op.create_index('idx_positions_strategy_symbol', 'positions', ['strategy_id', 'symbol'], unique=False)
    op.create_index('idx_positions_symbol', 'positions', ['symbol'], unique=False)
    op.create_index('idx_positions_user_id', 'positions', ['user_id'], unique=False)
    op.create_index(op.f('ix_positions_account_id'), 'positions', ['account_id'], unique=False)
    op.create_index(op.f('ix_positions_strategy_id'), 'positions', ['strategy_id'], unique=False)
    op.create_index(op.f('ix_positions_symbol'), 'positions', ['symbol'], unique=False)
    op.create_index(op.f('ix_positions_user_id'), 'positions', ['user_id'], unique=False)
    op.create_table('strategy_performance',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('strategy_id', sa.UUID(), nullable=False),
    sa.Column('record_date', sa.DateTime(), nullable=False, comment='记录日期'),
    sa.Column('total_value', sa.Numeric(precision=15, scale=2), nullable=False, comment='总价值'),
    sa.Column('cash', sa.Numeric(precision=15, scale=2), nullable=False, comment='现金'),
    sa.Column('position_value', sa.Numeric(precision=15, scale=2), nullable=False, comment='持仓价值'),
    sa.Column('daily_return', sa.Numeric(precision=8, scale=4), nullable=True, comment='日收益率'),
    sa.Column('total_return', sa.Numeric(precision=8, scale=4), nullable=True, comment='总收益率'),
    sa.Column('benchmark_return', sa.Numeric(precision=8, scale=4), nullable=True, comment='基准收益率'),
    sa.Column('excess_return', sa.Numeric(precision=8, scale=4), nullable=True, comment='超额收益率'),
    sa.Column('volatility', sa.Numeric(precision=8, scale=4), nullable=True, comment='波动率'),
    sa.Column('drawdown', sa.Numeric(precision=8, scale=4), nullable=True, comment='回撤'),
    sa.Column('var_95', sa.Numeric(precision=15, scale=2), nullable=True, comment='95% VaR'),
    sa.Column('daily_trades', sa.Integer(), nullable=True, comment='当日交易次数'),
    sa.Column('turnover', sa.Numeric(precision=15, scale=2), nullable=True, comment='成交额'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.ForeignKeyConstraint(['strategy_id'], ['strategies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_strategy_performance_record_date', 'strategy_performance', ['record_date'], unique=False)
    op.create_index('idx_strategy_performance_strategy_date', 'strategy_performance', ['strategy_id', 'record_date'], unique=False)
    op.create_index('idx_strategy_performance_strategy_id', 'strategy_performance', ['strategy_id'], unique=False)
    op.create_index(op.f('ix_strategy_performance_record_date'), 'strategy_performance', ['record_date'], unique=False)
    op.create_index(op.f('ix_strategy_performance_strategy_id'), 'strategy_performance', ['strategy_id'], unique=False)
    op.create_table('ctp_orders',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('order_id', sa.UUID(), nullable=True, comment='关联的统一订单ID'),
    sa.Column('order_ref', sa.String(length=13), nullable=False, comment='报单引用'),
    sa.Column('order_sys_id', sa.String(length=21), nullable=True, comment='报单编号'),
    sa.Column('instrument_id', sa.String(length=31), nullable=False, comment='合约代码'),
    sa.Column('exchange_id', sa.String(length=9), nullable=True, comment='交易所代码'),
    sa.Column('direction', sa.Enum('BUY', 'SELL', name='ctpdirection'), nullable=False, comment='买卖方向'),
    sa.Column('offset_flag', sa.Enum('OPEN', 'CLOSE', 'FORCE_CLOSE', 'CLOSE_TODAY', 'CLOSE_YESTERDAY', 'FORCE_OFF', 'LOCAL_FORCE_CLOSE', name='ctpoffsetflag'), nullable=False, comment='开平标志'),
    sa.Column('order_price_type', sa.String(length=1), nullable=False, comment='报单价格条件'),
    sa.Column('limit_price', sa.Numeric(precision=15, scale=4), nullable=False, comment='价格'),
    sa.Column('volume_total_original', sa.Integer(), nullable=False, comment='数量'),
    sa.Column('order_status', sa.Enum('UNKNOWN', 'ALL_TRADED', 'PART_TRADED_QUEUEING', 'PART_TRADED_NOT_QUEUEING', 'NO_TRADE_QUEUEING', 'NO_TRADE_NOT_QUEUEING', 'CANCELED', 'TOUCHED', 'ERROR', name='ctporderstatus'), nullable=False, comment='报单状态'),
    sa.Column('volume_traded', sa.Integer(), nullable=True, comment='已成交数量'),
    sa.Column('volume_total', sa.Integer(), nullable=False, comment='剩余数量'),
    sa.Column('time_condition', sa.String(length=1), nullable=False, comment='有效期类型'),
    sa.Column('volume_condition', sa.String(length=1), nullable=False, comment='成交量类型'),
    sa.Column('min_volume', sa.Integer(), nullable=True, comment='最小成交量'),
    sa.Column('contingent_condition', sa.String(length=1), nullable=True, comment='触发条件'),
    sa.Column('stop_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='止损价'),
    sa.Column('force_close_reason', sa.String(length=1), nullable=True, comment='强平原因'),
    sa.Column('is_auto_suspend', sa.Boolean(), nullable=True, comment='自动挂起标志'),
    sa.Column('insert_date', sa.String(length=9), nullable=True, comment='报单日期'),
    sa.Column('insert_time', sa.String(length=9), nullable=True, comment='委托时间'),
    sa.Column('active_time', sa.String(length=9), nullable=True, comment='激活时间'),
    sa.Column('suspend_time', sa.String(length=9), nullable=True, comment='挂起时间'),
    sa.Column('update_time', sa.String(length=9), nullable=True, comment='最后修改时间'),
    sa.Column('cancel_time', sa.String(length=9), nullable=True, comment='撤销时间'),
    sa.Column('front_id', sa.Integer(), nullable=True, comment='前置编号'),
    sa.Column('session_id', sa.Integer(), nullable=True, comment='会话编号'),
    sa.Column('user_product_info', sa.String(length=11), nullable=True, comment='用户端产品信息'),
    sa.Column('status_msg', sa.String(length=81), nullable=True, comment='状态信息'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_ctp_orders_instrument_id', 'ctp_orders', ['instrument_id'], unique=False)
    op.create_index('idx_ctp_orders_order_ref', 'ctp_orders', ['order_ref'], unique=False)
    op.create_index('idx_ctp_orders_order_sys_id', 'ctp_orders', ['order_sys_id'], unique=False)
    op.create_index('idx_ctp_orders_status', 'ctp_orders', ['order_status'], unique=False)
    op.create_index('idx_ctp_orders_user_id', 'ctp_orders', ['user_id'], unique=False)
    op.create_index(op.f('ix_ctp_orders_instrument_id'), 'ctp_orders', ['instrument_id'], unique=False)
    op.create_index(op.f('ix_ctp_orders_order_id'), 'ctp_orders', ['order_id'], unique=False)
    op.create_index(op.f('ix_ctp_orders_order_status'), 'ctp_orders', ['order_status'], unique=False)
    op.create_index(op.f('ix_ctp_orders_user_id'), 'ctp_orders', ['user_id'], unique=False)
    op.create_table('ctp_positions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('position_id', sa.UUID(), nullable=True, comment='关联的统一持仓ID'),
    sa.Column('instrument_id', sa.String(length=31), nullable=False, comment='合约代码'),
    sa.Column('position_direction', sa.String(length=1), nullable=False, comment='持仓方向'),
    sa.Column('hedge_flag', sa.String(length=1), nullable=True, comment='投机套保标志'),
    sa.Column('position_date', sa.String(length=1), nullable=False, comment='持仓日期'),
    sa.Column('yd_position', sa.Integer(), nullable=True, comment='上日持仓'),
    sa.Column('long_frozen', sa.Integer(), nullable=True, comment='多头冻结'),
    sa.Column('short_frozen', sa.Integer(), nullable=True, comment='空头冻结'),
    sa.Column('yd_long_frozen', sa.Integer(), nullable=True, comment='昨日多头冻结'),
    sa.Column('yd_short_frozen', sa.Integer(), nullable=True, comment='昨日空头冻结'),
    sa.Column('position_cost', sa.Numeric(precision=15, scale=4), nullable=True, comment='持仓成本'),
    sa.Column('pre_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次占用的保证金'),
    sa.Column('use_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='占用的保证金'),
    sa.Column('frozen_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='冻结的保证金'),
    sa.Column('frozen_cash', sa.Numeric(precision=15, scale=4), nullable=True, comment='冻结的资金'),
    sa.Column('frozen_commission', sa.Numeric(precision=15, scale=4), nullable=True, comment='冻结的手续费'),
    sa.Column('close_profit', sa.Numeric(precision=15, scale=4), nullable=True, comment='平仓盈亏'),
    sa.Column('position_profit', sa.Numeric(precision=15, scale=4), nullable=True, comment='持仓盈亏'),
    sa.Column('pre_settlement_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='上次结算价'),
    sa.Column('settlement_price', sa.Numeric(precision=15, scale=4), nullable=True, comment='本次结算价'),
    sa.Column('trading_day', sa.String(length=9), nullable=True, comment='交易日'),
    sa.Column('settlement_id', sa.Integer(), nullable=True, comment='结算编号'),
    sa.Column('open_cost', sa.Numeric(precision=15, scale=4), nullable=True, comment='开仓成本'),
    sa.Column('exchange_margin', sa.Numeric(precision=15, scale=4), nullable=True, comment='交易所保证金'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['position_id'], ['positions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_ctp_positions_direction', 'ctp_positions', ['position_direction'], unique=False)
    op.create_index('idx_ctp_positions_instrument_id', 'ctp_positions', ['instrument_id'], unique=False)
    op.create_index('idx_ctp_positions_user_id', 'ctp_positions', ['user_id'], unique=False)
    op.create_index(op.f('ix_ctp_positions_instrument_id'), 'ctp_positions', ['instrument_id'], unique=False)
    op.create_index(op.f('ix_ctp_positions_position_id'), 'ctp_positions', ['position_id'], unique=False)
    op.create_index(op.f('ix_ctp_positions_user_id'), 'ctp_positions', ['user_id'], unique=False)
    op.create_table('trades',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.UUID(), nullable=False),
    sa.Column('order_id', sa.UUID(), nullable=False),
    sa.Column('trade_id', sa.String(length=50), nullable=False, comment='成交ID'),
    sa.Column('exchange_trade_id', sa.String(length=50), nullable=True, comment='交易所成交ID'),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='交易标的'),
    sa.Column('exchange', sa.String(length=10), nullable=False, comment='交易所'),
    sa.Column('side', sa.Enum('BUY', 'SELL', name='tradetype'), nullable=False, comment='买卖方向'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='成交数量'),
    sa.Column('price', sa.Numeric(precision=15, scale=4), nullable=False, comment='成交价格'),
    sa.Column('amount', sa.Numeric(precision=15, scale=4), nullable=False, comment='成交金额'),
    sa.Column('commission', sa.Numeric(precision=15, scale=4), nullable=True, comment='手续费'),
    sa.Column('stamp_tax', sa.Numeric(precision=15, scale=4), nullable=True, comment='印花税'),
    sa.Column('transfer_fee', sa.Numeric(precision=15, scale=4), nullable=True, comment='过户费'),
    sa.Column('total_cost', sa.Numeric(precision=15, scale=4), nullable=False, comment='总成本'),
    sa.Column('trade_time', sa.DateTime(), nullable=False, comment='成交时间'),
    sa.Column('settlement_date', sa.DateTime(), nullable=True, comment='结算日期'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_trades_account_id', 'trades', ['account_id'], unique=False)
    op.create_index('idx_trades_order_id', 'trades', ['order_id'], unique=False)
    op.create_index('idx_trades_symbol', 'trades', ['symbol'], unique=False)
    op.create_index('idx_trades_trade_time', 'trades', ['trade_time'], unique=False)
    op.create_index('idx_trades_user_id', 'trades', ['user_id'], unique=False)
    op.create_index(op.f('ix_trades_account_id'), 'trades', ['account_id'], unique=False)
    op.create_index(op.f('ix_trades_order_id'), 'trades', ['order_id'], unique=False)
    op.create_index(op.f('ix_trades_symbol'), 'trades', ['symbol'], unique=False)
    op.create_index(op.f('ix_trades_trade_id'), 'trades', ['trade_id'], unique=True)
    op.create_index(op.f('ix_trades_user_id'), 'trades', ['user_id'], unique=False)
    op.create_table('asset_records',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('account_id', sa.UUID(), nullable=False),
    sa.Column('trade_id', sa.UUID(), nullable=True),
    sa.Column('change_type', sa.String(length=20), nullable=False, comment='变动类型'),
    sa.Column('amount', sa.Numeric(precision=15, scale=2), nullable=False, comment='变动金额'),
    sa.Column('balance_before', sa.Numeric(precision=15, scale=2), nullable=False, comment='变动前余额'),
    sa.Column('balance_after', sa.Numeric(precision=15, scale=2), nullable=False, comment='变动后余额'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='变动描述'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['trade_id'], ['trades.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_asset_records_account_id', 'asset_records', ['account_id'], unique=False)
    op.create_index('idx_asset_records_change_type', 'asset_records', ['change_type'], unique=False)
    op.create_index('idx_asset_records_created_at', 'asset_records', ['created_at'], unique=False)
    op.create_index(op.f('ix_asset_records_account_id'), 'asset_records', ['account_id'], unique=False)
    op.create_index(op.f('ix_asset_records_trade_id'), 'asset_records', ['trade_id'], unique=False)
    op.create_table('ctp_trades',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('trade_id_ref', sa.UUID(), nullable=True, comment='关联的统一成交ID'),
    sa.Column('ctp_order_id', sa.UUID(), nullable=True),
    sa.Column('trade_id', sa.String(length=21), nullable=False, comment='成交编号'),
    sa.Column('order_ref', sa.String(length=13), nullable=False, comment='报单引用'),
    sa.Column('order_sys_id', sa.String(length=21), nullable=True, comment='报单编号'),
    sa.Column('instrument_id', sa.String(length=31), nullable=False, comment='合约代码'),
    sa.Column('exchange_id', sa.String(length=9), nullable=True, comment='交易所代码'),
    sa.Column('direction', sa.Enum('BUY', 'SELL', name='ctpdirection'), nullable=False, comment='买卖方向'),
    sa.Column('offset_flag', sa.Enum('OPEN', 'CLOSE', 'FORCE_CLOSE', 'CLOSE_TODAY', 'CLOSE_YESTERDAY', 'FORCE_OFF', 'LOCAL_FORCE_CLOSE', name='ctpoffsetflag'), nullable=False, comment='开平标志'),
    sa.Column('price', sa.Numeric(precision=15, scale=4), nullable=False, comment='价格'),
    sa.Column('volume', sa.Integer(), nullable=False, comment='数量'),
    sa.Column('trade_date', sa.String(length=9), nullable=True, comment='成交日期'),
    sa.Column('trade_time', sa.String(length=9), nullable=True, comment='成交时间'),
    sa.Column('trading_day', sa.String(length=9), nullable=True, comment='交易日'),
    sa.Column('trader_id', sa.String(length=21), nullable=True, comment='交易所交易员代码'),
    sa.Column('order_local_id', sa.String(length=13), nullable=True, comment='本地报单编号'),
    sa.Column('clearing_part_id', sa.String(length=11), nullable=True, comment='结算会员编号'),
    sa.Column('business_unit', sa.String(length=21), nullable=True, comment='业务单元'),
    sa.Column('sequence_no', sa.Integer(), nullable=True, comment='序号'),
    sa.Column('settlement_id', sa.Integer(), nullable=True, comment='结算编号'),
    sa.Column('broker_order_seq', sa.Integer(), nullable=True, comment='经纪公司报单编号'),
    sa.Column('trade_source', sa.String(length=1), nullable=True, comment='成交来源'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['ctp_order_id'], ['ctp_orders.id'], ),
    sa.ForeignKeyConstraint(['trade_id_ref'], ['trades.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('trade_id')
    )
    op.create_index('idx_ctp_trades_instrument_id', 'ctp_trades', ['instrument_id'], unique=False)
    op.create_index('idx_ctp_trades_order_ref', 'ctp_trades', ['order_ref'], unique=False)
    op.create_index('idx_ctp_trades_trade_id', 'ctp_trades', ['trade_id'], unique=False)
    op.create_index('idx_ctp_trades_user_id', 'ctp_trades', ['user_id'], unique=False)
    op.create_index(op.f('ix_ctp_trades_ctp_order_id'), 'ctp_trades', ['ctp_order_id'], unique=False)
    op.create_index(op.f('ix_ctp_trades_instrument_id'), 'ctp_trades', ['instrument_id'], unique=False)
    op.create_index(op.f('ix_ctp_trades_trade_id_ref'), 'ctp_trades', ['trade_id_ref'], unique=False)
    op.create_index(op.f('ix_ctp_trades_user_id'), 'ctp_trades', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ctp_trades_user_id'), table_name='ctp_trades')
    op.drop_index(op.f('ix_ctp_trades_trade_id_ref'), table_name='ctp_trades')
    op.drop_index(op.f('ix_ctp_trades_instrument_id'), table_name='ctp_trades')
    op.drop_index(op.f('ix_ctp_trades_ctp_order_id'), table_name='ctp_trades')
    op.drop_index('idx_ctp_trades_user_id', table_name='ctp_trades')
    op.drop_index('idx_ctp_trades_trade_id', table_name='ctp_trades')
    op.drop_index('idx_ctp_trades_order_ref', table_name='ctp_trades')
    op.drop_index('idx_ctp_trades_instrument_id', table_name='ctp_trades')
    op.drop_table('ctp_trades')
    op.drop_index(op.f('ix_asset_records_trade_id'), table_name='asset_records')
    op.drop_index(op.f('ix_asset_records_account_id'), table_name='asset_records')
    op.drop_index('idx_asset_records_created_at', table_name='asset_records')
    op.drop_index('idx_asset_records_change_type', table_name='asset_records')
    op.drop_index('idx_asset_records_account_id', table_name='asset_records')
    op.drop_table('asset_records')
    op.drop_index(op.f('ix_trades_user_id'), table_name='trades')
    op.drop_index(op.f('ix_trades_trade_id'), table_name='trades')
    op.drop_index(op.f('ix_trades_symbol'), table_name='trades')
    op.drop_index(op.f('ix_trades_order_id'), table_name='trades')
    op.drop_index(op.f('ix_trades_account_id'), table_name='trades')
    op.drop_index('idx_trades_user_id', table_name='trades')
    op.drop_index('idx_trades_trade_time', table_name='trades')
    op.drop_index('idx_trades_symbol', table_name='trades')
    op.drop_index('idx_trades_order_id', table_name='trades')
    op.drop_index('idx_trades_account_id', table_name='trades')
    op.drop_table('trades')
    op.drop_index(op.f('ix_ctp_positions_user_id'), table_name='ctp_positions')
    op.drop_index(op.f('ix_ctp_positions_position_id'), table_name='ctp_positions')
    op.drop_index(op.f('ix_ctp_positions_instrument_id'), table_name='ctp_positions')
    op.drop_index('idx_ctp_positions_user_id', table_name='ctp_positions')
    op.drop_index('idx_ctp_positions_instrument_id', table_name='ctp_positions')
    op.drop_index('idx_ctp_positions_direction', table_name='ctp_positions')
    op.drop_table('ctp_positions')
    op.drop_index(op.f('ix_ctp_orders_user_id'), table_name='ctp_orders')
    op.drop_index(op.f('ix_ctp_orders_order_status'), table_name='ctp_orders')
    op.drop_index(op.f('ix_ctp_orders_order_id'), table_name='ctp_orders')
    op.drop_index(op.f('ix_ctp_orders_instrument_id'), table_name='ctp_orders')
    op.drop_index('idx_ctp_orders_user_id', table_name='ctp_orders')
    op.drop_index('idx_ctp_orders_status', table_name='ctp_orders')
    op.drop_index('idx_ctp_orders_order_sys_id', table_name='ctp_orders')
    op.drop_index('idx_ctp_orders_order_ref', table_name='ctp_orders')
    op.drop_index('idx_ctp_orders_instrument_id', table_name='ctp_orders')
    op.drop_table('ctp_orders')
    op.drop_index(op.f('ix_strategy_performance_strategy_id'), table_name='strategy_performance')
    op.drop_index(op.f('ix_strategy_performance_record_date'), table_name='strategy_performance')
    op.drop_index('idx_strategy_performance_strategy_id', table_name='strategy_performance')
    op.drop_index('idx_strategy_performance_strategy_date', table_name='strategy_performance')
    op.drop_index('idx_strategy_performance_record_date', table_name='strategy_performance')
    op.drop_table('strategy_performance')
    op.drop_index(op.f('ix_positions_user_id'), table_name='positions')
    op.drop_index(op.f('ix_positions_symbol'), table_name='positions')
    op.drop_index(op.f('ix_positions_strategy_id'), table_name='positions')
    op.drop_index(op.f('ix_positions_account_id'), table_name='positions')
    op.drop_index('idx_positions_user_id', table_name='positions')
    op.drop_index('idx_positions_symbol', table_name='positions')
    op.drop_index('idx_positions_strategy_symbol', table_name='positions')
    op.drop_index('idx_positions_account_symbol_unique', table_name='positions')
    op.drop_index('idx_positions_account_id', table_name='positions')
    op.drop_table('positions')
    op.drop_index(op.f('ix_orders_user_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_symbol'), table_name='orders')
    op.drop_index(op.f('ix_orders_strategy_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_status'), table_name='orders')
    op.drop_index(op.f('ix_orders_order_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_account_id'), table_name='orders')
    op.drop_index('idx_orders_user_id', table_name='orders')
    op.drop_index('idx_orders_symbol', table_name='orders')
    op.drop_index('idx_orders_submit_time', table_name='orders')
    op.drop_index('idx_orders_strategy_symbol', table_name='orders')
    op.drop_index('idx_orders_status', table_name='orders')
    op.drop_index('idx_orders_account_id', table_name='orders')
    op.drop_table('orders')
    op.drop_index(op.f('ix_ctp_accounts_user_id'), table_name='ctp_accounts')
    op.drop_index(op.f('ix_ctp_accounts_account_id'), table_name='ctp_accounts')
    op.drop_index('idx_ctp_accounts_user_id', table_name='ctp_accounts')
    op.drop_index('idx_ctp_accounts_investor_id', table_name='ctp_accounts')
    op.drop_index('idx_ctp_accounts_broker_id', table_name='ctp_accounts')
    op.drop_table('ctp_accounts')
    op.drop_index(op.f('ix_backtests_user_id'), table_name='backtests')
    op.drop_index(op.f('ix_backtests_strategy_id'), table_name='backtests')
    op.drop_index(op.f('ix_backtests_status'), table_name='backtests')
    op.drop_index('idx_backtests_user_id', table_name='backtests')
    op.drop_index('idx_backtests_strategy_id', table_name='backtests')
    op.drop_index('idx_backtests_status', table_name='backtests')
    op.drop_index('idx_backtests_start_date', table_name='backtests')
    op.drop_index('idx_backtests_created_at', table_name='backtests')
    op.drop_table('backtests')
    op.drop_index(op.f('ix_account_history_record_date'), table_name='account_history')
    op.drop_index(op.f('ix_account_history_account_id'), table_name='account_history')
    op.drop_index('idx_account_history_record_date', table_name='account_history')
    op.drop_index('idx_account_history_account_id', table_name='account_history')
    op.drop_index('idx_account_history_account_date', table_name='account_history')
    op.drop_table('account_history')
    op.drop_table('user_settings')
    op.drop_table('user_roles')
    op.drop_index('idx_user_role_assignments_user_id', table_name='user_role_assignments')
    op.drop_index('idx_user_role_assignments_role_id', table_name='user_role_assignments')
    op.drop_index('idx_user_role_assignments_active', table_name='user_role_assignments')
    op.drop_table('user_role_assignments')
    op.drop_table('user_profiles')
    op.drop_index(op.f('ix_strategies_user_id'), table_name='strategies')
    op.drop_index(op.f('ix_strategies_status'), table_name='strategies')
    op.drop_index('idx_strategies_user_id', table_name='strategies')
    op.drop_index('idx_strategies_type', table_name='strategies')
    op.drop_index('idx_strategies_status', table_name='strategies')
    op.drop_index('idx_strategies_created_at', table_name='strategies')
    op.drop_index('idx_strategies_active', table_name='strategies')
    op.drop_table('strategies')
    op.drop_table('role_permissions')
    op.drop_index(op.f('ix_market_ticks_trading_date'), table_name='market_ticks')
    op.drop_index(op.f('ix_market_ticks_timestamp'), table_name='market_ticks')
    op.drop_index(op.f('ix_market_ticks_symbol'), table_name='market_ticks')
    op.drop_index(op.f('ix_market_ticks_instrument_id'), table_name='market_ticks')
    op.drop_index('idx_market_ticks_trading_date', table_name='market_ticks')
    op.drop_index('idx_market_ticks_timestamp', table_name='market_ticks')
    op.drop_index('idx_market_ticks_symbol_time', table_name='market_ticks')
    op.drop_index('idx_market_ticks_symbol', table_name='market_ticks')
    op.drop_index('idx_market_ticks_instrument_time', table_name='market_ticks')
    op.drop_index('idx_market_ticks_instrument_id', table_name='market_ticks')
    op.drop_table('market_ticks')
    op.drop_index(op.f('ix_market_klines_trading_date'), table_name='market_klines')
    op.drop_index(op.f('ix_market_klines_timestamp'), table_name='market_klines')
    op.drop_index(op.f('ix_market_klines_symbol'), table_name='market_klines')
    op.drop_index(op.f('ix_market_klines_instrument_id'), table_name='market_klines')
    op.drop_index(op.f('ix_market_klines_frequency'), table_name='market_klines')
    op.drop_index('idx_market_klines_unique', table_name='market_klines')
    op.drop_index('idx_market_klines_trading_date', table_name='market_klines')
    op.drop_index('idx_market_klines_timestamp', table_name='market_klines')
    op.drop_index('idx_market_klines_symbol_freq_time', table_name='market_klines')
    op.drop_index('idx_market_klines_symbol', table_name='market_klines')
    op.drop_index('idx_market_klines_instrument_id', table_name='market_klines')
    op.drop_index('idx_market_klines_instrument_freq_time', table_name='market_klines')
    op.drop_index('idx_market_klines_frequency', table_name='market_klines')
    op.drop_table('market_klines')
    op.drop_index('idx_accounts_user_id', table_name='accounts')
    op.drop_index('idx_accounts_status', table_name='accounts')
    op.drop_index('idx_accounts_account_id', table_name='accounts')
    op.drop_table('accounts')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_phone'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index('idx_users_username', table_name='users')
    op.drop_index('idx_users_status', table_name='users')
    op.drop_index('idx_users_email', table_name='users')
    op.drop_index('idx_users_created_at', table_name='users')
    op.drop_table('users')
    op.drop_index('idx_roles_name', table_name='roles')
    op.drop_index('idx_roles_is_active', table_name='roles')
    op.drop_table('roles')
    op.drop_index('idx_permissions_resource_action', table_name='permissions')
    op.drop_index('idx_permissions_name', table_name='permissions')
    op.drop_index('idx_permissions_category', table_name='permissions')
    op.drop_table('permissions')
    op.drop_index(op.f('ix_market_depth_timestamp'), table_name='market_depth')
    op.drop_index(op.f('ix_market_depth_symbol'), table_name='market_depth')
    op.drop_index('idx_market_depth_timestamp', table_name='market_depth')
    op.drop_index('idx_market_depth_symbol_time', table_name='market_depth')
    op.drop_index('idx_market_depth_symbol', table_name='market_depth')
    op.drop_table('market_depth')
    op.drop_index(op.f('ix_instruments_symbol'), table_name='instruments')
    op.drop_index(op.f('ix_instruments_instrument_type'), table_name='instruments')
    op.drop_index(op.f('ix_instruments_exchange'), table_name='instruments')
    op.drop_index('idx_instruments_type', table_name='instruments')
    op.drop_index('idx_instruments_symbol', table_name='instruments')
    op.drop_index('idx_instruments_exchange', table_name='instruments')
    op.drop_index('idx_instruments_active', table_name='instruments')
    op.drop_table('instruments')
    # ### end Alembic commands ###