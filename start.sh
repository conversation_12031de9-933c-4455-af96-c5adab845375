#!/bin/bash
# 量化投资平台 - Linux/macOS 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 默认配置
ENVIRONMENT="development"
SERVICES="all"
USE_DOCKER=false
CHECK_ONLY=false

# 显示帮助信息
show_help() {
    cat << EOF
量化投资平台启动脚本 (Linux/macOS)

用法: $0 [选项]

选项:
    -e, --env ENV        运行环境 (development|production|testing) [默认: development]
    -s, --services SVC   要启动的服务 (backend|frontend|all) [默认: all]
    -d, --docker         使用 Docker 启动
    -c, --check          仅检查依赖，不启动服务
    -h, --help           显示此帮助信息
    -v, --verbose        详细输出

示例:
    $0                           # 启动开发环境（所有服务）
    $0 -e production            # 启动生产环境
    $0 -s backend              # 仅启动后端服务
    $0 -d                      # 使用 Docker 启动
    $0 -c                      # 检查依赖

EOF
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -s|--services)
                SERVICES="$2"
                shift 2
                ;;
            -d|--docker)
                USE_DOCKER=true
                shift
                ;;
            -c|--check)
                CHECK_ONLY=true
                shift
                ;;
            -v|--verbose)
                set -x
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查系统依赖
check_system_deps() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查基本工具
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if ! command -v git &> /dev/null; then
        missing_deps+=("git")
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # 检查 pip
    if ! command -v pip3 &> /dev/null; then
        missing_deps+=("python3-pip")
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=("nodejs")
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    # 如果使用 Docker，检查 Docker
    if [ "$USE_DOCKER" = true ]; then
        if ! command -v docker &> /dev/null; then
            missing_deps+=("docker")
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            missing_deps+=("docker-compose")
        fi
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少以下依赖："
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        log_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 检查项目依赖
check_project_deps() {
    log_info "检查项目依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 检查后端依赖
    if [[ "$SERVICES" == "all" || "$SERVICES" == "backend" ]]; then
        log_info "检查后端依赖..."
        if [ ! -f "backend/requirements.txt" ]; then
            log_warning "backend/requirements.txt 不存在"
        else
            cd backend
            log_info "安装 Python 依赖..."
            pip3 install -r requirements.txt
            cd ..
        fi
    fi
    
    # 检查前端依赖
    if [[ "$SERVICES" == "all" || "$SERVICES" == "frontend" ]]; then
        log_info "检查前端依赖..."
        if [ ! -f "frontend/package.json" ]; then
            log_warning "frontend/package.json 不存在"
        else
            cd frontend
            log_info "安装 Node.js 依赖..."
            npm install
            cd ..
        fi
    fi
    
    log_success "项目依赖检查完成"
}

# 启动服务
start_services() {
    log_info "启动服务 (环境: $ENVIRONMENT, 服务: $SERVICES)..."
    
    cd "$PROJECT_ROOT"
    
    # 创建必要的目录
    mkdir -p data logs
    
    # 设置环境变量
    export ENVIRONMENT="$ENVIRONMENT"
    export DATABASE_URL="sqlite:///./data/quant.db"
    export REDIS_URL="redis://localhost:6379/0"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        export DATABASE_URL="****************************************************/quant_db"
    fi
    
    # 启动后端
    if [[ "$SERVICES" == "all" || "$SERVICES" == "backend" ]]; then
        log_info "启动后端服务..."
        cd backend
        nohup python3 app/main.py > ../logs/backend.log 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > ../logs/backend.pid
        log_success "后端服务已启动 (PID: $BACKEND_PID)"
        cd ..
        
        # 等待后端启动
        log_info "等待后端服务就绪..."
        for i in {1..30}; do
            if curl -s http://localhost:8000/health > /dev/null; then
                log_success "后端服务就绪"
                break
            fi
            sleep 2
            if [ $i -eq 30 ]; then
                log_warning "后端服务启动超时"
            fi
        done
    fi
    
    # 启动前端
    if [[ "$SERVICES" == "all" || "$SERVICES" == "frontend" ]]; then
        log_info "启动前端服务..."
        cd frontend
        export VITE_API_URL="http://localhost:8000"
        nohup npm run dev -- --host 0.0.0.0 > ../logs/frontend.log 2>&1 &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > ../logs/frontend.pid
        log_success "前端服务已启动 (PID: $FRONTEND_PID)"
        cd ..
        
        # 等待前端启动
        log_info "等待前端服务就绪..."
        for i in {1..30}; do
            if curl -s http://localhost:5173 > /dev/null; then
                log_success "前端服务就绪"
                break
            fi
            sleep 2
            if [ $i -eq 30 ]; then
                log_warning "前端服务启动超时"
            fi
        done
    fi
    
    # 显示服务状态
    echo
    log_success "🎉 平台启动完成!"
    echo "=================================="
    echo "前端地址: http://localhost:5173"
    echo "后端API: http://localhost:8000"
    echo "环境: $ENVIRONMENT"
    echo "日志目录: $PROJECT_ROOT/logs"
    echo "=================================="
    
    # 自动打开浏览器 (仅在桌面环境)
    if [ "$ENVIRONMENT" = "development" ] && [ -n "$DISPLAY" ]; then
        log_info "打开浏览器..."
        if command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:5173 &
        elif command -v open &> /dev/null; then  # macOS
            open http://localhost:5173 &
        fi
    fi
}

# 使用 Docker 启动
start_with_docker() {
    log_info "使用 Docker 启动平台..."
    
    cd "$PROJECT_ROOT"
    
    local compose_file="docker-compose.yml"
    if [ "$ENVIRONMENT" = "production" ]; then
        compose_file="docker-compose.prod.yml"
    fi
    
    if [ ! -f "$compose_file" ]; then
        log_error "Docker Compose 文件不存在: $compose_file"
        exit 1
    fi
    
    log_info "构建并启动容器..."
    docker-compose -f "$compose_file" up --build -d
    
    log_info "等待服务就绪..."
    sleep 10
    
    # 检查服务状态
    docker-compose -f "$compose_file" ps
    
    log_success "🐳 Docker 平台启动完成!"
    echo "=================================="
    echo "前端地址: http://localhost:5173"
    echo "后端API: http://localhost:8000"
    echo "环境: $ENVIRONMENT"
    echo "容器状态: docker-compose ps"
    echo "=================================="
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    cd "$PROJECT_ROOT"
    
    # 停止进程
    if [ -f "logs/backend.pid" ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if kill -0 "$BACKEND_PID" 2>/dev/null; then
            kill "$BACKEND_PID"
            log_success "后端服务已停止"
        fi
        rm -f logs/backend.pid
    fi
    
    if [ -f "logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if kill -0 "$FRONTEND_PID" 2>/dev/null; then
            kill "$FRONTEND_PID"
            log_success "前端服务已停止"
        fi
        rm -f logs/frontend.pid
    fi
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    stop_services
    exit 0
}

# 主函数
main() {
    # 解析参数
    parse_args "$@"
    
    # 检查系统依赖
    check_system_deps
    
    # 如果只是检查依赖
    if [ "$CHECK_ONLY" = true ]; then
        check_project_deps
        log_success "依赖检查完成"
        exit 0
    fi
    
    # 设置信号处理
    trap cleanup SIGINT SIGTERM
    
    # 检查项目依赖
    check_project_deps
    
    # 启动服务
    if [ "$USE_DOCKER" = true ]; then
        start_with_docker
    else
        start_services
        
        # 等待用户中断
        log_info "按 Ctrl+C 停止服务"
        while true; do
            sleep 1
        done
    fi
}

# 运行主函数
main "$@"